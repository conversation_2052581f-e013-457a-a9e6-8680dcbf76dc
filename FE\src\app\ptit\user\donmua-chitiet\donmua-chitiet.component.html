<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0">Chi tiết đơn hàng</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <div class="row">
        <div class="col-xl-9">
          <div class="card">
            <div class="card-header">
              <div class="d-flex align-items-center">
                <h5 class="card-title flex-grow-1 mb-0">
                  Hóa đơn #{{ donhang.id }}
                </h5>
                <!-- <div class="flex-shrink-0">
                  <a class="btn btn-success btn-sm"
                    ><i class="ri-download-2-fill align-middle me-1"></i> <PERSON><PERSON><PERSON>
                    đơn</a
                  >
                </div> -->
              </div>
            </div>
            <div class="card-body">
              <div class="table-responsive table-card">
                <table
                  class="table table-nowrap align-middle table-borderless mb-0"
                >
                  <thead class="table-light text-muted">
                    <tr>
                      <th scope="col">Chi tiết thuốc</th>
                      <th scope="col">Giá</th>
                      <th scope="col">Số lượng</th>
                      <th scope="col" class="text-end">Tổng</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of donhang.chiTietDonHangs">
                      <td>
                        <div class="d-flex">
                          <div
                            (click)="showDetail(item.thuoc)"
                            class="flex-shrink-0 avatar-md bg-light rounded p-1 cursor-pointer"
                          >
                            <img
                              [src]="item.thuoc?.avatar"
                              alt=""
                              class="img-fluid d-block"
                            />
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <h5 class="fs-15">
                              <a
                                [routerLink]="[
                                  '/sys/product-create',
                                  item.thuoc?.id
                                ]"
                                class="link-primary"
                                >{{ item.thuoc?.tenThuoc }}</a
                              >
                            </h5>
                            <p class="text-muted mb-0">
                              Loại thuốc:
                              <span class="fw-medium">{{
                                item.thuoc?.loaiThuoc?.tenLoai
                              }}</span>
                            </p>
                            <!-- <p class="text-muted mb-0">
                                Size: <span class="fw-medium">M</span>
                              </p> -->
                          </div>
                        </div>
                      </td>
                      <td>{{ item.donGia }}</td>
                      <td>{{ item.soLuong }}</td>

                      <td class="fw-medium text-end">
                        {{
                          (item.donGia || 0) * (item.soLuong || 0)
                            | currency : "VND" : "symbol"
                        }}
                      </td>
                    </tr>

                    <tr class="border-top border-top-dashed">
                      <td colspan="3"></td>
                      <td colspan="2" class="fw-medium p-0">
                        <table class="table table-borderless mb-0">
                          <tbody>
                            <tr>
                              <td>Tổng :</td>
                              <td class="text-end">{{ tongTien }}</td>
                            </tr>
                            <tr>
                              <td>
                                Giảm giá
                                <!-- <span class="text-muted">0</span> : : -->
                              </td>
                              <td class="text-end">-0</td>
                            </tr>
                            <tr>
                              <td>Phí vận chuyển :</td>
                              <td class="text-end">0</td>
                            </tr>

                            <tr class="border-top border-top-dashed">
                              <th scope="row">Tổng tiền :</th>
                              <th class="text-end">
                                {{ tongTien | number : "1.0-0" }}
                              </th>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <!--end card-->
          <div class="card">
            <div class="card-header">
              <div class="d-sm-flex align-items-center">
                <h5 class="card-title flex-grow-1 mb-0">Trạng thái đơn hàng</h5>
              </div>
            </div>
            <div class="card-body">
              <div class="profile-timeline">
                <div
                  class="accordion accordion-flush"
                  id="accordionFlushExample"
                >
                  <div class="accordion-item border-0">
                    <div
                      class="accordion-header"
                      id="headingOne"
                      *ngIf="donhang.createdAt"
                    >
                      <a
                        class="accordion-button p-2 shadow-none"
                        data-bs-toggle="collapse"
                        href="#collapseOne"
                        aria-expanded="true"
                        aria-controls="collapseOne"
                      >
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0 avatar-xs">
                            <div
                              class="avatar-title bg-success rounded-circle shadow"
                            >
                              <i class="ri-shopping-bag-line"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <h6 class="fs-15 mb-0 fw-semibold">
                              Ngày đặt hàng -
                              <span class="fw-normal">{{
                                donhang.createdAt | date : "dd-MM-yyyy"
                              }}</span>
                            </h6>
                          </div>
                        </div>
                      </a>
                    </div>
                    <div
                      id="collapseOne"
                      class="accordion-collapse collapse show"
                      aria-labelledby="headingOne"
                      data-bs-parent="#accordionExample"
                    >
                      <div class="accordion-body ms-2 ps-5 pt-0">
                        <h6 class="mb-1">Đơn hàng đã được đặt.</h6>
                        <p class="text-muted">
                          {{ donhang.createdAt | date : "dd-MM-yyyy HH:mm" }}
                        </p>

                        <h6
                          *ngIf="
                            donhang.trangThaiGiaoHang ==
                              TrangThaiGiaoHang.DANG_GIAO ||
                            donhang.trangThaiGiaoHang ==
                              TrangThaiGiaoHang.DA_GIAO
                          "
                          class="mb-1"
                        >
                          Người bán hàng đã xác nhận đơn.
                        </h6>
                        <!-- <p class="text-muted mb-0">Thu, 16 Dec 2021 - 5:48AM</p> -->
                      </div>
                    </div>
                  </div>
                  <!-- <div class="accordion-item border-0">
                      <div class="accordion-header" id="headingTwo">
                        <a
                          class="accordion-button p-2 shadow-none"
                          data-bs-toggle="collapse"
                          href="#collapseTwo"
                          aria-expanded="false"
                          aria-controls="collapseTwo"
                        >
                          <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 avatar-xs">
                              <div
                                class="avatar-title bg-success rounded-circle shadow"
                              >
                                <i class="mdi mdi-gift-outline"></i>
                              </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                              <h6 class="fs-15 mb-1 fw-semibold">
                                Packed -
                                <span class="fw-normal">Thu, 16 Dec 2021</span>
                              </h6>
                            </div>
                          </div>
                        </a>
                      </div>
                      <div
                        id="collapseTwo"
                        class="accordion-collapse collapse show"
                        aria-labelledby="headingTwo"
                        data-bs-parent="#accordionExample"
                      >
                        <div class="accordion-body ms-2 ps-5 pt-0">
                          <h6 class="mb-1">
                            Your Item has been picked up by courier patner
                          </h6>
                          <p class="text-muted mb-0">Fri, 17 Dec 2021 - 9:45AM</p>
                        </div>
                      </div>
                    </div> -->
                  <div
                    class="accordion-item border-0"
                    *ngIf="
                      donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DANG_GIAO
                    "
                  >
                    <div class="accordion-header" id="headingThree">
                      <a
                        class="accordion-button p-2 shadow-none"
                        data-bs-toggle="collapse"
                        href="#collapseThree"
                        aria-expanded="false"
                        aria-controls="collapseThree"
                      >
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0 avatar-xs">
                            <div
                              class="avatar-title bg-success rounded-circle shadow"
                            >
                              <i class="ri-truck-line"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <h6 class="fs-15 mb-1 fw-semibold">
                              Đang vận chuyển
                              <!-- <span class="fw-normal">Thu, 16 Dec 2021</span> -->
                            </h6>
                          </div>
                        </div>
                      </a>
                    </div>
                    <div
                      id="collapseThree"
                      class="accordion-collapse collapse show"
                      aria-labelledby="headingThree"
                      data-bs-parent="#accordionExample"
                    >
                      <div class="accordion-body ms-2 ps-5 pt-0">
                        <!-- <h6 class="fs-14">RQK Logistics - MFDS1400457854</h6> -->
                        <h6 class="mb-1">
                          Đon hàng của bạn đã được vẫn chuyển.
                        </h6>
                        <!-- <p class="text-muted mb-0">Sat, 18 Dec 2021 - 4.54PM</p> -->
                      </div>
                    </div>
                  </div>
                  <div
                    class="accordion-item border-0"
                    *ngIf="
                      donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DA_GIAO
                    "
                  >
                    <div class="accordion-header" id="headingFour">
                      <a
                        class="accordion-button p-2 shadow-none"
                        data-bs-toggle="collapse"
                        href="#collapseFour"
                        aria-expanded="false"
                      >
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0 avatar-xs">
                            <div
                              class="avatar-title bg-light text-success rounded-circle shadow"
                            >
                              <i class="ri-takeaway-fill"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <h6 class="fs-14 mb-0 fw-semibold">
                              Đã giao thành công
                            </h6>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>

                  <div
                    class="accordion-item border-0"
                    *ngIf="
                      donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DA_HUY
                    "
                  >
                    <div class="accordion-header" id="headingFour">
                      <a
                        class="accordion-button p-2 shadow-none"
                        data-bs-toggle="collapse"
                        href="#collapseFour"
                        aria-expanded="false"
                      >
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0 avatar-xs">
                            <div
                              class="avatar-title bg-danger rounded-circle shadow"
                            >
                              <i class="ri-truck-line"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <h6 class="fs-14 mb-0 fw-semibold">Đã hủy hàng</h6>
                          </div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
                <!--end accordion-->
              </div>
            </div>
          </div>
          <!--end card-->
        </div>
        <!--end col-->
        <div class="col-xl-3">
          <div class="card">
            <div class="card-header">
              <div class="d-flex">
                <h5 class="card-title flex-grow-1 mb-0">Chi tiết khách hàng</h5>
                <!-- <div class="flex-shrink-0">
                  <a href="javascript:void(0);" class="link-secondary"
                    >Xem hồ sơ</a
                  >
                </div> -->
              </div>
            </div>
            <div class="card-body">
              <ul class="list-unstyled mb-0 vstack gap-3">
                <li>
                  <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                      <img
                        [src]="donhang.khachHang?.avatar"
                        class="avatar-sm rounded shadow"
                      />
                    </div>
                    <div class="flex-grow-1 ms-3">
                      <h6 class="fs-14 mb-1">{{ donhang.khachHang?.hoTen }}</h6>
                      <p class="text-muted mb-0">Khách hàng</p>
                    </div>
                  </div>
                </li>
                <li>
                  <i
                    class="ri-mail-line me-2 align-middle text-muted fs-16"
                  ></i>
                  {{ donhang.khachHang?.email }}
                </li>
                <li>
                  <i
                    class="ri-phone-line me-2 align-middle text-muted fs-16"
                  ></i
                  >{{ donhang.khachHang?.soDienThoai }}
                </li>
              </ul>
            </div>
          </div>
          <!--end card-->
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="ri-map-pin-line align-middle me-1 text-muted"></i>
                Địa chỉ nhận hàng
              </h5>
            </div>
            <div class="card-body">
              <ul class="list-unstyled vstack gap-2 fs-13 mb-0">
                <li class="fw-medium fs-14">{{ donhang.tenKhachHang }}</li>
                <li>{{ donhang.soDienThoai }}</li>
                <li>{{ donhang.diaChi }}</li>
              </ul>
            </div>
          </div>
          <!--end card-->

          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i
                  class="ri-secure-payment-line align-bottom me-1 text-muted"
                ></i>
                Chi tiết thanh toán
              </h5>
            </div>
            <div class="card-body">
              <div class="d-flex align-items-center mb-2">
                <div class="flex-shrink-0">
                  <p class="text-muted mb-0">Phương thức:</p>
                </div>
                <div class="flex-grow-1 ms-2">
                  <h6 class="mb-0">Tiền mặt</h6>
                </div>
              </div>
              <!-- <div class="d-flex align-items-center mb-2">
                  <div class="flex-shrink-0">
                    <p class="text-muted mb-0">Card Holder Name:</p>
                  </div>
                  <div class="flex-grow-1 ms-2">
                    <h6 class="mb-0">Joseph Parker</h6>
                  </div>
                </div>
                <div class="d-flex align-items-center mb-2">
                  <div class="flex-shrink-0">
                    <p class="text-muted mb-0">Card Number:</p>
                  </div>
                  <div class="flex-grow-1 ms-2">
                    <h6 class="mb-0">xxxx xxxx xxxx 2456</h6>
                  </div>
                </div> -->
              <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                  <p class="text-muted mb-0">Tổng tiền:</p>
                </div>
                <div class="flex-grow-1 ms-2">
                  <h6 class="mb-0">{{ tongTien | number : "1.0-0" }} VNĐ</h6>
                </div>
              </div>
            </div>
          </div>
          <!--end card-->
        </div>
        <!--end col-->
      </div>
      <!--end row-->
    </div>
    <!-- container-fluid -->
  </div>
  <!-- End Page-content -->
</div>
