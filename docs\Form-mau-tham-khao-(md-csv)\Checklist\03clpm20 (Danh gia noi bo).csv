,Checklist <PERSON><PERSON><PERSON> giá nội bộ,,,,
,,,,,
,<PERSON><PERSON> hiệu dự án: ,,,,
,Ng<PERSON><PERSON>i đánh giá: ,,,,
,Đ<PERSON>i tượng đánh giá: ,,,,
,<PERSON><PERSON><PERSON> đ<PERSON>h giá:,,,,
,Thời gian thực hiện (person-hour): ,,,,
,,,,,
STT,Câu hỏi,Yes,No,N/A,<PERSON>hi chú
,02-QT/PM/HDCV - <PERSON><PERSON><PERSON> đ<PERSON>nh YC NSD,,,,
1,<PERSON><PERSON> kế hoạch chi tiết giai đoạn Khảo sát và Phân tích không ?,,,,
2,<PERSON><PERSON> hoạch khảo sát và phân tích có được thông qua không ?,,,,
3,<PERSON><PERSON> mụ<PERSON> các vấn đề cần khảo sát không ?,,,,
4,<PERSON><PERSON> bản khảo sát không ?,,,,
5,<PERSON><PERSON><PERSON> khảo sát không ?,,,,
6,<PERSON><PERSON> thực hiện xem xét báo cáo khảo sát không? Bằngchứng?,,,,
7,<PERSON><PERSON> tài liệu phân tích nghiệp vụ không ?,,,,
8,Tài liệu phân tích nghiệp vụ có được xem xét không ? Bằng chứng ? ,,,,
9,Có tài liệu phân tích YC-NSD không ?,,,,
10,Tài liệu phân tích YC-NSD có được xem xét không ? Bằng chứng?,,,,
11,Có bản theo dõi liên kết các sản phẩm (CI Register) không ?,,,,
12,Có tài liệu mô tả hoạt động hệ thống không ?,,,,
13,Tài liệu mô tả hoạt động hệ thống có được xem xét không ?,,,,
14,Có tài liệu Đặc tả yêu cầu phần mềm không ?,,,,
15,Tài liệu Đặc tả yêu cầu phần mềm có được xem xét không ?,,,,
16,Có tiêu chí và điều kiện nghiệm thu không ?,,,,
17,Có biên bản bàn giao kết quả của quá trình Xác định YC-NSD cho khách hàng không ?,,,,
18,Các sản phẩm có được thực hiện kiểm tra cuối cùng bởi PdQA trước khi bàn giao cho khách hàng không?,,,,
19,Có thực hiện phân tích chỉ tiêu không đạt của quá trình Xác định YC-NSD không ?,,,,
20,Có tiến hành quản lý tình trạng các yêu cầu không? ,,,,
21,Có sử dụng tool để quản lý yêu cầu không? Tool đó có được xác định trong KHDA không?,,,,
22,"Có sử dụng checklist để xem xét các sản phẩm không? (URD, SRS)",,,,
,03-QT/PM/HDCV - Thiết kế,,,,
23,Có kế hoạch chi tiết giai đoạn Thiết kế không ?,,,,
24,Kế hoạch thiết kế có được thông qua không ?,,,,
25,Có quy định chuẩn thiết kế không ?,,,,
26,Các quy định chuẩn thiết kế (nếu có) có được xem xét không ?,,,,
27,Chuẩn thiết kế có là CI không ?,,,,
28,Có thực hiện đánh giá các giải pháp và lựa chọn giải pháp phù hợp trước khi thiết kế tổng thể không?,,,,
29,Có tài liệu thiết kế tổng thể không ?,,,,
30,Tài liệu thiết kế tổng thể có được xem xét và thông qua không ?,,,,
31,Có tài liệu thiết kế chi tiết không ?,,,,
32,Tài liệu thiết kế chi tiết có được xem xét và thông qua không ?,,,,
33,Việc xem xét các sản phẩm của quá trình thiết kế có được lên kế hoạch và thông báo cho những người liên quan không?,,,,
34,Các checklist có được dùng để xem xét các tài liệu thiết kế không?,,,,
35,Các sản phẩm của quá trình thiết có được PdQA kiểm tra cuối cùng không?,,,,
36,Có biên bản bàn giao tài liệu thiết kế (nếu cần) không ?,,,,
37,Có thực hiện phân tích chỉ tiêu không đạt của quá trình Thiết kế không?,,,,
38,Có cập nhật vào bản liên kết yêu cầu thiết kế không?,,,,
,04-QT/PM/HDCV - Lập trình,,,,
39,Có kế hoạch chi tiết giai đoạn Lập trình không ?,,,,
40,Kế hoạch lập trình có được thông qua không ?,,,,
41,Kế hoạch lập trình có bao gồm kế hoạch review code và kế hoạch tích hợp không?,,,,
42,Có quy trình cho việc tích hợp không?,,,,
43,Có quy ước lập trình không ?,,,,
44,Quy ước lập trình có được xem xét bởi đội dự án và thông qua bởi QTDA không ?,,,,
45,Quy ước lập trình có là CI không ?,,,,
46,Có thực hiện kiểm tra công cụ lập trình không (nếu cần)? Có BB kiểm tra không ?,,,,
47,Có thực hiện xem xét code theo kế hoạch không ? Có sử dụng checklist trong quá trình xem xét code không?,,,,
48,Có ghi nhn các lỗi trong quá trình xem xét code không?,,,,
49,Có thực hiện lập trình các thư viện dùng chung không? Các thư viện dùng chung có được hệ thống hoá các tài liệu không?,,,,
50,Có tài liệu mô tả chức năng hệ thống phần mềm không?,,,,
51,Tài liệu mô tả chức năng hệ thống phần mềm có được xem xét không ?,,,,
52,Có tài liệu hướng dẫn cài đặt hệ thống không ? Có được xem xét không? Bằng chứng,,,,
53,Có tài liệu hướng dẫn sử dụng không ? Có được xem xét không? Bằng chứng?,,,,
54,Có thực hiện unit test không? Lỗi có được ghi nhận không?,,,,
55,"Nếu có thực hiện tích hợp, có được test tích hợp không? Lỗi có được ghi nhận không?",,,,
56,Các tài liệu của quá trình Lập trình có được PdQA kiểm tra không?,,,,
57,Có thực hiện phân tích chỉ tiêu không đạt của quá trình Lập trình không?,,,,
,05-QT/PM/HDCV - Triển khai,,,,
58,Có kế hoạch triển khai không ?,,,,
59,Kế hoạch triển khai có được khách hàng thông qua không ?,,,,
60,Có tài liệu giải pháp triển khai không ?,,,,
61,Giải pháp triển khai có được xem xét và thông qua không ?,,,,
62,Có các tiêu chuẩn nghiệm thu công việc triển khai không ?,,,,
63,Có các tiêu chuẩn kiểm tra kết quả không ?,,,,
64,Có tài liệu về quy trình cài đặt và vận hành không ?,,,,
65,Tài liệu quy trình cài đặt và vận hành có được xem xét và thông qua không ?,,,,
66,Có kiểm tra môi trường cài đặt không? Có biên bản kiểm tra không?,,,,
67,Có các biên bản triển khai tại các điểm triển khai không? ,,,,
68,Có thực hiện đào tạo không ?,,,,
69,"Có chương trình, các tài liệu đào tạo không ?",,,,
70,Có các BB ghi nhận các hoạt động đào tạo không ?,,,,
71,"Có tổng hợp, đánh giá kết quả về đào tạo không ?",,,,
72,Có bản ghi nhận các vấn đề và giải pháp khắc phục trong khi triển khai không ?,,,,
73,Có thực hiện ghi nhận lỗi phát sinh trong quá trình triển khai không ?,,,,
74,Chương trình có được đảm bảo đã kiểm tra trước khi bàn giao cho khách hàng không? PdQA có kiểm tra không?,,,,
75,Có ghi nhận các thay đổi trong quá trình triển khai không?,,,,
76,"Các yêu cầu thay đổi có được đánh giá ảnh hưởng, thông báo lại cho khách hàng về quyết định có thực hiện hay không?",,,,
77,Các yêu cầu thay đổi có được lên kế hoạch thực hiện và thông báo cho những người liên quan không?,,,,
78,Các yêu cầu có được cập nhật vào bản liên kết yêu cầu sản phẩm không?,,,,
79,Có thực hiện phân tích chỉ tiêu không đạt của quá trình Triển khai không ?,,,,
,06-QT/PM/HDCV - Hỗ trợ,,,,
80,Có kế hoạch hỗ trợ không ?,,,,
81,Có các nội dung hỗ trợ thống nhất với khách hàng không?,,,,
82,Có các phương án hỗ trợ không ?,,,,
83,Có các phiếu Yêu cầu hỗ trợ không ?,,,,
84,Có Nhật ký hỗ trợ không ?,,,,
85,Có báo cáo hỗ trợ không ?,,,,
,Đối với các yc thay đổi phát sinh trong giai đoạn hỗ trợ: ,,,,
86, - Có được cập nhật không?,,,,
87, - Có được đánh giá ảnh hưởng không?,,,,
88, - Có thực hiện giải quyết yêu cầu và trả lời với khách hàng không?,,,,
89, - Có hẹn ngày có giải pháp cho các yêu cầu cho khách hàng không?,,,,
90, - Có thực hiện xây dựng giải pháp không?,,,,
91, - Giải pháp có được đội dự án review không?,,,,
92, - Có thực hiện việc thống nhất giải pháp với khách hàng không?,,,,
93," - Trong thời gian chỉnh sửa, có thực hiện System test không?",,,,
94, - Khách hàng cho thực hiện Acceptance test không?,,,,
95," - Khi triển khai cho khách hàng, đội dự án có lấy biên bản triển khai không?",,,,
96,Có thực hiện phân tích chỉ tiêu không đạt của quá trình Hỗ trợ không ?,,,,
,07-QT/PM/HDCV - Test,,,,
97,Có kế hoạch test không (03-BM/PM/HDCV),,,,
98,Kế hoạch test có được xem xét và thông qua bởi QTDA không ? Checklist có được sử dụng để xem xét kế hoạch test không?,,,,
99,Có tài liệu Kịch bản test không 05-BM/PM/HDCV,,,,
100,Tài liệu kịch bản test có được xem xét và thông qua không ?,,,,
101,Kế hoạch test và kịch bản test có được kiểm tra bởi PdQA trước khi sử dụng không?,,,,
102,Có biên bản kiểm tra công cụ test (nếu cần) không ?,,,,
103,Có ghi nhận các lỗi phát hiện không ?,,,,
104,Có báo cáo kết quả test không ?,,,,
105,Có xác nhận sản phẩm đủ tiêu chuẩn phát hành trước khi được bàn giao không?,,,,
106,Có biên bản test không ?,,,,
107,Có thực hiện phân tích chỉ tiêu không đạt của quá trình Test không ?,,,,
,08-QT/PM/HDCV - Quản lý cấu hình,,,,
108,Có danh sách các Baseline không  ?,,,,
109,Có danh sách các đơn vị cấu hình không ?,,,,
110,Có bản theo dõi liên kết các sản phẩm không ?,,,,
111,Cấu trúc thư mục dự án có tuân thủ theo 01-HD/PM/HDCV không?,,,,
,Có thực hiện kiểm soát thay đổi cấu hình sản phẩm theo các bước sau không:,,,,
112,"   - Ghi nhận các YCTĐ, phân tích ảnh hưởng các YCTĐ",,,,
113,"   - Cập nhật thay đổi cho các CI, xác định version mới cho các CI bị thay đổi",,,,
114,   - Có cập nhật bản ghi nhận thay đổi các CI không ?,,,,
115,  - Có thông báo về thay đổi cho những người liên quan không?,,,,
116,Có thông báo phát hành sản phẩm không ?,,,,
117,Có đầy đủ các báo cáo baseline không ?,,,,
118,Có thực hiện baseline theo đúng kế hoạch không?,,,,
119,PdQA có thực hiện đánh giá baseline không?,,,,
120,Có thực hiện công việc cập nhật dữ liệu dự án hàng tuần không?,,,,
121,Có quy trình lưu trữ dự phòng không?,,,,
122,Có phương pháp lưu trữ dự phòng không?,,,,
123,Có thực hiện lưu trữ dự phòng đúng như kế hoạch không?,,,,
124,Có thực hiện việc phân tích chỉ tiêu không đạt quá trình Quản lý cấu hình không ?,,,,
,09-QT/PM/HDCV - Quản trị dự án,,,,
125,Có Quyết định khởi động dự án không ?,,,,
126,Dự án có tuân thủ theo check list HDCV Tổ chức Khởi động dự án không ?,,,,
127,Có Kế hoạch dự án không ?,,,,
128,KHDA có được xem xét không ? Bằng chứng ?,,,,
129,Check list có được sử dụng để xem xét KHDA không?,,,,
130,KHDA có được phê duyệt không ?,,,,
131,Các mục trong Kế hoạch dự án có được thực hiện đầy đủ theo các mục sau đây không?,,,,
132,   - Có thực hiện đúng việc trao đổi thông tin nội bộ đội dự án không?,,,,
133,"   - Có thực hiện đúng theo việc mô tả chế độ báo cáo, xem xét, họp giữa đội dự án và cán bộ quản lý đơn vị không?",,,,
134,"   - Có thực hiện đúng việc trao đổi, báo cáo, phương thức theo dõi thông tin giữa đội dự án và khách hàng không?",,,,
135,   - Quy trình xử lý yêu cầu thay đổi (nếu có) có được thực hiện đúng theo kế hoạch không?,,,,
136,   - Dự án có được đánh giá nội bộ theo lịch của Navisoft không?,,,,
137,Có cập nhật KHDA lên Navisoft Insight không?,,,,
138,Có theo dõi trạng thái công việc trên Navisoft Insight không?,,,,
139,Có estimation size dự án khi khởi động dự án không?,,,,
140,Có estimation size dự án khi kết thúc phân tích không?,,,,
141,Các thay đổi so với quy trình chuẩn có được xác định trong kế hoạch dự án không?,,,,
142,Có BB họp khởi động dự án không ?,,,,
143,"Có BB bàn giao SP khi đến kỳ hạn không?
",,,,
144,Có kế hoạch chi tiết các giai đoạn không ?,,,,
145,Kế hoạch chi tiết các giai đoạn có được cập nhật trạng thái các công việc không?,,,,
146,Có thực hiện báo cáo tiến trình dự án không ?,,,,
147,Có gửi báo cáo tiến trình dự án lên forum không? Có gửi cho khách hàng (nếu cần) không?,,,,
148,Có các biên bản họp dự án không ?,,,,
149,Có các biên bản nghiệm thu (theo hợp đồng) không ?,,,,
150,"Xem xét tiến độ dự án, nếu vượt quá 20% thì có yêu cầu  cập nhật KHDA không? Số liệu không đúng với thực tế, có  re_plan lại không?",,,,
151,Có báo cáo milestone khi đến mốc kiểm soát không ?,,,,
152,Báo cáo milestone có được xem xét không ?,,,,
153,QTDA có cập nhật đủ thông tin để làm milestone đặc biệt là các thông tin về chỉ tiêu không?,,,,
154,Có họp phân tích chỉ tiêu dự án khi kết thúc milestone cùng với đội dự án không?,,,,
155,Có báo cáo tổng kết dự án không ?,,,,
156,Báo cáo tổng kết dự án có được review không ?,,,,
157,Có thực hiện phân tích các sai lệch trong báo cáo Postmortem không?.  ,,,,
158,Có nêu các bài học kinh nghiệm trong báo cáo Postmortem không?.  ,,,,
159,Có cập nhật lại báo cáo sau buổi họp tổng kết không?,,,,
160,Các mục trong Kế hoạch dự án có được thực hiện đầy đủ không ?,,,,
161,Các thành viên dự án có khai timesheet đầy đủ không?,,,,
162,QTDA có approve các timesheet của các thành viên dự án không?,,,,
163,Ghi nhận lỗi của dự án lên TesTrack có chính xác không ?,,,,
,Dự án có thực hiện quản lý rủi ro theo các bước sau không:,,,,
164,    - Có xác định rủi ro không?,,,,
165,    - Có đưa ra hướng giải quyết cho các rủi ro không?,,,,
166,"    - Có hành động, biện pháp phòng ngừa cho các rủi ro có thể xẩy ra không?",,,,
167,"    - Các rủi ro đã xẩy ra, có thực hiện nhìn lại, phân tích không?
",,,,
168,"Trong tuần có CI nào của dự án thay đổi không? Nếu có, có thông báo nội dung thay đổi cho các bên liên quan không?",,,,
169,Review báo cáo tuần: nguồn lực có khớp với số liệu từ timesheet không?,,,,
170,NC/NX hoặc khiếu nại trong tuần của dự án đã thực hiện các hành động đóng chưa?,,,,
171,QTDA có biết về danh sách NC/NX hay gặp tại Navisoft không?,,,,
172,Có thực hiện phân tích chỉ tiêu không đạt của quá trình Quản trị dự án không?,,,,
,Thêm tiếp nếu cần?,,,,
,,,,,
,,,,,
,,,,,
,,,,,
,* Nhận xét,,,,
,,,,,
,,,,,
,,,,,
,* Đề xuất,,,,
,,,,,
,[X] - Đạt,,,,
,[   ] - Xem xét lại,,,,
,[   ] - Khác,,,,
