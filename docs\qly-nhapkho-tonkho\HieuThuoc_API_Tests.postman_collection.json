{"info": {"_postman_id": "d4e5f6g7-h8i9-0123-jklm-n45678901234", "name": "HieuThuoc - API Tests", "description": "Collection for testing HieuThuoc APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "NhaCungCap API", "item": [{"name": "Get All NhaCungCap", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/nhacungcap/list", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "list"]}, "description": "<PERSON><PERSON><PERSON> danh sách tất cả nhà cung cấp"}, "response": []}, {"name": "Search NhaCungCap By Name", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=ABC", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "search_by_ten_nha_cung_cap"], "query": [{"key": "tenNhaCungCap", "value": "ABC"}]}, "description": "T<PERSON>m kiếm nhà cung cấp theo tên"}, "response": []}, {"name": "Create NhaCungCap", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"maNCC\": \"NCC001\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 1\",\n  \"diaChi\": \"Địa chỉ 1\",\n  \"soDienThoai\": \"0123456789\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/nhacungcap/create", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "create"]}, "description": "<PERSON><PERSON>o mới nhà cung cấp"}, "response": []}, {"name": "Update NhaCungCap", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"maNCC\": \"NCC001\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 1 Updated\",\n  \"diaChi\": \"Địa chỉ 1 Updated\",\n  \"soDienThoai\": \"0123456789\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/nhacungcap/update", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật thông tin nhà cung cấp"}, "response": []}, {"name": "Delete NhaCungCap", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/nhacungcap/delete?id=1", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "delete"], "query": [{"key": "id", "value": "1"}]}, "description": "Xóa nhà cung cấp theo ID"}, "response": []}], "description": "API endpoints for NhaCungCap"}, {"name": "PhieuNhap API", "item": [{"name": "Search PhieuNhap", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/search", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm phiếu nhập với phân trang"}, "response": []}, {"name": "Get PhieuNhap By ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/phieunhap/get?id=1", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "get"], "query": [{"key": "id", "value": "1"}]}, "description": "<PERSON><PERSON><PERSON> thông tin phiếu nhập theo ID"}, "response": []}, {"name": "Create <PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 1000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 100,\n      \"donGia\": 10000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/create", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "create"]}, "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> nh<PERSON>p mới"}, "response": []}, {"name": "Update PhieuNhap", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 2000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"id\": 1,\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 200,\n      \"donGia\": 10000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/update", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật thông tin phiếu nhập"}, "response": []}, {"name": "Delete PhieuNhap", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/phieunhap/delete?id=1", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "delete"], "query": [{"key": "id", "value": "1"}]}, "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> nh<PERSON>p theo <PERSON>"}, "response": []}], "description": "API endpoints for PhieuNhap"}, {"name": "TonKho API", "item": [{"name": "Search TonKho", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm tồn kho với phân trang"}, "response": []}, {"name": "Search TonKho By <PERSON><PERSON><PERSON> Name", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tenThuoc\": \"Paracetamol\",\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm tồn kho theo tên thuốc"}, "response": []}, {"name": "Update <PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"thuocId\": 1,\n  \"soLo\": \"L001\",\n  \"hanSuDung\": \"2025-12-31\",\n  \"soLuong\": 200,\n  \"viTri\": \"Kệ A-01\"\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/update", "host": ["{{baseUrl}}"], "path": ["tonkho", "update"]}, "description": "<PERSON><PERSON><PERSON> nh<PERSON>t tồn kho"}, "response": []}], "description": "API endpoints for TonKho"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}]}