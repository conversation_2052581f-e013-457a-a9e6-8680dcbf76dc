<div class="dropdown topbar-head-dropdown ms-1 header-item">
  <button
    type="button"
    class="btn btn-icon btn-topbar btn-ghost-secondary rounded-circle shadow-none"
    id="notification-dropdown"
    data-bs-toggle="dropdown"
    aria-haspopup="true"
    aria-expanded="false"
  >
    <i class="bx bx-bell fs-22"></i>
    <span
      class="position-absolute topbar-badge fs-10 translate-middle badge rounded-pill bg-danger"
      >{{ countUnRead }}<span class="visually-hidden"> Chưa đọc</span></span
    >
  </button>
  <div
    class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0"
    aria-labelledby="page-header-notifications-dropdown"
  >
    <div class="dropdown-head bg-pattern rounded-top">
      <div class="p-3">
        <div class="row align-items-center">
          <div class="col">
            <h6 class="m-0 fs-16 fw-semibold">Thông báo</h6>
          </div>
          <!-- <div class="col-auto dropdown-tabs">
              <span class="badge badge-soft-info fs-13"> 4 New</span>
            </div> -->
        </div>
      </div>

      <div id="notificationItemsTabContent">
        <div class="py-2 ps-2" id="all-noti-tab" *ngIf="countTotal > 0">
          <div
            data-simplebar
            style="max-height: 300px"
            class="pe-2"
            *ngIf="lstNoti"
          >
            <div
              *ngFor="let noti of lstNoti | slice : 0 : 5"
              [ngClass]="{ active: noti.trangThai == false }"
              class="text-reset notification-item d-block dropdown-item position-relative"
            >
              <div class="d-flex">
                <img
                  src="assets/images/users/avatar-2.jpg"
                  class="me-3 rounded-circle avatar-xs"
                  alt="user-pic"
                />
                <div class="flex-1">
                  <a class="stretched-link" (click)="navigateToLink(noti.linkLienKet, $event)">
                    <h6 class="mt-0 mb-1 fs-13 fw-semibold">
                      <!-- {{ noti.nguoiNhan?.hoTen }} -->
                    </h6>
                  </a>
                  <div class="fs-13 text-muted">
                    <p class="mb-1">
                      {{ noti.noiDung }}
                      🔔.
                    </p>
                  </div>
                  <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                    <span
                      ><i class="mdi mdi-clock-outline"></i>
                      {{ noti.createdAt | date : "dd/MM/yyyy HH:mm:ss" }}</span
                    >
                  </p>
                </div>
              </div>
            </div>

            <div class="my-3 text-center">
              <button
                (click)="showAllNotification()"
                type="button"
                class="btn btn-soft-success waves-effect waves-light"
              >
                Xem tất cả
                <i class="ri-arrow-right-line align-middle"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="p-4" id="alerts-tab" *ngIf="countTotal == 0">
          <div class="w-25 w-sm-50 pt-3 mx-auto">
            <img
              src="assets/images/svg/bell.svg"
              class="img-fluid"
              alt="user-pic"
            />
          </div>
          <div class="text-center pb-5 mt-2">
            <h6 class="fs-18 fw-semibold lh-base">
              Bạn không có thông báo nào
            </h6>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
