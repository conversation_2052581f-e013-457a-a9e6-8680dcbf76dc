ID,Controller,Endpoint,Method,Test Case Name,Description,Input,Expected Output,Preconditions,Mock Setup,Assertions

# NhaCungCapController Tests
NCC-TC-01,NhaCungCapController,/nhacungcap/list,GET,Get all suppliers successfully,Test retrieving all suppliers,N/A,ResponseDTO with status 200 and list of suppliers,Database has suppliers,Mock nhaCungCapService.getAll() to return ResponseDTO with list of suppliers,Verify status 200; Verify message "Thành công"; Verify data contains list of suppliers
NCC-TC-02,NhaCungCapController,/nhacungcap/list,GET,Get empty suppliers list,Test retrieving empty suppliers list,N/A,ResponseDTO with status 200 and empty list,Database has no suppliers,Mock nhaCungCapService.getAll() to return ResponseDTO with empty list,Verify status 200; Verify message "Thành công"; Verify data is empty list
NCC-TC-03,NhaCungCapController,/nhacungcap/list,GET,Handle service exception,Test handling exception from service,N/A,ResponseDTO with error status,N/A,Mock nhaCungCapService.getAll() to throw exception,Verify controller handles exception properly
NCC-TC-04,NhaCungCapController,/nhacungcap/search_by_ten_nha_cung_cap,GET,Search suppliers by name successfully,Test searching suppliers by name,tenNhaCungCap="ABC",ResponseDTO with status 200 and matching suppliers,Database has suppliers with name containing "ABC",Mock nhaCungCapService.searchByTenNhaCungCap() to return ResponseDTO with matching suppliers,Verify status 200; Verify message "Thành công"; Verify data contains suppliers with name containing "ABC"
NCC-TC-05,NhaCungCapController,/nhacungcap/search_by_ten_nha_cung_cap,GET,Search suppliers with no matches,Test searching suppliers with no matches,tenNhaCungCap="XYZ",ResponseDTO with status 200 and empty list,Database has no suppliers with name containing "XYZ",Mock nhaCungCapService.searchByTenNhaCungCap() to return ResponseDTO with empty list,Verify status 200; Verify message "Thành công"; Verify data is empty list
NCC-TC-06,NhaCungCapController,/nhacungcap/create,POST,Create supplier successfully,Test creating a new supplier,NhaCungCapDTO with valid data,ResponseDTO with status 201 and created supplier,N/A,Mock nhaCungCapService.create() to return ResponseDTO with created supplier,Verify status 201; Verify message "Thành công"; Verify data contains created supplier
NCC-TC-07,NhaCungCapController,/nhacungcap/create,POST,Create supplier with existing code,Test creating supplier with existing maNCC,NhaCungCapDTO with existing maNCC,ResponseDTO with status 409,Supplier with same maNCC exists,Mock nhaCungCapService.create() to return ResponseDTO with status 409,Verify status 409; Verify message "Nhà cung cấp đã tồn tại"; Verify data is null
NCC-TC-08,NhaCungCapController,/nhacungcap/create,POST,Create supplier with invalid data,Test creating supplier with invalid data,NhaCungCapDTO with invalid data,ResponseDTO with validation error,N/A,Mock nhaCungCapService.create() to return ResponseDTO with validation error,Verify error response; Verify validation message
NCC-TC-09,NhaCungCapController,/nhacungcap/update,PUT,Update supplier successfully,Test updating an existing supplier,NhaCungCapDTO with valid data and existing ID,ResponseDTO with status 200 and updated supplier,Supplier with ID exists,Mock nhaCungCapService.update() to return ResponseDTO with updated supplier,Verify status 200; Verify message "Thành công"; Verify data contains updated supplier
NCC-TC-10,NhaCungCapController,/nhacungcap/update,PUT,Update non-existent supplier,Test updating a non-existent supplier,NhaCungCapDTO with non-existent ID,ResponseDTO with status 404,No supplier with ID exists,Mock nhaCungCapService.update() to return ResponseDTO with status 404,Verify status 404; Verify message "Không tìm thấy nhà cung cấp"; Verify data is null
NCC-TC-11,NhaCungCapController,/nhacungcap/update,PUT,Update supplier with existing code,Test updating supplier with existing maNCC,NhaCungCapDTO with existing maNCC (different from current),ResponseDTO with status 409,Another supplier with same maNCC exists,Mock nhaCungCapService.update() to return ResponseDTO with status 409,Verify status 409; Verify message "Mã Nhà cung cấp đã tồn tại"; Verify data is null
NCC-TC-12,NhaCungCapController,/nhacungcap/delete,DELETE,Delete supplier successfully,Test deleting an existing supplier,id=1,ResponseDTO with status 200,Supplier with ID exists,Mock nhaCungCapService.delete() to return ResponseDTO with status 200,Verify status 200; Verify message "Thành công"
NCC-TC-13,NhaCungCapController,/nhacungcap/delete,DELETE,Delete non-existent supplier,Test deleting a non-existent supplier,id=999,ResponseDTO with status 404,No supplier with ID exists,Mock nhaCungCapService.delete() to return ResponseDTO with status 404,Verify status 404; Verify appropriate error message

# PhieuNhapController Tests
PN-TC-01,PhieuNhapController,/phieunhap/search,POST,Search import receipts successfully,Test searching import receipts with pagination,SearchDTO with currentPage=0; size=20,ResponseDTO with status 200 and paged list of import receipts,Database has import receipts,Mock phieuNhapService.search() to return ResponseDTO with PageDTO containing import receipts,Verify status 200; Verify message "Thành công"; Verify data contains PageDTO with import receipts
PN-TC-02,PhieuNhapController,/phieunhap/search,POST,Search import receipts by supplier name,Test searching import receipts by supplier name,SearchDTO with keyWord="ABC"; currentPage=0; size=20,ResponseDTO with status 200 and matching import receipts,Database has import receipts with supplier name containing "ABC",Mock phieuNhapService.search() to return ResponseDTO with matching import receipts,Verify status 200; Verify message "Thành công"; Verify data contains import receipts with supplier name containing "ABC"
PN-TC-03,PhieuNhapController,/phieunhap/search,POST,Search with no matching import receipts,Test searching with no matching results,SearchDTO with keyWord="XYZ"; currentPage=0; size=20,ResponseDTO with status 200 and empty list,Database has no matching import receipts,Mock phieuNhapService.search() to return ResponseDTO with empty PageDTO,Verify status 200; Verify message "Thành công"; Verify data.data is empty list
PN-TC-04,PhieuNhapController,/phieunhap/search,POST,Search with invalid pagination,Test searching with invalid pagination parameters,SearchDTO with currentPage=-1; size=0,ResponseDTO with validation error,N/A,Mock phieuNhapService.search() to handle invalid pagination,Verify appropriate error response
PN-TC-05,PhieuNhapController,/phieunhap/get,GET,Get import receipt by ID successfully,Test retrieving import receipt by ID,id=1,ResponseDTO with status 200 and import receipt,Import receipt with ID exists,Mock phieuNhapService.getById() to return ResponseDTO with import receipt,Verify status 200; Verify message "Thành công"; Verify data contains import receipt with matching ID
PN-TC-06,PhieuNhapController,/phieunhap/get,GET,Get non-existent import receipt,Test retrieving non-existent import receipt,id=999,ResponseDTO with status 404,No import receipt with ID exists,Mock phieuNhapService.getById() to return ResponseDTO with status 404,Verify status 404; Verify message "Không tìm thấy phiếu nhập"; Verify data is null
PN-TC-07,PhieuNhapController,/phieunhap/create,POST,Create import receipt successfully,Test creating a new import receipt,PhieuNhapDTO with valid data,ResponseDTO with status 200 and created import receipt,Supplier and user exist,Mock phieuNhapService.create() to return ResponseDTO with created import receipt,Verify status 200; Verify message "Thành công"; Verify data contains created import receipt
PN-TC-08,PhieuNhapController,/phieunhap/create,POST,Create import receipt with non-existent supplier,Test creating import receipt with non-existent supplier,PhieuNhapDTO with non-existent nhaCungCapId,ResponseDTO with error status,Supplier does not exist,Mock phieuNhapService.create() to return ResponseDTO with error,Verify error response; Verify appropriate error message
PN-TC-09,PhieuNhapController,/phieunhap/create,POST,Create import receipt with non-existent user,Test creating import receipt with non-existent user,PhieuNhapDTO with non-existent nguoiDungId,ResponseDTO with error status,User does not exist,Mock phieuNhapService.create() to return ResponseDTO with error,Verify error response; Verify appropriate error message
PN-TC-10,PhieuNhapController,/phieunhap/create,POST,Create import receipt with invalid details,Test creating import receipt with invalid details,PhieuNhapDTO with invalid chiTietPhieuNhaps,ResponseDTO with validation error,N/A,Mock phieuNhapService.create() to return ResponseDTO with validation error,Verify error response; Verify validation message
PN-TC-11,PhieuNhapController,/phieunhap/update,PUT,Update import receipt successfully,Test updating an existing import receipt,PhieuNhapDTO with valid data and existing ID,ResponseDTO with status 200 and updated import receipt,Import receipt with ID exists,Mock phieuNhapService.update() to return ResponseDTO with updated import receipt,Verify status 200; Verify message "Thành công"; Verify data contains updated import receipt
PN-TC-12,PhieuNhapController,/phieunhap/update,PUT,Update non-existent import receipt,Test updating a non-existent import receipt,PhieuNhapDTO with non-existent ID,ResponseDTO with status 404,No import receipt with ID exists,Mock phieuNhapService.update() to return ResponseDTO with status 404,Verify status 404; Verify message "Không tìm thấy phiếu nhập"; Verify data is null
PN-TC-13,PhieuNhapController,/phieunhap/update,PUT,Update import receipt with invalid details,Test updating import receipt with invalid details,PhieuNhapDTO with invalid chiTietPhieuNhaps,ResponseDTO with validation error,N/A,Mock phieuNhapService.update() to return ResponseDTO with validation error,Verify error response; Verify validation message
PN-TC-14,PhieuNhapController,/phieunhap/delete,DELETE,Delete import receipt successfully,Test deleting an existing import receipt,id=1,ResponseDTO with status 200,Import receipt with ID exists,Mock phieuNhapService.delete() to return ResponseDTO with status 200,Verify status 200; Verify message "Thành công"
PN-TC-15,PhieuNhapController,/phieunhap/delete,DELETE,Delete non-existent import receipt,Test deleting a non-existent import receipt,id=999,ResponseDTO with error status,No import receipt with ID exists,Mock phieuNhapService.delete() to handle non-existent ID,Verify appropriate error response

# TonKhoController Tests
TK-TC-01,TonKhoController,/tonkho/update,PUT,Update inventory successfully,Test updating inventory information,TonKhoDTO with valid data and existing ID,ResponseDTO with status 200 and updated inventory,Inventory with ID exists; Product with thuocId exists,Mock tonKhoService.update() to return ResponseDTO with updated inventory,Verify status 200; Verify message "Thành công"; Verify data contains updated inventory
TK-TC-02,TonKhoController,/tonkho/update,PUT,Update non-existent inventory,Test updating non-existent inventory,TonKhoDTO with non-existent ID,ResponseDTO with status 404,No inventory with ID exists,Mock tonKhoService.update() to return ResponseDTO with status 404,Verify status 404; Verify message "Tồn kho không tồn tại"; Verify data is null
TK-TC-03,TonKhoController,/tonkho/update,PUT,Update inventory with non-existent product,Test updating inventory with non-existent product,TonKhoDTO with non-existent thuocId,ResponseDTO with error status,Product does not exist,Mock tonKhoService.update() to return ResponseDTO with error,Verify error response; Verify appropriate error message
TK-TC-04,TonKhoController,/tonkho/update,PUT,Update inventory with invalid quantity,Test updating inventory with invalid quantity,TonKhoDTO with soLuong < 0,ResponseDTO with validation error,N/A,Mock tonKhoService.update() to return ResponseDTO with validation error,Verify error response; Verify validation message
TK-TC-05,TonKhoController,/tonkho/update,PUT,Update inventory with invalid expiry date,Test updating inventory with invalid expiry date,TonKhoDTO with hanSuDung in the past,ResponseDTO with validation error,N/A,Mock tonKhoService.update() to return ResponseDTO with validation error,Verify error response; Verify validation message
TK-TC-06,TonKhoController,/tonkho/update,PUT,Update inventory with empty batch number,Test updating inventory with empty batch number,TonKhoDTO with soLo="",ResponseDTO with validation error,N/A,Mock tonKhoService.update() to return ResponseDTO with validation error,Verify error response; Verify validation message
TK-TC-07,TonKhoController,/tonkho/search,POST,Search inventory successfully,Test searching inventory with pagination,SearchTonKhoDTO with currentPage=0; size=20,ResponseDTO with status 200 and paged list of inventory,Database has inventory records,Mock tonKhoService.search() to return ResponseDTO with PageDTO containing inventory records,Verify status 200; Verify message "Thành công"; Verify data contains PageDTO with inventory records
TK-TC-08,TonKhoController,/tonkho/search,POST,Search inventory by product name,Test searching inventory by product name,SearchTonKhoDTO with tenThuoc="Paracetamol"; currentPage=0; size=20,ResponseDTO with status 200 and matching inventory records,Database has inventory for products with name containing "Paracetamol",Mock tonKhoService.search() to return ResponseDTO with matching inventory records,Verify status 200; Verify message "Thành công"; Verify data contains inventory records for products with name containing "Paracetamol"
TK-TC-09,TonKhoController,/tonkho/search,POST,Search inventory by batch number,Test searching inventory by batch number,SearchTonKhoDTO with soLo="L001"; currentPage=0; size=20,ResponseDTO with status 200 and matching inventory records,Database has inventory with batch number containing "L001",Mock tonKhoService.search() to return ResponseDTO with matching inventory records,Verify status 200; Verify message "Thành công"; Verify data contains inventory records with batch number containing "L001"
TK-TC-10,TonKhoController,/tonkho/search,POST,Search inventory by manufacturer name,Test searching inventory by manufacturer name,SearchTonKhoDTO with tenNhaSanXuat="ABC"; currentPage=0; size=20,ResponseDTO with status 200 and matching inventory records,Database has inventory for products from manufacturer with name containing "ABC",Mock tonKhoService.search() to return ResponseDTO with matching inventory records,Verify status 200; Verify message "Thành công"; Verify data contains inventory records for products from manufacturer with name containing "ABC"
TK-TC-11,TonKhoController,/tonkho/search,POST,Search with no matching inventory,Test searching with no matching results,SearchTonKhoDTO with tenThuoc="XYZ"; currentPage=0; size=20,ResponseDTO with status 200 and empty list,Database has no matching inventory records,Mock tonKhoService.search() to return ResponseDTO with empty PageDTO,Verify status 200; Verify message "Thành công"; Verify data.data is empty list
TK-TC-12,TonKhoController,/tonkho/search,POST,Search with invalid pagination,Test searching with invalid pagination parameters,SearchTonKhoDTO with currentPage=-1; size=0,ResponseDTO with validation error,N/A,Mock tonKhoService.search() to handle invalid pagination,Verify appropriate error response
