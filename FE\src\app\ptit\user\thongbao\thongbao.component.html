<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <div class="row">
        <!-- <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0"><PERSON><PERSON> sơ của tôi</h4>
          </div>
        </div> -->
      </div>
      <div class="row">
        <div class="col-xxl-3">
          <div class="card">
            <div class="card-body p-4">
              <div class="text-center">
                <div
                  class="profile-user position-relative d-inline-block mx-auto mb-4"
                >
                  <img
                    [src]="imageUrl"
                    class="rounded-circle avatar-xl img-thumbnail user-profile-image shadow"
                    alt="user-profile-image"
                    *ngIf="imageUrl"
                  />
                  <img
                    [src]="userUpdate.avatar"
                    class="rounded-circle avatar-xl img-thumbnail user-profile-image shadow"
                    *ngIf="userUpdate.avatar && !imageUrl"
                    alt="user-profile-image"
                  />

                  <img
                    src="assets/images/no_image.png"
                    class="rounded-circle avatar-xl img-thumbnail user-profile-image shadow"
                    *ngIf="!userUpdate.avatar && !imageUrl"
                    alt="user-profile-image"
                  />
                </div>
                <h5 class="fs-16 mb-1">
                  {{ user.hoTen }} / {{ user.soDienThoai }}
                </h5>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-body">
              <div
                class="mb-3 d-flex align-items-center cursor-pointer"
                [routerLink]="['/user/profile']"
              >
                <div class="avatar-xs d-block flex-shrink-0 me-3">
                  <i
                    class="mdi mdi-account-circle text-muted fs-3 align-middle me-1"
                  ></i>
                </div>
                <div>Thông tin cá nhân</div>
              </div>
              <div
                class="d-flex align-items-center bg-soft-info mb-3 cursor-pointer"
                [routerLink]="['/user/thongbao']"
              >
                <div class="avatar-xs d-block flex-shrink-0 me-3">
                  <i class="bx bx-bell fs-24 align-middle"></i>
                </div>
                <div>Thông báo</div>
              </div>
              <div
                *ngIf="!isAdmin"
                class="d-flex align-items-center cursor-pointer mb-3"
                [routerLink]="['/user/donmua']"
              >
                <div class="avatar-xs d-block flex-shrink-0 me-3">
                  <i class="ri-store-2-fill me-1 align-middle fs-3"></i>
                </div>
                <div>Đơn mua</div>
              </div>

              <div
                class="d-flex align-items-center cursor-pointer"
                (click)="logout()"
              >
                <div class="avatar-xs d-block flex-shrink-0 me-3">
                  <i
                    class="mdi mdi-logout text-muted fs-3 align-middle me-1"
                  ></i>
                </div>
                <div>Đăng xuất</div>
              </div>
            </div>
          </div>
          <!--end card-->
        </div>
        <!--end col-->
        <div class="col-xxl-9">
          <div class="col-lg-12">
            <div class="card" *ngIf="lstNoti && lstNoti.length > 0">
              <div class="card-body">
                <div class="d-flex align-items-center mb-4">
                  <!-- <h5 class="card-title flex-grow-1 mb-0">Documents</h5> -->
                  <div class="flex-shrink-0">
                    <input
                      class="form-control d-none"
                      type="file"
                      id="formFile"
                    />
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-12">
                    <p-dataView
                      [value]="lstNoti"
                      [rowHover]="true"
                      dataKey="id"
                      [showCurrentPageReport]="true"
                      [rows]="10"
                      [alwaysShowPaginator]="true"
                      currentPageReportTemplate=""
                    >
                      <ng-template let-noti pTemplate="listItem">
                        <div
                          [ngClass]="{
                            'alert-danger': noti.readYn == 'N',
                            'border-light': noti.readYn == 'Y'
                          }"
                          class="alert"
                          role="alert"
                        >
                          <div class="d-flex align-items-center">
                            <!-- <lord-icon
    src="https://cdn.lordicon.com/nkmsrxys.json"
    trigger="loop"
    colors="primary:#121331,secondary:#f06548"
    style="width: 80px; height: 80px"
  ></lord-icon> -->

                            <img
                              [src]="
                                noti &&
                                noti.nguoiNhan[0] &&
                                noti.nguoiNhan[0].avatar
                                  ? noti.nguoiNhan[0].avatar
                                  : 'assets/images/users/avatar-2.jpg'
                              "
                              class="me-3 rounded-circle avatar-xs"
                              alt="user-pic"
                            />

                            <div class="ms-2">
                              <h5 class="fs-14 text-danger fw-semibold">
                                {{ noti.title }}
                              </h5>
                              <p class="text-black mb-1">
                                {{ noti.noiDung }}
                              </p>
                              <a
                                (click)="navigateToLink(noti.linkLienKet, $event)"
                                type="button"
                                class="btn ps-0 btn-sm btn-link text-danger text-uppercase"
                                style="cursor: pointer;"
                              >
                                Xem chi tiết
                              </a>
                            </div>
                          </div>
                        </div>
                      </ng-template>
                    </p-dataView>
                  </div>
                </div>
              </div>
            </div>

            <div
              class="card"
              id="orderList"
              *ngIf="lstNoti && lstNoti.length == 0"
            >
              <div class="card-body">
                <div>
                  <div class="table-responsive table-card mb-1">
                    <div class="noresult" style="display: none">
                      <div class="text-center">
                        <lord-icon
                          src="https://cdn.lordicon.com/msoeawqm.json"
                          trigger="loop"
                          colors="primary:#405189,secondary:#0ab39c"
                          style="width: 75px; height: 75px"
                        ></lord-icon>
                        <h5 class="mt-2">Không có thông báo nào</h5>
                        <p class="text-muted">
                          <!-- We've searched more than 150+ Orders We did not find
                          any orders for you search. -->
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
