ID,Tên Test Case,<PERSON><PERSON> tả,Endpoint,Method,Input,Expected Output,<PERSON><PERSON><PERSON><PERSON> kiện tiên quyết
PN-TC-01,T<PERSON><PERSON> kiếm phiếu nhập thành công,<PERSON><PERSON><PERSON> tra API tìm kiếm phiếu nhập với phân trang,/phieunhap/search,POST,SearchDTO với currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách phiếu nhập,<PERSON><PERSON> dữ liệu phiếu nhập trong database
PN-TC-02,Tìm kiếm phiếu nhập theo tên nhà cung cấp,Kiểm tra API tìm kiếm phiếu nhập theo tên nhà cung cấp,/phieunhap/search,POST,SearchDTO với keyWord="ABC"; currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách phiếu nhập của nhà cung cấp có tên chứa "ABC",<PERSON><PERSON> phiếu nhập của nhà cung cấp có tên chứa "ABC"
PN-TC-03,Tìm kiếm phiếu nhập với tên nhà cung cấp không tồn tại,Kiểm tra API tìm kiếm phiếu nhập với tên nhà cung cấp không tồn tại,/phieunhap/search,POST,SearchDTO với keyWord="XYZ123"; currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách rỗng,Không có phiếu nhập của nhà cung cấp có tên chứa "XYZ123"
PN-TC-04,Tìm kiếm phiếu nhập với phân trang không hợp lệ,Kiểm tra API tìm kiếm phiếu nhập với phân trang không hợp lệ,/phieunhap/search,POST,SearchDTO với currentPage=-1; size=20,Status: 400; Lỗi validation,Tham số phân trang không hợp lệ
PN-TC-05,Tìm kiếm phiếu nhập với kích thước trang không hợp lệ,Kiểm tra API tìm kiếm phiếu nhập với kích thước trang không hợp lệ,/phieunhap/search,POST,SearchDTO với currentPage=0; size=0,Status: 400; Lỗi validation,Tham số kích thước trang không hợp lệ
PN-TC-06,Tìm kiếm phiếu nhập với sắp xếp theo trường không tồn tại,Kiểm tra API tìm kiếm phiếu nhập với sắp xếp theo trường không tồn tại,/phieunhap/search,POST,SearchDTO với sortedField="fieldNotExist",Status: 500; Lỗi khi sắp xếp theo trường không tồn tại,Trường sắp xếp không tồn tại
PN-TC-07,Lấy thông tin phiếu nhập theo ID thành công,Kiểm tra API lấy thông tin phiếu nhập theo ID,/phieunhap/get,GET,id=1,Status: 200; Msg: "Thành công"; Data: Thông tin phiếu nhập có ID=1,Phiếu nhập với ID=1 tồn tại
PN-TC-08,Lấy thông tin phiếu nhập với ID không tồn tại,Kiểm tra API lấy thông tin phiếu nhập với ID không tồn tại,/phieunhap/get,GET,id=999,Status: 409; Msg: "Không tìm thấy phiếu nhập",Không có phiếu nhập với ID=999
PN-TC-09,Lấy thông tin phiếu nhập với ID không hợp lệ,Kiểm tra API lấy thông tin phiếu nhập với ID không hợp lệ,/phieunhap/get,GET,id=abc,Status: 400; Lỗi validation,ID không phải là số
PN-TC-10,Tạo phiếu nhập mới thành công,Kiểm tra API tạo phiếu nhập mới,/phieunhap/create,POST,PhieuNhapDTO với thông tin hợp lệ,Status: 200; Msg: "ok"; Data: Thông tin phiếu nhập vừa tạo,Nhà cung cấp và người dùng tồn tại; Thuốc trong chi tiết phiếu nhập tồn tại
PN-TC-11,Tạo phiếu nhập với nhà cung cấp không tồn tại,Kiểm tra API tạo phiếu nhập với nhà cung cấp không tồn tại,/phieunhap/create,POST,PhieuNhapDTO với nhaCungCapId không tồn tại,Status: 500; Lỗi khi không tìm thấy nhà cung cấp,Nhà cung cấp với ID không tồn tại
PN-TC-12,Tạo phiếu nhập với người dùng không tồn tại,Kiểm tra API tạo phiếu nhập với người dùng không tồn tại,/phieunhap/create,POST,PhieuNhapDTO với nguoiDungId không tồn tại,Status: 500; Lỗi khi không tìm thấy người dùng,Người dùng với ID không tồn tại
PN-TC-13,Tạo phiếu nhập với thuốc không tồn tại,Kiểm tra API tạo phiếu nhập với thuốc không tồn tại trong chi tiết phiếu nhập,/phieunhap/create,POST,PhieuNhapDTO với thuocId không tồn tại trong chiTietPhieuNhaps,Status: 500; Lỗi khi không tìm thấy thuốc,Thuốc với ID không tồn tại
PN-TC-14,Tạo phiếu nhập với danh sách chi tiết rỗng,Kiểm tra API tạo phiếu nhập với danh sách chi tiết rỗng,/phieunhap/create,POST,PhieuNhapDTO với chiTietPhieuNhaps rỗng,Status: 400; Lỗi validation,Danh sách chi tiết phiếu nhập không được rỗng
PN-TC-15,Tạo phiếu nhập với số lượng âm trong chi tiết,Kiểm tra API tạo phiếu nhập với số lượng âm trong chi tiết,/phieunhap/create,POST,PhieuNhapDTO với soLuong < 0 trong chiTietPhieuNhaps,Status: 400; Lỗi validation,Số lượng phải lớn hơn 0
PN-TC-16,Tạo phiếu nhập với đơn giá âm trong chi tiết,Kiểm tra API tạo phiếu nhập với đơn giá âm trong chi tiết,/phieunhap/create,POST,PhieuNhapDTO với donGia < 0 trong chiTietPhieuNhaps,Status: 400; Lỗi validation,Đơn giá phải lớn hơn hoặc bằng 0
PN-TC-17,Cập nhật thông tin phiếu nhập thành công,Kiểm tra API cập nhật thông tin phiếu nhập,/phieunhap/update,PUT,PhieuNhapDTO với thông tin hợp lệ và ID tồn tại,Status: 200; Msg: "Thành công"; Data: Thông tin phiếu nhập sau khi cập nhật,Phiếu nhập với ID đã tồn tại
PN-TC-18,Cập nhật thông tin phiếu nhập không tồn tại,Kiểm tra API cập nhật thông tin phiếu nhập không tồn tại,/phieunhap/update,PUT,PhieuNhapDTO với ID không tồn tại,Status: 200; Msg: "Không tìm thấy phiếu nhập",Không có phiếu nhập với ID cần cập nhật
PN-TC-19,Cập nhật phiếu nhập với danh sách chi tiết rỗng,Kiểm tra API cập nhật phiếu nhập với danh sách chi tiết rỗng,/phieunhap/update,PUT,PhieuNhapDTO với chiTietPhieuNhaps rỗng,Status: 400; Lỗi validation,Danh sách chi tiết phiếu nhập không được rỗng
PN-TC-20,Xóa phiếu nhập thành công,Kiểm tra API xóa phiếu nhập,/phieunhap/delete,DELETE,id=1,Status: 200; Msg: "Thành công",Phiếu nhập với ID=1 tồn tại
PN-TC-21,Xóa phiếu nhập không tồn tại,Kiểm tra API xóa phiếu nhập không tồn tại,/phieunhap/delete,DELETE,id=999,Status: 200; Msg: "Thành công" (Không báo lỗi khi xóa ID không tồn tại),Không có phiếu nhập với ID=999
PN-TC-22,Xóa phiếu nhập với ID không hợp lệ,Kiểm tra API xóa phiếu nhập với ID không hợp lệ,/phieunhap/delete,DELETE,id=abc,Status: 400; Lỗi validation,ID không phải là số
