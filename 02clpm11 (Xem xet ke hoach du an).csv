Checklist xem xét kế hoạch dự án,,,,,,
,,,,,,
Mã dự án,,,,,,
Phiên bản sản phẩm,,,,,,
Người xem xét,,,,,,
PcQA,,,,,,
Ngày xem xét,,,,,,
Lần Xem xét,,,,,,
PdQA,,,,,,
Ngày xem xét,,,,,,
Lần Xem xét,,,,,,
Kích cỡ sản phẩm,,,,,,
Nguồn lực dùng để xem xét (man-hour),,,,,,
,,,,,,
Câu hỏi,Có,Không,N/A,<PERSON><PERSON> chú,PcQA,PdQA
Trang Bìa,,,,,,
Tài liệu có tuân theo các thủ tục quản lý tài liệu không?,x,,,,,
Tài liệu có dấu hiệu bảo mật không?,x,,,,,
<PERSON><PERSON><PERSON> liệu có tuân theo biểu mẫu mới nhất không?,x,,,,,
Tên công ty ,x,,,,,
Tên dự án có đúng với hợp đồng kinh tế/quyết định khởi động dự án không?,x,,,,,
Mã hiệu dự án có đúng với quyết định khởi động dự án không?,x,,,,,
Mã hiệu tài liệu có đặt đúng CI đã được quy định trong tài liệu hướng dẫn quản lý cấu hình không?,x,,,,,
Địa điểm ban hành có đúng nơi và tháng ban hành không?,x,,,,,
Bảng ghi nhận thay đổi tài liệu (dành cho các version sau version 1.0),,,,,,
Bảng ghi nhận có được đề cập đầy đủ các mục thay đổi trong tài liệu không?,x,,,,,
"<Tuỳ vào hạng mục thay đổi để review tiếp các hạng mục đó, phải đảm bảo một điền tất cả các phụ lục liên quan đến thay đổi phải được update cho đồng bộ)",,,,,,
Trang ký,,,,,,
"Có đây đủ thông tin họ tên, chức vụ, ngày ký của: QTDA, PcQA, PdQA, GDDA, Khách hàng ?",x,,,,,
Thuật ngữ và định nghĩa,,,,,,
Có đề cập đầy đủ các từ viết tắt và thuật ngữ trong tài liệu không?,x,,,,,
1.1 Tổng quan dự án,,,,,,
"Khách hàng, địa điểm, có được điền đầy đủ và đúng theo hợp đồng kinh tế không? ",x,,,,,
Đơn vị thực hiện có đúng với đơn vị Navisoft đang làm không?,x,,,,,
Tên dự án có đúng với tên đã được đề cập ở trang bìa không?,x,,,,,
Cấp dự án có đúng theo hướng dẫn quản trị dự án không?,x,,,,,
Ngày bắt đầu và kết thúc dự án có không?,x,,,,,
Phạm vi và mục tiêu dự án có đúng với quyết định khởi động dự án không?,x,,,,,
Qui mô dự án có bằng với nhân lực dự án được ghi ở mục 6.1 KHDA?,x,,,,,
1.2 Sản phẩm bàn giao,,,,,,
Sản phẩm bàn giao có đúng với sản phẩm đã được đề cập trong hợp đồng kinh tế không?,x,,,,,
Các thông tin về mỗi sản phẩm bàn giao có đủ không?,x,,,,,
Ngày bàn giao,x,,,,,
Nơi bàn giao,x,,,,,
Với mỗi sản phẩm bàn giao có ghi nhận thông tin bàn giao lần đầu hay bản phê duyệt không?,x,,,,,
1.3 Sự phụ thuộc đặc biệt,,,,,,
Sự phụ thuộc đặc biệt có được mô tả không?,x,,,,,
Có đề cập đến ngày giải quyết xong sự phụ thuộc đó không?,x,,,,,
Khi cập nhật lại KHDA nếu có sự phụ thuộc đặc biệt phát sinh có được ghi nhận không?,x,,,,,
1.4 Giả thiết và ràng buộc?,,,,,,
Các giả thiết và ràng buộc có liệt kê không?,x,,,,,
"Có phân tích giữa giả thiết và ràng buộc không? (giả thiết là của đội dự án đưa ra, ràng buộc là do khách hàng đưa ra)",x,,,,,
1.5 Các tài liệu liên quan,,,,,,
Có liệt kê các tài liệu liên quan không?,x,,,,,
Thông tin về các tài liệu liên quan có đủ không?,x,,,,,
Tên tài liệu,x,,,,,
Ngày phát hành,x,,,,,
Nguồn,x,,,,,
2.1 Sơ đồ dự án,,,,,,
Sơ đồ dự án có đúng với hướng dẫn quản trị dự án không?,x,,,,,
"Nếu dự án có sơ đồ khác thì sơ đồ đó có đảm bảo 3 yếu tố: Thành viên chính của dự án- khách hàng- và thành phần  hỗ trợ (hành chánh, tài chánh, HR ..)",x,,,,,
2.2 Đội dự án,,,,,,
Trách nhiệm của các nhóm có được xác định không?,x,,,,,
Thời gian làm việc của các thành viên dự án có nằm trong ≤ 100% không?,x,,,,,
Có đảm bảo thời gian thành viên dự án tham gia vào dự án này không đụng với thời gian tham gia dự án khác đang làm không? ,x,,,,,
Đội dự án có được xác định đúng với skill matrix của dự án không? ,x,,,,,
các thành viên đội dự án có đủ để làm công việc được giao không? (Người giao làm phân tích thì ít nhất đã được đào tạo về quy trình phân tích…. Tương tự cho các vị trí khác),x,,,,,
Có xác định nhóm CCB cho dự án không?,,,x,,,
2.3.1 Nội bộ Navisoft,,,,,,
Đại diện chất lượng có đầy đủ trách nhiệm và thời gian làm việc không?,x,,,,,
Đại diện Nhân sự có đầy đủ trách nhiệm và thời gian làm việc không?,x,,,,,
Đại diện hành chánh có đầy đủ trách nhiệm và thời gian làm việc không?,x,,,,,
Đại diện Tài chánh có đầy đủ trách nhiệm và thời gian làm việc không?,x,,,,,
Đại diện IT có đầy đủ trách nhiệm và thời gian làm việc không?,x,,,,,
Đại diện Thư ký trung tâm có đầy đủ trách nhiệm và thời gian làm việc không?,x,,,,,
Đại diện kinh doanh có đầy đủ trách nhiệm và thời gian làm việc không?,x,,,,,
Đại diện của chi nhánh có đầy đủ trách nhiệm và thời gian làm việc không?,x,,,,,
<bổ sung thêm nếu cần>,,,,,,
2.3.2 Khách hàng,,,,,,
"Có xác định đầy đủ tên, vị trí công tác và trách nhiệm và thời gian làm việc không?",,,x,,,
Duyệt KHDA,,,x,,,
Duyệt URD,,,x,,,
Duyệt yêu cầu thay đổi,,,x,,,
Tiến hành acceptance test,,,x,,,
Nghiệm thu dự án,,,x,,,
<bổ sung thêm nếu cần>,,,,,,
3.1 Mục tiêu chất lượng,,,,,,
Danh mục Chỉ tiêu chất lượng có đúng với PCB version mới nhất không?,x,,,,,
Giá trị dự kiến sai khác target của PCB thì có được sự đồng ý của giám đốc dự án và SEPG không?,,,x,,,
Nếu bỏ chỉ tiêu nào so với PCB có được sự đồng ý của giám đốc dự án và SEPG không?,,,x,,,
Chế độ theo dõi chỉ tiêu có được ghi nhận không đúng ?,,,x,,,
Chế độ đó có đúng với quy định trong hướng dẫn tính chỉ tiêu dự án không?,x,,,,,
3.2.1 Kiểm tra xem xét,,,,,,
Danh sách sản phẩm có được liệt kê đầy đủ theo sản phẩm của dự án ở mục 4.2.2 không?,x,,,,,
"URD, SRS",x,,,,,
ADD,x,,,,,
DDD,x,,,,,
STP,x,,,,,
TCS,x,,,,,
ITP,x,,,,,
SRC,x,,,,,
UM,,,,,,
<bổ sung thêm nếu dự án có sản phẩm khác>,,,,,,
Có xác định phương pháp xem xét cho mỗi sản phẩm không?,x,,,,,
Người xem xét và người duyệt có đúng với Navisoft radio không?,x,,,,,
Ngày xem xét sản phẩm có đúng là trước ngày bàn giao cho khách hàng so với mục 1.2 không?,x,,,,,
3.2.2 Các hoạt động khác,,,,,,
Danh sách sản phẩm final inspection có đầy đủ so với danh sách sản phẩm bàn giao cho khách hàng mục 1.2 không?,,,x,,,
Thời gian final inspection có trước ngày ban giao cho khách hàng không?,,,x,,,
Có xác định người làm final không?,,,x,,,
Có xác định hoạt động quality gate không?,,,x,,,
Có xác định người và thời gian làm quality gate không?,,,x,,,
Có xác định hoạt động internal audit không?,,,x,,,
Có xác định người và thời gian làm internal audit không?,,,x,,,
Có xác định hoạt động thu thập số liệu của dự án không?,,,x,,,
Có xác định người và thời gian thu thập số liệu này không?,,,x,,,
4.1.1 Chuẩn mực,,,,,,
Có ghi nhận đầy đủ nội dung các chuẩn mực áp dụng cho dự án không?,x,,,,,
Nếu không áp dụng chuẩn của Navisoft thì có ghi nhận chuẩn khác không?,,,,,,
4.1.2 Các thay đổi (tailoring),,,,,,
Các thay đổi của dự án so với chu trình chuẩn có được ghi nhận đầy đủ không?,x,,,,,
Các thay đổi có phù hợp với tài liệu hướng dẫn tailoring không?,x,,,,,
Các tailoring có được ghi nhận trong danh sách tailoring không?,x,,,,,
Các tailoring có đúng điều kiện áp dụng của dự án không?,x,,,,,
Các tailoring của dự án có được duyệt bởi GĐDA hoặc SEPG hoặc Trưởng Ban Chất lượng không?,x,,,,,
4.1.3 Các sai lệch (deviation),,,,,,
Có ghi nhận đầy đủ các sai lệch của dự án không?,,,,,,
Các lý do / điều kiện áp dụng đối với các sai lệch này có được ghi nhận đầy đủ không?,,,,,,
Các deviation của dự án có được duyệt bởi GĐDA hoặc SEPG hoặc Trưởng Ban Chất lượng không?,,,,,,
4.2.1 Chu trình dự án,,,,,,
Chu trình dự án có đúng với chu trình chuẩn trong tài liệu 10-HD/PM/HDCV không?,x,,,,,
Nếu không đúng thì chu trình mới này có được giám đốc dự án và SEPG duyệt chưa?,,,,,,
Việc sai khác này có được mô tả ở phần 4.1.2 không?,,,,,,
4.2.2 Lịch trình tổng thể,,,,,,
Dự án có đủ 6 giai đoạn theo đúng hướng dẫn 10-HD/PM/HDCV?,x,,,,,
Nếu không đủ thì có được sự đồng ý của GDDA và SEPG chưa?,,,,,,
Ngày bắt đầu của giai đoạn sau có sau ngày kết thúc của giai đoạn trước không?,x,,,,,
Sản phẩm của các giai đoạn có đúng với mô tả của hướng dẫn 10-HD/PM/HDCV?,x,,,,,
Nếu khác có được sự đồng ý của GDDA và SEPG chưa?,,,,,,
Ngày bắt đầu của giai đoạn đầu tiên và ngày kết thúc của giai đoạn cuối cùng có đúng với ngày bắt đầu và kết thúc dự án ở mục 1.1 không?,x,,,,,
Tên của các giai đoạn có đúng với tên trong tài liệu hướng dẫn: 10-HD/PM/HDCV? Và đúng với tên của chu trình dự án không?,x,,,,,
4.2.3 Mốc kiểm soát chính,,,,,,
Mốc kiểm soát có đầy đủ 6 giai đoạn không?,x,,,,,
Có điều kiện hoàn thành giai đoạn không?,x,,,,,
Có xác định ngày hoàn thành giai đoạn không?,x,,,,,
Ngày hoàn thành giai đoạn có cùng ngày kết thúc giai đoạn không?,x,,,,,
4.3.1 Nội bộ dự án,,,,,,
Có chế độ làm kế hoạch chi tiết (giao việc) giao việc cho các thành viên dự án và các bên liên quan không?,x,,,,,
Có chế độ làm việc kiểm soát công việc đã giao cho các thành viên dự án và các bên liên quan không?,x,,,,,
Có Cách thức theo dõi kiểm soát công việc không?,x,,,,,
Có Cách thức trao đổi thông tin giữa các thành viên dự án và các bên liên quan không?,x,,,,,
"Có chế độ kiểm soát risk, issues, sự phụ thuộc đặc biệt, kế hoạch training, tiến độ và nhân lực dự án không?",x,,,,,
4.3.2 Đơn vị phụ trách dự án,,,,,,
Có chế độ báo cáo tiến độ dự án cho quản lý cấp cao không?,x,,,,,
Có mô tả hình thức báo cáo với quản lý cấp cao không?,x,,,,,
Cách thức trao đổi thông tin giữa đội dự án và quản lý cấp cao có được mô tả không?,x,,,,,
Có mô tả cách thức giải quyết và feedback của quản lý cấp cao với đội dự án không?,x,,,,,
4.3.3 Khách hàng,,,,,,
Có chế độ báo cáo tiến độ dự án cho khách hàng không?,x,,,,,
Có mô tả hình thức báo cáo với khách hàng không?,x,,,,,
Cách thức trao đổi thông tin giữa đội dự án và khách hàng không?,x,,,,,
Có mô tả cách thức giải quyết yêu cầu thay đổi cho khách hàng biết không?,x,,,,,
Có mô tả cách thức làm việc của nhóm CCB không?,x,,,,,
4.3.4 Khác,,,,,,
"Có mô tả cách thức làm việc với các bên liên quan như HR, AD … không?",x,,,,,
5.1 Baseline,,,,,,
Có đầy đủ 4 baseline tối thiểu như quy định không?,x,,,,,
Nếu không thì có ghi nhận thay đổi ở mục 4.1.2 không? Tailoring này đã được duyệt chưa?,,,,,,
Có ghi thời điểm làm baseline không?,x,,,,,
Có khác định thời gian baseline không?,x,,,,,
Tên baseline có đúng với tên quy định trong tài liệu hướng dẫn quản lý cấu hình không?,x,,,,,
5.2 Đơn vị cấu hình,,,,,,
CIID có được đặt đúng như quy tắc đặt tên trong hướng dẫn quản lý cấu hình không?,x,,,,,
ID bắt đầu ghi nhận có đúng với mô tả của hướng dẫn quản lý cấu hình mục 5.2 không?,x,,,,,
Các CI liệt kê có đầy đủ so với sản phẩm dự án mục 4.2.2 không?,x,,,,,
Có CI về sản phẩm khách hàng cung cấp ở mục 6.4 không?,x,,,,,
Có CI về sản phẩm reuse ở mục 6.3 không?,x,,,,,
5.3 Thư mục dự án,,,,,,
Cây thư mục dự án có đủ 3 môi trường không?,x,,,,,
Cách thức thể hiện cây thư mục có tuân theo hướng dẫn quản lý cấu hình mục 5.7 không?,x,,,,,
Có mô tả đầy đủ cách thức lưu giữ cho từng môi trường và từng mục không?,x,,,,,
Có mô tả đúng quyền quy cập như mục 4.6 hướng dẫn quản lý cấu hình không?,x,,,,,
Có mô tả đường dẫn lưu VSS của dự án không?,x,,,,,
5.4 Lưu trữ dự phòng,,,,,,
Có đề cập đến việc backup toàn bộ thư mục dự án của cán bộ quản lý cấu hình không?,x,,,,,
"Có mô tả chế độ, số bản và máy backup không?",x,,,,,
Có mô tả chế độ backup database của dự án không?,x,,,,,
5.5 Các hoạt động quản lý cấu hình,,,,,,
Có xác định người quản lý yêu cầu của dự án?,x,,,,,
Có xác định công việc của cán bộ quản lý cấu hình không?,x,,,,,
Có mô tả thời gian cập nhật Requirement–Sheet (RMSheet) không? ,x,,,,,
"Có mô tả thời gian baseline CI, baseline dự án không?",x,,,,,
Có mô tả tần suất cập nhật dữ liệu lên VSS không?,x,,,,,
Có xác định baseline audit không?,x,,,,,
6.1.1 Dự kiến nhân lực,,,,,,
Dự án có tiến hành estimation effort không?,x,,,,,
Phương pháp estimation có nằm trong các phương pháp của Navisoft quy định không?,x,,,,,
Dự án có làm file distribution effort không?,x,,,,,
Nhân lực từ file distribution effort mục giai đoạn có đúng với nhân lực trong mục giai đoạn của PP không?,x,,,,,
Nhân lực từ file distribution effort mục công việc (process) có đúng với nhân lực trong mục công việc (process) của PP không?,x,,,,,
Tỷ lệ % của nhân lực theo từng giai đoạn có đúng với hướng dẫn database: 14-HD/PM/HDCV?,x,,,,,
Tỷ lệ % của nhân lực theo từng công việc (process) có đúng với hướng dẫn database: 14-HD/PM/HDCV?,x,,,,,
Nếu không đúng chúng có được ghi nhận trong mục thay đổi 4.1.2 PP không?,x,,,,,
Tên giai đoạn có đúng với tên giai đoạn ở mục 4.2.2 không?,x,,,,,
Thời gian của giai đoạn có đúng là ngày kết thúc giai đoạn-ngày bắt đầu giai đoạn +1 của mục 4.2.2 không?,x,,,,,
Số người có đúng bằng số người tham gia trong giai đoạn đó không?,x,,,,,
Thời gian và số người của công việc có đúng bằng thời gian/ số người ở mục 2.2 không?,x,,,,,
6.1.2 Đào tạo huấn luyện,,,,,,
Có mô tả tên khoá đào tạo không?,,,x,,,
Có xác định tên của người tham gia không?,,,x,,,
Có xác định ngày đào tạo không?,,,x,,,
Có xác định phương pháp đào tạo không?,,,x,,,
Có xác định các tiêu chuẩn miễn tham gia không?,,,,,,
6.1.3 Thầu phụ,,,,,,
Dự án có nhà thầu phụ không?,,,x,,,
Nếu có thì xác định sản phẩm và ngày ban giao sản phẩm cho nhà thầu phụ không?,,,x,,,
6.2.1 Phần mềm,,,,,,
Dự án có xác định danh sách phần mềm của dự án không?,x,,,,,
Có xác định mục đích của các phần mềm không?,x,,,,,
Phần mềm có tương ứng với công nghệ dự án đang làm không?,x,,,,,
Các phần mềm đã có licence chưa?,x,,,,,
6.2.2 Các thành Phần phần mềm khác,,,,,,
Dự án có sử dụng component nào khác không?,,,x,,,
Có xác định mục đích không?,,,x,,,
Có kế hoạch mua hay phát triển không?,,,,,,
6.2.3 Phần cứng,,,,,,
Dự án có xác định yêu cầu phần cứng để thực hiện dự án không?,x,,,,,
6.2.3 Cơ sở vật chất khác,,,,,,
"Ngoài phần cứng, dự án có cần các máy móc khác để thực hiện sự án không?",,,x,,,
"Nếu có, có ghi nhận cách thức và ngày cần có không?",,,,,,
6.3 Tái sử dụng,,,,,,
Dự án có tái sử dụng lại cái gì của Navisoft không?,x,,,,,
Có ghi nhận mục đích và nguồn sử dụng không? ,x,,,,,
6.4 Sản phẩm khách hàng cung cấp,,,,,,
Dự án có ghi nhận sản phẩm do khách hàng cung cấp  không? ,,,x,,,
Có ghi nhận mục đích và ngày bàn giao không? ,,,x,,,
Các sản phẩm này có được ghi nhận xem xét tại mục 3.2.1 không?,,,x,,,
6.5.1 Lịch thu tiền,,,,,,
Các khoản mục thu tiền có đúng với hợp đồng kinh tế không?,x,,,,,
Ngày ban giao hồ sơ thu tiền (thu tiền) có đảm là nhỏ hơn 3 ngày sau khi ngày đủ điều kiện bàn giao?,x,,,,,
Số tiền từng đợt và tổng tiền có đúng theo hợp đồng không?,x,,,,,
Có xác nhận điều kiện để thu tiền không?,x,,,,,
6.5.2 Chi Phí,,,,,,
Có kế hoạch ngân sách / Chi phí cho dự án không?,x,,,,,
7.1 Risk,,,,,,
Có làm risk matrix không?,,,x,làm sau vì cần gửi KH gấp,,
Risk matrix có được điền đầy đủ thông tin không?,,,x,,,
Risk có nằm trong danh sách risk category không?,,,x,,,
7.2 Vấn đề,,,,,,
Có xác định issue của dự án không?,,,,,,
7.3 Estimation,,,,Các câu hỏi này phải được sử dụng để xem xét size trước khi điền số size này vào WO,,
Có estimation sheet không?,,,,,,
Tỉ lệ quy đổi ra effort cho từng loại module có trùng với các gía trị tương ứng trong Estimation Database không?,,,,,,
Nếu hệ số effort không giống như chuẩn thì có được SEPG phê duyệt chưa?,,,,,,
"Có module nào được đánh dấu ""x"" trên nhiều cột không?",,,,,,
<Thêm tiếp nếu cần>,,,,,,
,,0,,0,,
* Nhận xét,,,,,,
,,,,,,
,,,,,,
,,,,,,
,,,,,,
* Đề xuất,,,,,,
,,,,,,
[X] - Đạt,,,,,,
[   ] - Xem xét lại,,,,,,
[   ] - Khác,,,,,,
