{"info": {"_postman_id": "c3d4e5f6-g7h8-9012-ijkl-mn3456789012", "name": "HieuThuoc - TonKho API Tests", "description": "Collection for testing TonKhoController APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Search TonKho", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm tồn kho với phân trang"}, "response": []}, {"name": "Search TonKho By <PERSON><PERSON><PERSON> Name", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tenThuoc\": \"Paracetamol\",\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm tồn kho theo tên thuốc"}, "response": []}, {"name": "Search TonKho By SoLo", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"soLo\": \"L001\",\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> ki<PERSON>m tồn kho theo số lô"}, "response": []}, {"name": "Search TonKho By NhaSanXuat Name", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tenNhaSanXuat\": \"ABC\",\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm tồn kho theo tên nhà sản xuất"}, "response": []}, {"name": "Search TonKho - Invalid Page", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 999,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm tồn kho với trang không tồn tại"}, "response": []}, {"name": "Search TonKho - Invalid Page Size", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 0,\n  \"size\": 0\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm tồn kho với kích thư<PERSON><PERSON> trang không hợp lệ"}, "response": []}, {"name": "Search TonKho - Invalid Sort Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 0,\n  \"size\": 20,\n  \"sortedField\": \"fieldNotExist\"\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/search", "host": ["{{baseUrl}}"], "path": ["tonkho", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm tồn kho với sắp xếp theo trường không tồn tại"}, "response": []}, {"name": "Update <PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"thuocId\": 1,\n  \"soLo\": \"L001\",\n  \"hanSuDung\": \"2025-12-31\",\n  \"soLuong\": 200,\n  \"viTri\": \"Kệ A-01\"\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/update", "host": ["{{baseUrl}}"], "path": ["tonkho", "update"]}, "description": "<PERSON><PERSON><PERSON> nh<PERSON>t tồn kho"}, "response": []}, {"name": "Update TonKho - Not Found", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 999,\n  \"thuocId\": 1,\n  \"soLo\": \"L001\",\n  \"hanSuDung\": \"2025-12-31\",\n  \"soLuong\": 200,\n  \"viTri\": \"Kệ A-01\"\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/update", "host": ["{{baseUrl}}"], "path": ["tonkho", "update"]}, "description": "<PERSON><PERSON><PERSON> nh<PERSON>t tồn kho không tồn tại"}, "response": []}, {"name": "Update <PERSON>n<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"thuocId\": 999,\n  \"soLo\": \"L001\",\n  \"hanSuDung\": \"2025-12-31\",\n  \"soLuong\": 200,\n  \"viTri\": \"Kệ A-01\"\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/update", "host": ["{{baseUrl}}"], "path": ["tonkho", "update"]}, "description": "<PERSON><PERSON><PERSON> nh<PERSON>t tồn kho với thuốc không tồn tại"}, "response": []}, {"name": "Update TonKho - Negative Quantity", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"thuocId\": 1,\n  \"soLo\": \"L001\",\n  \"hanSuDung\": \"2025-12-31\",\n  \"soLuong\": -100,\n  \"viTri\": \"Kệ A-01\"\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/update", "host": ["{{baseUrl}}"], "path": ["tonkho", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật tồn kho với số lượng âm"}, "response": []}, {"name": "Update TonKho - Invalid Expiry Date", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"thuocId\": 1,\n  \"soLo\": \"L001\",\n  \"hanSuDung\": \"2020-12-31\",\n  \"soLuong\": 200,\n  \"viTri\": \"Kệ A-01\"\n}"}, "url": {"raw": "{{baseUrl}}/tonkho/update", "host": ["{{baseUrl}}"], "path": ["tonkho", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật tồn kho với hạn sử dụng không hợp lệ"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}]}