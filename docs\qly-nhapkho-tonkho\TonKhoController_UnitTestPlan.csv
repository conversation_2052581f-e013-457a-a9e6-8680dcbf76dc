Test Case ID,Method,Purpose,Test Setup,Input,Expected Result,Actual Result,Error Details,Evaluation
TK-UT-01,updateT<PERSON><PERSON><PERSON>(),<PERSON><PERSON><PERSON> tra phương thức updateTon<PERSON>ho() cập nhật tồn kho thành công,"Mock TonKhoService.update(tonKhoDTO) trả về ResponseDTO với status=200, msg=""Thành công."", data=tồn kho sau khi cập nhật","TonKhoDTO với id=1, thuocId=1, soLo=""L001"", hanSuDung=2025-12-31, soLuong=100, viTri=""Kệ A1""","Controller gọi tonKhoService.update(tonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thành công."", data=tồn kho sau khi cập nhật",,, 
TK-UT-02,update<PERSON><PERSON><PERSON><PERSON>(),<PERSON><PERSON><PERSON> tra phương thức updateTon<PERSON>ho() khi service báo lỗi không tìm thấy tồn kho,"Mock TonKhoService.update(tonKhoDTO) trả về ResponseDTO với status=404, msg=""Tồn kho không tồn tại."", data=null","TonKhoDTO với id=999 (không tồn tại), thuocId=1, soLo=""L001""","Controller gọi tonKhoService.update(tonKhoDTO) và trả về ResponseDTO với status=404, msg=""Tồn kho không tồn tại."", data=null",,, 
TK-UT-03,updateTonKho(),Kiểm tra phương thức updateTonKho() khi service ném ra exception,"Mock TonKhoService.update(tonKhoDTO) ném ra RuntimeException với message ""Database error""","TonKhoDTO với id=1, thuocId=1, soLo=""L001""",Controller bắt exception và trả về ResponseDTO với status=500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
TK-UT-04,updateTonKho(),Kiểm tra phương thức updateTonKho() khi service ném ra exception do thuốc không tồn tại,"Mock TonKhoService.update(tonKhoDTO) ném ra RuntimeException với message ""Thuốc không tồn tại""","TonKhoDTO với id=1, thuocId=999 (không tồn tại), soLo=""L001""",Controller bắt exception và trả về ResponseDTO với status=500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
TK-UT-05,search(),Kiểm tra phương thức search() trả về danh sách tồn kho,"Mock TonKhoService.search(searchTonKhoDTO) trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa danh sách tồn kho","SearchTonKhoDTO với tenThuoc=""Paracetamol"", soLo=""L001"", tenNhaSanXuat=""ABC"", currentPage=0, size=10","Controller gọi tonKhoService.search(searchTonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa danh sách tồn kho",,, 
TK-UT-06,search(),Kiểm tra phương thức search() khi service trả về danh sách rỗng,"Mock TonKhoService.search(searchTonKhoDTO) trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa danh sách rỗng","SearchTonKhoDTO với tenThuoc=""XYZ"" (không tồn tại), currentPage=0, size=10","Controller gọi tonKhoService.search(searchTonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa danh sách rỗng",,, 
TK-UT-07,search(),Kiểm tra phương thức search() khi service ném ra exception,"Mock TonKhoService.search(searchTonKhoDTO) ném ra RuntimeException với message ""Database error""","SearchTonKhoDTO với tenThuoc=""Paracetamol"", currentPage=0, size=10",Controller bắt exception và trả về ResponseDTO với status=500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
TK-UT-08,search(),Kiểm tra phương thức search() với tên thuốc rỗng,"Mock TonKhoService.search(searchTonKhoDTO) trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa tất cả tồn kho","SearchTonKhoDTO với tenThuoc="""", currentPage=0, size=10","Controller gọi tonKhoService.search(searchTonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa tất cả tồn kho",,, 
TK-UT-09,search(),Kiểm tra phương thức search() với số lô rỗng,"Mock TonKhoService.search(searchTonKhoDTO) trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa tất cả tồn kho","SearchTonKhoDTO với soLo="""", currentPage=0, size=10","Controller gọi tonKhoService.search(searchTonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa tất cả tồn kho",,, 
TK-UT-10,search(),Kiểm tra phương thức search() với tên nhà sản xuất rỗng,"Mock TonKhoService.search(searchTonKhoDTO) trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa tất cả tồn kho","SearchTonKhoDTO với tenNhaSanXuat="""", currentPage=0, size=10","Controller gọi tonKhoService.search(searchTonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa tất cả tồn kho",,, 
TK-UT-11,search(),Kiểm tra phương thức search() với phân trang,"Mock TonKhoService.search(searchTonKhoDTO) trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa danh sách tồn kho trang thứ 2","SearchTonKhoDTO với tenThuoc=""Paracetamol"", currentPage=1, size=5","Controller gọi tonKhoService.search(searchTonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa danh sách tồn kho trang thứ 2",,, 
TK-UT-12,Kiểm tra tích hợp,Kiểm tra tích hợp giữa TonKhoController và TonKhoService,"Sử dụng TonKhoService thật (không mock), nhưng mock các repository","TonKhoDTO với id=1, thuocId=1, soLo=""L001"", hanSuDung=2025-12-31, soLuong=100, viTri=""Kệ A1""","Controller gọi tonKhoService.update(tonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thành công."", data=tồn kho sau khi cập nhật",,, 
TK-UT-13,Kiểm tra tích hợp,Kiểm tra tích hợp giữa TonKhoController và TonKhoService.search(),"Sử dụng TonKhoService thật (không mock), nhưng mock các repository","SearchTonKhoDTO với tenThuoc=""Paracetamol"", currentPage=0, size=10","Controller gọi tonKhoService.search(searchTonKhoDTO) và trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa danh sách tồn kho",,, 
TK-UT-14,Kiểm tra tích hợp,Kiểm tra tích hợp giữa TonKhoController và TonKhoRepo,"Sử dụng TonKhoService và TonKhoRepo thật (không mock), nhưng mock các repository khác","SearchTonKhoDTO với tenThuoc=""Paracetamol"", currentPage=0, size=10","Controller gọi tonKhoService.search(searchTonKhoDTO), service gọi tonKhoRepo.search() và trả về ResponseDTO với status=200, msg=""Thanh công"", data=PageDTO chứa danh sách tồn kho",,, 
