<PERSON><PERSON> testcase,<PERSON><PERSON><PERSON> đích kiểm tra,Input đầu vào,<PERSON><PERSON><PERSON> qu<PERSON> mong muốn,<PERSON><PERSON><PERSON> quả hiện tại,<PERSON> tiế<PERSON> lỗi,<PERSON><PERSON><PERSON> gi<PERSON>

# NhaCungCapController
NCC-TC-01,<PERSON><PERSON><PERSON> danh sách nhà cung cấp thành công,GET /hieuthuoc/nhacungcap/list,Status: 200; Msg: "Thành công"; Data: <PERSON>h sách nhà cung cấp,,,
NCC-TC-02,<PERSON><PERSON><PERSON> danh sách nhà cung cấp khi không có dữ liệu,GET /hieuthuoc/nhacungcap/list,Status: 200; Msg: "Thành công"; Data: <PERSON><PERSON> sách rỗng,,,
NCC-TC-03,Tìm kiếm nhà cung cấp theo tên thành công,GET /hieuthuoc/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=ABC,Status: 200; Msg: "Thành công"; Data: <PERSON><PERSON> s<PERSON>ch nhà cung cấp có tên chứa "ABC",,,
NCC-TC-04,<PERSON><PERSON><PERSON> kiếm nhà cung cấp theo tên không tồn tại,GET /hieuthuoc/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=XYZ,Status: 409; Msg: "Nhà cung cấp không tồn tại",,,
NCC-TC-05,Tìm kiếm nhà cung cấp với tên rỗng,GET /hieuthuoc/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=,Status: 409; Msg: "Nhà cung cấp không tồn tại",,,
NCC-TC-06,Tìm kiếm nhà cung cấp với tên chứa ký tự đặc biệt,GET /hieuthuoc/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=ABC@#$,Status: 409; Msg: "Nhà cung cấp không tồn tại",,,
NCC-TC-07,Tạo mới nhà cung cấp thành công,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""maNCC"": ""NCC001"",
  ""tenNhaCungCap"": ""Nhà cung cấp 1"",
  ""diaChi"": ""Địa chỉ 1"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 201; Msg: "Thành công"; Data: Thông tin nhà cung cấp vừa tạo,,,
NCC-TC-08,Tạo mới nhà cung cấp với mã đã tồn tại,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""maNCC"": ""NCC001"",
  ""tenNhaCungCap"": ""Nhà cung cấp 2"",
  ""diaChi"": ""Địa chỉ 2"",
  ""soDienThoai"": ""0987654321"",
  ""email"": ""<EMAIL>""
}",Status: 409; Msg: "Nhà cung cấp đã tồn tại",,,
NCC-TC-09,Tạo mới nhà cung cấp với dữ liệu không hợp lệ,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""maNCC"": ""NCC003"",
  ""tenNhaCungCap"": ""Nhà cung cấp 3"",
  ""diaChi"": ""Địa chỉ 3"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""invalid-email""
}",Status: 400; Lỗi validation,,,
NCC-TC-10,Tạo mới nhà cung cấp với thiếu thông tin bắt buộc,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""tenNhaCungCap"": ""Nhà cung cấp 4"",
  ""diaChi"": ""Địa chỉ 4"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 400; Lỗi validation,,,
NCC-TC-11,Cập nhật thông tin nhà cung cấp thành công,"PUT /hieuthuoc/nhacungcap/update
Body: {
  ""id"": 1,
  ""maNCC"": ""NCC001"",
  ""tenNhaCungCap"": ""Nhà cung cấp 1 Updated"",
  ""diaChi"": ""Địa chỉ 1 Updated"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 200; Msg: "Thành công"; Data: Thông tin nhà cung cấp sau khi cập nhật,,,
NCC-TC-12,Cập nhật thông tin nhà cung cấp không tồn tại,"PUT /hieuthuoc/nhacungcap/update
Body: {
  ""id"": 999,
  ""maNCC"": ""NCC999"",
  ""tenNhaCungCap"": ""Nhà cung cấp không tồn tại"",
  ""diaChi"": ""Địa chỉ"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 404; Msg: "Không tìm thấy nhà cung cấp",,,
NCC-TC-13,Cập nhật thông tin nhà cung cấp với mã đã tồn tại,"PUT /hieuthuoc/nhacungcap/update
Body: {
  ""id"": 1,
  ""maNCC"": ""NCC002"",
  ""tenNhaCungCap"": ""Nhà cung cấp 1 Updated"",
  ""diaChi"": ""Địa chỉ 1 Updated"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 409; Msg: "Mã Nhà cung cấp đã tồn tại",,,
NCC-TC-14,Cập nhật thông tin nhà cung cấp với dữ liệu không hợp lệ,"PUT /hieuthuoc/nhacungcap/update
Body: {
  ""id"": 1,
  ""maNCC"": ""NCC001"",
  ""tenNhaCungCap"": ""Nhà cung cấp 1 Updated"",
  ""diaChi"": ""Địa chỉ 1 Updated"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""invalid-email""
}",Status: 400; Lỗi validation,,,
NCC-TC-15,Xóa nhà cung cấp thành công,DELETE /hieuthuoc/nhacungcap/delete?id=1,Status: 200; Msg: "Thành công",,,
NCC-TC-16,Xóa nhà cung cấp không tồn tại,DELETE /hieuthuoc/nhacungcap/delete?id=999,Status: 404; Msg: "Không tìm thấy nhà cung cấp",,,
NCC-TC-17,Xóa nhà cung cấp với ID không hợp lệ,DELETE /hieuthuoc/nhacungcap/delete?id=abc,Status: 400; Lỗi validation,,,
NCC-TC-18,Tạo mới nhà cung cấp với số điện thoại không hợp lệ,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""maNCC"": ""NCC005"",
  ""tenNhaCungCap"": ""Nhà cung cấp 5"",
  ""diaChi"": ""Địa chỉ 5"",
  ""soDienThoai"": ""abc123"",
  ""email"": ""<EMAIL>""
}",Status: 400; Lỗi validation,,,
NCC-TC-19,Tạo mới nhà cung cấp với địa chỉ quá dài,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""maNCC"": ""NCC006"",
  ""tenNhaCungCap"": ""Nhà cung cấp 6"",
  ""diaChi"": ""Địa chỉ quá dài vượt quá giới hạn cho phép của trường dữ liệu trong cơ sở dữ liệu. Địa chỉ quá dài vượt quá giới hạn cho phép của trường dữ liệu trong cơ sở dữ liệu. Địa chỉ quá dài vượt quá giới hạn cho phép của trường dữ liệu trong cơ sở dữ liệu."",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 400; Lỗi validation,,,
NCC-TC-20,Tìm kiếm nhà cung cấp với tên có ký tự Unicode,"GET /hieuthuoc/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=Dược phẩm",Status: 200; Msg: "Thành công"; Data: Danh sách nhà cung cấp có tên chứa "Dược phẩm",,,
NCC-TC-21,Xóa nhà cung cấp đã có liên kết với phiếu nhập,DELETE /hieuthuoc/nhacungcap/delete?id=1,Status: 409; Msg: "Không thể xóa nhà cung cấp đã có liên kết với phiếu nhập",,,
NCC-TC-22,Tạo mới nhà cung cấp với tên trùng lặp,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""maNCC"": ""NCC007"",
  ""tenNhaCungCap"": ""Nhà cung cấp 1"",
  ""diaChi"": ""Địa chỉ 7"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 409; Msg: "Tên nhà cung cấp đã tồn tại",,,
NCC-TC-23,Tạo mới nhà cung cấp với email trùng lặp,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""maNCC"": ""NCC008"",
  ""tenNhaCungCap"": ""Nhà cung cấp 8"",
  ""diaChi"": ""Địa chỉ 8"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 409; Msg: "Email nhà cung cấp đã tồn tại",,,
NCC-TC-24,Tạo mới nhà cung cấp với số điện thoại trùng lặp,"POST /hieuthuoc/nhacungcap/create
Body: {
  ""maNCC"": ""NCC009"",
  ""tenNhaCungCap"": ""Nhà cung cấp 9"",
  ""diaChi"": ""Địa chỉ 9"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 409; Msg: "Số điện thoại nhà cung cấp đã tồn tại",,,
NCC-TC-25,Tìm kiếm nhà cung cấp với tên quá ngắn,"GET /hieuthuoc/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=A",Status: 400; Msg: "Từ khóa tìm kiếm quá ngắn",,,
NCC-TC-26,Cập nhật nhà cung cấp với email không đúng định dạng,"PUT /hieuthuoc/nhacungcap/update
Body: {
  ""id"": 1,
  ""maNCC"": ""NCC001"",
  ""tenNhaCungCap"": ""Nhà cung cấp 1"",
  ""diaChi"": ""Địa chỉ 1"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""invalid.email@""
}",Status: 400; Lỗi validation,,,
NCC-TC-27,Cập nhật nhà cung cấp với mã NCC chứa ký tự đặc biệt,"PUT /hieuthuoc/nhacungcap/update
Body: {
  ""id"": 1,
  ""maNCC"": ""NCC001@#$"",
  ""tenNhaCungCap"": ""Nhà cung cấp 1"",
  ""diaChi"": ""Địa chỉ 1"",
  ""soDienThoai"": ""0123456789"",
  ""email"": ""<EMAIL>""
}",Status: 400; Lỗi validation,,,

# PhieuNhapController
PN-TC-01,Tìm kiếm phiếu nhập thành công,"POST /hieuthuoc/phieunhap/search
Body: {
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách phiếu nhập,,,
PN-TC-02,Tìm kiếm phiếu nhập theo tên nhà cung cấp,"POST /hieuthuoc/phieunhap/search
Body: {
  ""keyWord"": ""ABC"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách phiếu nhập của nhà cung cấp có tên chứa "ABC",,,
PN-TC-03,Tìm kiếm phiếu nhập với trang không tồn tại,"POST /hieuthuoc/phieunhap/search
Body: {
  ""currentPage"": 999,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO với danh sách rỗng,,,
PN-TC-04,Tìm kiếm phiếu nhập với kích thước trang âm,"POST /hieuthuoc/phieunhap/search
Body: {
  ""currentPage"": 0,
  ""size"": -1
}",Status: 400; Lỗi validation,,,
PN-TC-05,Tìm kiếm phiếu nhập với kích thước trang không hợp lệ,"POST /hieuthuoc/phieunhap/search
Body: {
  ""currentPage"": 0,
  ""size"": 0
}",Status: 400; Lỗi validation,,,
PN-TC-06,Tìm kiếm phiếu nhập với sắp xếp theo trường không tồn tại,"POST /hieuthuoc/phieunhap/search
Body: {
  ""currentPage"": 0,
  ""size"": 20,
  ""sortedField"": ""fieldNotExist""
}",Status: 500; Lỗi khi sắp xếp theo trường không tồn tại,,,
PN-TC-07,Lấy thông tin phiếu nhập theo ID thành công,GET /hieuthuoc/phieunhap/get?id=1,Status: 200; Msg: "Thành công"; Data: Thông tin phiếu nhập có ID=1,,,
PN-TC-08,Lấy thông tin phiếu nhập với ID không tồn tại,GET /hieuthuoc/phieunhap/get?id=999,Status: 409; Msg: "Không tìm thấy phiếu nhập",,,
PN-TC-09,Lấy thông tin phiếu nhập với ID không hợp lệ,GET /hieuthuoc/phieunhap/get?id=abc,Status: 400; Lỗi validation,,,
PN-TC-10,Tạo phiếu nhập mới thành công,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 1000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 200; Msg: "Thành công"; Data: Thông tin phiếu nhập vừa tạo,,,
PN-TC-11,Tạo phiếu nhập với nhà cung cấp không tồn tại,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 999,
  ""nguoiDungId"": 1,
  ""tongTien"": 1000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 404; Msg: "Không tìm thấy nhà cung cấp",,,
PN-TC-12,Tạo phiếu nhập với người dùng không tồn tại,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 999,
  ""tongTien"": 1000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 404; Msg: "Không tìm thấy người dùng",,,
PN-TC-13,Tạo phiếu nhập với thuốc không tồn tại,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 1000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 999,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 404; Msg: "Không tìm thấy thuốc",,,
PN-TC-14,Tạo phiếu nhập với dữ liệu không hợp lệ,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": -1000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": -10,
      ""donGia"": -1000
    }
  ]
}",Status: 400; Lỗi validation,,,
PN-TC-15,Tạo phiếu nhập với chi tiết phiếu nhập rỗng,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 0,
  ""chiTietPhieuNhaps"": []
}",Status: 400; Msg: "Chi tiết phiếu nhập không được rỗng",,,
PN-TC-16,Cập nhật thông tin phiếu nhập thành công,"PUT /hieuthuoc/phieunhap/update
Body: {
  ""id"": 1,
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 2000000,
  ""chiTietPhieuNhaps"": [
    {
      ""id"": 1,
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 200,
      ""donGia"": 10000
    }
  ]
}",Status: 200; Msg: "Thành công"; Data: Thông tin phiếu nhập sau khi cập nhật,,,
PN-TC-17,Cập nhật thông tin phiếu nhập không tồn tại,"PUT /hieuthuoc/phieunhap/update
Body: {
  ""id"": 999,
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 1000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 404; Msg: "Không tìm thấy phiếu nhập",,,
PN-TC-18,Cập nhật thông tin phiếu nhập với nhà cung cấp không tồn tại,"PUT /hieuthuoc/phieunhap/update
Body: {
  ""id"": 1,
  ""nhaCungCapId"": 999,
  ""nguoiDungId"": 1,
  ""tongTien"": 1000000,
  ""chiTietPhieuNhaps"": [
    {
      ""id"": 1,
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 404; Msg: "Không tìm thấy nhà cung cấp",,,
PN-TC-19,Xóa phiếu nhập thành công,DELETE /hieuthuoc/phieunhap/delete?id=1,Status: 200; Msg: "Thành công",,,
PN-TC-20,Xóa phiếu nhập không tồn tại,DELETE /hieuthuoc/phieunhap/delete?id=999,Status: 404; Msg: "Không tìm thấy phiếu nhập",,,
PN-TC-21,Xóa phiếu nhập với ID không hợp lệ,DELETE /hieuthuoc/phieunhap/delete?id=abc,Status: 400; Lỗi validation,,,
PN-TC-22,Xóa phiếu nhập đã có liên kết với tồn kho,DELETE /hieuthuoc/phieunhap/delete?id=1,Status: 409; Msg: "Không thể xóa phiếu nhập đã có liên kết với tồn kho",,,
PN-TC-23,Tạo phiếu nhập với nhiều chi tiết phiếu nhập,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 3000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    },
    {
      ""thuocId"": 2,
      ""hanSuDung"": ""2025-10-31"",
      ""soLuong"": 50,
      ""donGia"": 20000
    },
    {
      ""thuocId"": 3,
      ""hanSuDung"": ""2026-01-31"",
      ""soLuong"": 200,
      ""donGia"": 5000
    }
  ]
}",Status: 200; Msg: "Thành công"; Data: Thông tin phiếu nhập vừa tạo,,,
PN-TC-24,Tạo phiếu nhập với hạn sử dụng đã qua,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 1000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2020-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 400; Msg: "Hạn sử dụng không hợp lệ",,,
PN-TC-25,Tạo phiếu nhập với tổng tiền không khớp với chi tiết,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 2000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 400; Msg: "Tổng tiền không khớp với chi tiết phiếu nhập",,,
PN-TC-26,Tìm kiếm phiếu nhập theo khoảng thời gian,"POST /hieuthuoc/phieunhap/search
Body: {
  ""fromDate"": ""2023-01-01"",
  ""toDate"": ""2023-12-31"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách phiếu nhập trong khoảng thời gian,,,
PN-TC-27,Tìm kiếm phiếu nhập với khoảng thời gian không hợp lệ,"POST /hieuthuoc/phieunhap/search
Body: {
  ""fromDate"": ""2023-12-31"",
  ""toDate"": ""2023-01-01"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 400; Msg: "Khoảng thời gian không hợp lệ",,,
PN-TC-28,Tạo phiếu nhập với số lượng thuốc bằng 0,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 0,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 0,
      ""donGia"": 10000
    }
  ]
}",Status: 400; Msg: "Số lượng thuốc phải lớn hơn 0",,,
PN-TC-29,Tạo phiếu nhập với đơn giá bằng 0,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 0,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 0
    }
  ]
}",Status: 400; Msg: "Đơn giá phải lớn hơn 0",,,
PN-TC-30,Tạo phiếu nhập với định dạng ngày không hợp lệ,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 1000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""31-12-2025"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 400; Lỗi validation,,,
PN-TC-31,Tạo phiếu nhập với nhiều thuốc trùng nhau,"POST /hieuthuoc/phieunhap/create
Body: {
  ""nhaCungCapId"": 1,
  ""nguoiDungId"": 1,
  ""tongTien"": 2000000,
  ""chiTietPhieuNhaps"": [
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    },
    {
      ""thuocId"": 1,
      ""hanSuDung"": ""2025-12-31"",
      ""soLuong"": 100,
      ""donGia"": 10000
    }
  ]
}",Status: 400; Msg: "Thuốc trùng lặp trong phiếu nhập",,,
PN-TC-32,Tìm kiếm phiếu nhập theo mã phiếu nhập,"POST /hieuthuoc/phieunhap/search
Body: {
  ""maPhieuNhap"": ""PN001"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách phiếu nhập có mã phiếu nhập chứa "PN001",,,
PN-TC-33,Tìm kiếm phiếu nhập theo ID nhà cung cấp,"POST /hieuthuoc/phieunhap/search
Body: {
  ""nhaCungCapId"": 1,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách phiếu nhập của nhà cung cấp có ID=1,,,
PN-TC-34,Tìm kiếm phiếu nhập theo ID người dùng,"POST /hieuthuoc/phieunhap/search
Body: {
  ""nguoiDungId"": 1,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách phiếu nhập của người dùng có ID=1,,,

# TonKhoController
TK-TC-01,Tìm kiếm tồn kho thành công,"POST /hieuthuoc/tonkho/search
Body: {
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho,,,
TK-TC-02,Tìm kiếm tồn kho theo tên thuốc,"POST /hieuthuoc/tonkho/search
Body: {
  ""tenThuoc"": ""Paracetamol"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho của thuốc có tên chứa "Paracetamol",,,
TK-TC-03,Tìm kiếm tồn kho theo số lô,"POST /hieuthuoc/tonkho/search
Body: {
  ""soLo"": ""L001"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho có số lô chứa "L001",,,
TK-TC-04,Tìm kiếm tồn kho theo tên nhà sản xuất,"POST /hieuthuoc/tonkho/search
Body: {
  ""tenNhaSanXuat"": ""ABC"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho của thuốc có nhà sản xuất chứa "ABC",,,
TK-TC-05,Tìm kiếm tồn kho với trang không tồn tại,"POST /hieuthuoc/tonkho/search
Body: {
  ""currentPage"": 999,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO với danh sách rỗng,,,
TK-TC-06,Tìm kiếm tồn kho với kích thước trang âm,"POST /hieuthuoc/tonkho/search
Body: {
  ""currentPage"": 0,
  ""size"": -1
}",Status: 400; Lỗi validation,,,
TK-TC-07,Tìm kiếm tồn kho với kích thước trang không hợp lệ,"POST /hieuthuoc/tonkho/search
Body: {
  ""currentPage"": 0,
  ""size"": 0
}",Status: 400; Lỗi validation,,,
TK-TC-08,Tìm kiếm tồn kho với sắp xếp theo trường không tồn tại,"POST /hieuthuoc/tonkho/search
Body: {
  ""currentPage"": 0,
  ""size"": 20,
  ""sortedField"": ""fieldNotExist""
}",Status: 500; Lỗi khi sắp xếp theo trường không tồn tại,,,
TK-TC-09,Cập nhật tồn kho thành công,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 1,
  ""soLo"": ""L001"",
  ""hanSuDung"": ""2025-12-31"",
  ""soLuong"": 200,
  ""viTri"": ""Kệ A-01""
}",Status: 200; Msg: "Thành công"; Data: Thông tin tồn kho sau khi cập nhật,,,
TK-TC-10,Cập nhật tồn kho không tồn tại,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 999,
  ""thuocId"": 1,
  ""soLo"": ""L001"",
  ""hanSuDung"": ""2025-12-31"",
  ""soLuong"": 200,
  ""viTri"": ""Kệ A-01""
}",Status: 404; Msg: "Không tìm thấy tồn kho",,,
TK-TC-11,Cập nhật tồn kho với thuốc không tồn tại,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 999,
  ""soLo"": ""L001"",
  ""hanSuDung"": ""2025-12-31"",
  ""soLuong"": 200,
  ""viTri"": ""Kệ A-01""
}",Status: 404; Msg: "Không tìm thấy thuốc",,,
TK-TC-12,Cập nhật tồn kho với số lượng âm,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 1,
  ""soLo"": ""L001"",
  ""hanSuDung"": ""2025-12-31"",
  ""soLuong"": -100,
  ""viTri"": ""Kệ A-01""
}",Status: 400; Lỗi validation,,,
TK-TC-13,Cập nhật tồn kho với hạn sử dụng không hợp lệ,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 1,
  ""soLo"": ""L001"",
  ""hanSuDung"": ""2020-12-31"",
  ""soLuong"": 200,
  ""viTri"": ""Kệ A-01""
}",Status: 400; Msg: "Hạn sử dụng không hợp lệ",,,
TK-TC-14,Cập nhật tồn kho với số lô trùng lặp,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 1,
  ""soLo"": ""L002"",
  ""hanSuDung"": ""2025-12-31"",
  ""soLuong"": 200,
  ""viTri"": ""Kệ A-01""
}",Status: 409; Msg: "Số lô đã tồn tại cho thuốc này",,,
TK-TC-15,Cập nhật tồn kho với vị trí quá dài,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 1,
  ""soLo"": ""L001"",
  ""hanSuDung"": ""2025-12-31"",
  ""soLuong"": 200,
  ""viTri"": ""Kệ A-01 với mô tả vị trí quá dài vượt quá giới hạn cho phép của trường dữ liệu trong cơ sở dữ liệu""
}",Status: 400; Lỗi validation,,,
TK-TC-16,Tìm kiếm tồn kho theo hạn sử dụng,"POST /hieuthuoc/tonkho/search
Body: {
  ""fromDate"": ""2023-01-01"",
  ""toDate"": ""2025-12-31"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho có hạn sử dụng trong khoảng thời gian,,,
TK-TC-17,Tìm kiếm tồn kho theo nhiều tiêu chí kết hợp,"POST /hieuthuoc/tonkho/search
Body: {
  ""tenThuoc"": ""Paracetamol"",
  ""soLo"": ""L001"",
  ""tenNhaSanXuat"": ""ABC"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho thỏa mãn tất cả các điều kiện,,,
TK-TC-18,Tìm kiếm tồn kho sắp hết hạn,"POST /hieuthuoc/tonkho/search
Body: {
  ""sapHetHan"": true,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho sắp hết hạn,,,
TK-TC-19,Tìm kiếm tồn kho sắp hết,"POST /hieuthuoc/tonkho/search
Body: {
  ""sapHet"": true,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho sắp hết,,,
TK-TC-20,Tìm kiếm tồn kho đã hết hạn,"POST /hieuthuoc/tonkho/search
Body: {
  ""daHetHan"": true,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho đã hết hạn,,,
TK-TC-21,Cập nhật tồn kho với định dạng ngày không hợp lệ,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 1,
  ""soLo"": ""L001"",
  ""hanSuDung"": ""31-12-2025"",
  ""soLuong"": 200,
  ""viTri"": ""Kệ A-01""
}",Status: 400; Lỗi validation,,,
TK-TC-22,Cập nhật tồn kho với số lô chứa ký tự đặc biệt,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 1,
  ""soLo"": ""L001@#$"",
  ""hanSuDung"": ""2025-12-31"",
  ""soLuong"": 200,
  ""viTri"": ""Kệ A-01""
}",Status: 400; Lỗi validation,,,
TK-TC-23,Tìm kiếm tồn kho với sắp xếp theo số lượng tăng dần,"POST /hieuthuoc/tonkho/search
Body: {
  ""currentPage"": 0,
  ""size"": 20,
  ""sortedField"": ""soLuong"",
  ""isAsc"": true
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho được sắp xếp theo số lượng tăng dần,,,
TK-TC-24,Tìm kiếm tồn kho với sắp xếp theo hạn sử dụng giảm dần,"POST /hieuthuoc/tonkho/search
Body: {
  ""currentPage"": 0,
  ""size"": 20,
  ""sortedField"": ""hanSuDung"",
  ""isAsc"": false
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho được sắp xếp theo hạn sử dụng giảm dần,,,
TK-TC-25,Tìm kiếm tồn kho theo vị trí,"POST /hieuthuoc/tonkho/search
Body: {
  ""viTri"": ""Kệ A"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho có vị trí chứa "Kệ A",,,
TK-TC-26,Tìm kiếm tồn kho theo khoảng số lượng,"POST /hieuthuoc/tonkho/search
Body: {
  ""minSoLuong"": 10,
  ""maxSoLuong"": 100,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho có số lượng từ 10 đến 100,,,
TK-TC-27,Tìm kiếm tồn kho với khoảng số lượng không hợp lệ,"POST /hieuthuoc/tonkho/search
Body: {
  ""minSoLuong"": 100,
  ""maxSoLuong"": 10,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 400; Msg: "Khoảng số lượng không hợp lệ",,,
TK-TC-28,Tìm kiếm tồn kho theo ID thuốc,"POST /hieuthuoc/tonkho/search
Body: {
  ""thuocId"": 1,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho của thuốc có ID=1,,,
TK-TC-29,Tìm kiếm tồn kho theo ID thuốc không tồn tại,"POST /hieuthuoc/tonkho/search
Body: {
  ""thuocId"": 999,
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO với danh sách rỗng,,,
TK-TC-30,Tìm kiếm tồn kho theo nhiều tiêu chí phức tạp,"POST /hieuthuoc/tonkho/search
Body: {
  ""tenThuoc"": ""Paracetamol"",
  ""soLo"": ""L001"",
  ""tenNhaSanXuat"": ""ABC"",
  ""fromDate"": ""2023-01-01"",
  ""toDate"": ""2025-12-31"",
  ""minSoLuong"": 10,
  ""maxSoLuong"": 100,
  ""viTri"": ""Kệ A"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho thỏa mãn tất cả các điều kiện,,,
TK-TC-31,Cập nhật tồn kho với số lượng quá lớn,"PUT /hieuthuoc/tonkho/update
Body: {
  ""id"": 1,
  ""thuocId"": 1,
  ""soLo"": ""L001"",
  ""hanSuDung"": ""2025-12-31"",
  ""soLuong"": 1000000000,
  ""viTri"": ""Kệ A-01""
}",Status: 400; Msg: "Số lượng quá lớn",,,
TK-TC-32,Tìm kiếm tồn kho theo từ khóa tổng hợp,"POST /hieuthuoc/tonkho/search
Body: {
  ""keyWord"": ""Paracetamol L001"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho có tên thuốc hoặc số lô chứa từ khóa,,,
TK-TC-33,Tìm kiếm tồn kho với sắp xếp theo nhiều trường,"POST /hieuthuoc/tonkho/search
Body: {
  ""currentPage"": 0,
  ""size"": 20,
  ""sortedFields"": [""tenThuoc"", ""hanSuDung""],
  ""isAsc"": [true, false]
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho được sắp xếp theo tên thuốc tăng dần, sau đó theo hạn sử dụng giảm dần,,,
TK-TC-34,Tìm kiếm tồn kho theo trạng thái hoạt động,"POST /hieuthuoc/tonkho/search
Body: {
  ""trangThai"": ""ACTIVE"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho có trạng thái hoạt động,,,
TK-TC-35,Tìm kiếm tồn kho theo trạng thái không hoạt động,"POST /hieuthuoc/tonkho/search
Body: {
  ""trangThai"": ""INACTIVE"",
  ""currentPage"": 0,
  ""size"": 20
}",Status: 200; Msg: "Thành công"; Data: PageDTO chứa danh sách tồn kho có trạng thái không hoạt động,,,
