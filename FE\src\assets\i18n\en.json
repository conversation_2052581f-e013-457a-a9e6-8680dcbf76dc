{"common": {"msg.save.succes": "Save success!", "msg.save.fail": "Save fail!", "msg.delete.succes": "Delete success!", "msg.delete.fail": "Delete fail!", "confirm.delete": "Delete confirmation", "msg.update.succes": "Update success!", "msg.update.fail": "Update fail!", "text.status": "Status", "text.search": "Search", "text.total": "Total", "text.no": ".No", "text.delete": "Delete", "text.save": "Save", "text.update": "Update", "text.cancel": "Cancel", "error.required": "You need to enter information", "text.addNew": "Add new", "text.dropFile": "Drop files here or click to upload.", "error.fillFullInfo": "Please fill out enough information!", "text.publish": "Publish", "text.visibility": "Visibility", "text.publish.schedule": "Publish schedule", "text.publish.date": "Publish date", "text.status.published": "Published", "text.status.scheduled": "Scheduled", "text.status.draft": "Draft", "text.visibility.public": "Public", "text.visibility.hidden": "Hidden", "text.submit": "Submit", "text.add": "Add", "text.requiredSelectType": "You need choose at least a type!", "confirm.save": "Save confirmation", "text.exit": "Exit", "text.download": "Download", "text.downloadTemplate": "Download template", "text.selectFile": "Select file", "text.date": "Date", "text.action": "Action", "text.close": "Close"}, "header": {"text.login": "<PERSON><PERSON>", "text.profile": "Profile", "text.logout": "Logout", "text.notification": "Notifications", "text.unreadNoti": "unread messages", "text.viewAllNoti": "View all notifications", "text.noNoti": "Hey! You have no any notifications."}, "sidebarleft": {"text.menu": "<PERSON><PERSON>", "text.artice": "Article", "text.articleCate": "Article category", "text.course": "Course", "text.test": "Test", "text.testType": "Test type", "text.practice": "Practice"}, "article": {"text.artice": "Article", "text.category": "Category", "text.title": "Title", "text.add": "Add article", "text.articeCate": "Article category", "text.addArticeCate": "Add article category", "text.desc": "Description", "confirm.delete.articleCate": "Do you want to delete this article category? ", "text.selectArticleCate": "Select article category", "text.articleThumbnail": "Article thumbnail", "text.content": "Content", "text.typeHere": "Type here..."}, "course": {"text.course": "Course", "text.type": "Type", "text.title": "Title", "text.addCourse": "Add course", "text.desc": "Description", "confirm.delete.course": "Do you want to delete this course?", "text.testOfCourse": "Test of course", "text.updateOrderTest": "Update order test"}, "practice": {"text.practice": "Practice", "text.title": "Title", "text.add": "Add", "text.course": "Course", "confirm.delete.practice": "Do you want to delete this practice?", "text.createPractice": "Create practice", "text.selectCourse": "Select course", "text.fileListen": "File listen", "text.type": "Type", "text.practiceDetail": "Practice detail", "text.info": "Practice your listening comprehension on the same sentences you speak.", "text.info1": "Press Enter to check answer. Press Ctrl+Enter to replay audio.", "text.info2": "Automatically continue on correct"}, "test": {"text.test": "Test", "text.course": "Course", "text.title": "Title", "text.addTest": "Add test", "text.desc": "Description", "confirm.delete.test": "Do you want to delete this test?", "text.questionRead": "Question read", "text.addQuestion": "Add question", "text.type": "Type", "text.quantity": "Quantity", "text.score": "Score", "text.time": "Time", "text.questionListen": "Question listen", "text.selectCourse": "Select course", "text.timeMinute": "Time (minute)", "confirm.delete.testType": "Do you want to delete this test type?", "text.testType": "Test type", "text.addTestType": "Add test type", "text.question": "Question", "text.quantityQuestion": "Quantity question", "text.listen": "Listen", "text.read": "Read", "text.write": "Write", "text.draft": "Draft", "text.import": "Import", "text.export": "Export", "text.save": "Save", "text.fileListen": "File listen", "text.addQuestionGroup": "Add question group", "text.transcript": "Transcript", "text.keyDetail": "Key detail", "confirm.delete.scoreCard": "Do you want to delete this score card?", "text.newScoreCard": "New score card", "text.scoreCard": "Score card", "text.totalQuestion": "Total question", "text.totalScore": "Total score", "text.testDraft": "Test draft", "text.key": "Key", "text.partName": "Part name", "text.currentDivideRequired": "(Current/Required)", "text.testImport": "Test import", "text.viewDetail": "View detail", "text.timeRemain": "Time remain", "text.point": "Point", "text.testResult": "Test result", "text.correct": "Correct", "text.questions": "Questions", "text.wrong": "Wrong", "text.result": "Result", "text.accuracy": "Accuracy", "text.workingTime": "Working time", "text.pass": "Pass", "text.answerCorrect": "Answer correct", "text.showKeyDetail": "Show key detail", "text.showTranscript": "Show transcript", "text.noAnswer": "No answer", "text.detail": "Detail", "text.returnResultPage": "Return result page", "text.showAllTransciptKey": "Show all transcript/ key", "text.testPractice": "Test practice", "text.infoTest": "Information test", "text.topik": "TOPIK", "text.minutes": "minutes", "text.parts": "parts", "text.comments": "Comments", "text.userTested": "User tested", "text.practice": "Practice", "text.part": "Part", "text.fullTest": "Full test", "text.startTest": "Start test", "text.alert": " Ready to start taking the full test? To achieve the best results, you need to spend", "text.alert2": "minutes on this test.", "text.leaveAComment": "Leave a comment", "text.postComment": "Post comment", "text.enterYourComment": "Enter your comment...", "text.reply": "Reply"}}