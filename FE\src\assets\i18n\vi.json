{"common": {"msg.save.succes": "<PERSON><PERSON><PERSON> thành công!", "msg.save.fail": "<PERSON><PERSON><PERSON> không thành công!", "msg.delete.succes": "<PERSON><PERSON>a thành công!", "msg.delete.fail": "<PERSON><PERSON>a không thành công!", "confirm.delete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "msg.update.succes": "<PERSON><PERSON><PERSON> nhật thành công!", "msg.update.fail": "<PERSON><PERSON><PERSON> nhật không thành công!", "text.status": "<PERSON><PERSON><PERSON><PERSON> thái", "text.search": "<PERSON><PERSON><PERSON>", "text.total": "<PERSON><PERSON><PERSON> cộng", "text.no": "STT", "text.delete": "Xóa", "text.save": "<PERSON><PERSON><PERSON>", "text.update": "<PERSON><PERSON><PERSON>", "text.cancel": "<PERSON><PERSON><PERSON>", "error.required": "<PERSON><PERSON><PERSON> cần nhập thông tin", "text.addNew": "<PERSON><PERSON><PERSON><PERSON> mới", "text.dropFile": "<PERSON>h<PERSON> tệp vào đây hoặc nhấp để tải lên.", "error.fillFullInfo": "<PERSON>ui lòng điền đủ thông tin!", "text.publish": "<PERSON><PERSON><PERSON>", "text.visibility": "<PERSON><PERSON><PERSON> năng hiển thị", "text.publish.schedule": "<PERSON><PERSON><PERSON> b<PERSON>", "text.publish.date": "<PERSON><PERSON><PERSON> b<PERSON>", "text.status.published": "<PERSON><PERSON> xu<PERSON> b<PERSON>n", "text.status.scheduled": "<PERSON><PERSON> lên l<PERSON>ch", "text.status.draft": "<PERSON><PERSON><PERSON>", "text.visibility.public": "<PERSON><PERSON><PERSON> khai", "text.visibility.hidden": "Ẩn", "text.submit": "<PERSON><PERSON><PERSON>", "text.add": "<PERSON><PERSON><PERSON><PERSON>", "confirm.save": "<PERSON><PERSON><PERSON>", "text.exit": "<PERSON><PERSON><PERSON><PERSON>", "text.download": "<PERSON><PERSON><PERSON>", "text.downloadTemplate": "<PERSON><PERSON><PERSON> xuống mẫu", "text.selectFile": "<PERSON><PERSON><PERSON> tập tin", "text.date": "<PERSON><PERSON><PERSON>", "text.action": "<PERSON><PERSON><PERSON> đ<PERSON>", "text.close": "Đ<PERSON><PERSON>"}, "header": {"text.login": "<PERSON><PERSON><PERSON>", "text.profile": "<PERSON><PERSON> sơ", "text.logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "text.notification": "<PERSON><PERSON><PERSON><PERSON> báo", "text.unreadNoti": "<PERSON><PERSON><PERSON>", "text.viewAllNoti": "<PERSON><PERSON> t<PERSON>t cả", "text.noNoti": "Ồ, bạn không có thông báo nào."}, "sidebarleft": {"text.menu": "<PERSON><PERSON>", "text.artice": "<PERSON><PERSON><PERSON> vi<PERSON>", "text.articleCate": "<PERSON><PERSON><PERSON> loại bài viết", "text.course": "<PERSON><PERSON><PERSON><PERSON>", "text.test": "<PERSON><PERSON><PERSON> kiểm tra", "text.testType": "<PERSON><PERSON><PERSON> bài kiểm tra", "text.practice": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh"}, "article": {"text.artice": "<PERSON><PERSON><PERSON> vi<PERSON>", "text.category": "<PERSON><PERSON>", "text.title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "text.add": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>i vi<PERSON>t", "text.articeCate": "<PERSON><PERSON><PERSON> loại bài viết", "text.addArticeCate": "<PERSON><PERSON><PERSON><PERSON> thể loại bài viết", "text.desc": "<PERSON><PERSON>", "confirm.delete.articleCate": "Bạn có muốn xóa danh mục bài viết này không? ", "text.selectArticleCate": "<PERSON><PERSON><PERSON> danh mục bài viết", "text.articleThumbnail": "<PERSON><PERSON><PERSON> bài viết", "text.content": "<PERSON><PERSON>i dung", "text.typeHere": "Nhập ở đây..."}, "course": {"text.course": "<PERSON><PERSON><PERSON><PERSON>", "text.type": "<PERSON><PERSON><PERSON>", "text.title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "text.addCourse": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "text.desc": "<PERSON><PERSON>", "confirm.delete.course": "Bạn có muốn xóa khóa học này không?", "text.testOfCourse": "<PERSON><PERSON><PERSON> kiểm tra khóa học", "text.updateOrderTest": "<PERSON><PERSON><PERSON> nh<PERSON>t thứ tự"}, "practice": {"text.practice": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh", "text.title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "text.add": "<PERSON><PERSON><PERSON><PERSON>", "text.course": "<PERSON><PERSON><PERSON><PERSON>", "confirm.delete.practice": "Bạn có muốn xóa bài thực hành này không?", "text.createPractice": "<PERSON><PERSON><PERSON> b<PERSON>i tập", "text.selectCourse": "<PERSON><PERSON><PERSON> kh<PERSON> học", "text.fileListen": "File", "text.type": "<PERSON><PERSON><PERSON>"}, "test": {"text.test": "<PERSON><PERSON><PERSON> kiểm tra", "text.course": "<PERSON><PERSON><PERSON><PERSON>", "text.title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "text.addTest": "<PERSON><PERSON><PERSON><PERSON> bài kiểm tra", "text.desc": "<PERSON><PERSON>", "confirm.delete.test": "Bạn có muốn xóa bài kiểm tra này không?", "text.questionRead": "<PERSON><PERSON>u hỏi  đọc", "text.addQuestion": "<PERSON><PERSON><PERSON><PERSON> câu hỏi", "text.type": "<PERSON><PERSON><PERSON>", "text.quantity": "Số lượng", "text.score": "<PERSON><PERSON><PERSON><PERSON>", "text.time": "<PERSON><PERSON><PERSON><PERSON> gian", "text.questionListen": "C<PERSON>u hỏi  nghe", "text.selectCourse": "<PERSON><PERSON><PERSON> kh<PERSON> học", "text.timeMinute": "<PERSON><PERSON><PERSON><PERSON> gian (phút)", "confirm.delete.testType": "Bạn có muốn xóa loại kiểm tra này không?", "text.testType": "<PERSON><PERSON><PERSON> kiểm tra", "text.addTestType": "<PERSON>h<PERSON>m loại kiểm tra", "text.question": "Câu hỏi", "text.quantityQuestion": "<PERSON><PERSON> lượng câu hỏi", "text.listen": "<PERSON><PERSON>", "text.read": "<PERSON><PERSON><PERSON>", "text.write": "<PERSON><PERSON><PERSON><PERSON>", "text.draft": "<PERSON><PERSON><PERSON>", "text.import": "<PERSON><PERSON><PERSON><PERSON>", "text.export": "<PERSON><PERSON><PERSON>", "text.save": "<PERSON><PERSON><PERSON>", "text.fileListen": "<PERSON><PERSON>", "text.addQuestionGroup": "Thêm nhóm câu hỏi", "text.transcript": "<PERSON><PERSON><PERSON>hi ch<PERSON>p", "text.keyDetail": "<PERSON><PERSON><PERSON> gi<PERSON>i chi tiết", "confirm.delete.scoreCard": "Bạn có muốn xóa thẻ điểm này không?", "text.newScoreCard": "Thẻ điểm mới", "text.scoreCard": "Thẻ điểm", "text.totalQuestion": "Tổng số câu hỏi", "text.totalScore": "<PERSON>ổng số điểm", "text.testDraft": "<PERSON><PERSON><PERSON> ng<PERSON> bản <PERSON>", "text.key": "Khóa", "text.partName": "<PERSON><PERSON><PERSON>", "text.currentDivideRequired": "(Hiện tại/B<PERSON><PERSON> bu<PERSON>)", "text.testImport": "<PERSON><PERSON><PERSON><PERSON>", "text.viewDetail": "<PERSON>em chi tiết", "text.timeRemain": "<PERSON><PERSON><PERSON><PERSON> gian còn lại", "text.point": "<PERSON><PERSON><PERSON><PERSON>", "text.testResult": "<PERSON><PERSON><PERSON> quả kiểm tra", "text.core": "<PERSON><PERSON><PERSON>", "text.questions": "Câu hỏi", "text.wrong": "<PERSON>", "text.result": "<PERSON><PERSON><PERSON> qu<PERSON>", "text.accuracy": "<PERSON><PERSON> ch<PERSON>h x<PERSON>c", "text.workingTime": "<PERSON><PERSON><PERSON><PERSON> gian làm vi<PERSON>c", "text.pass": "Đạt", "text.answerCorrect": "<PERSON><PERSON><PERSON> lời đ<PERSON>g", "text.showKeyDetail": "<PERSON><PERSON><PERSON> thị chi tiết kh<PERSON>a", "text.showTranscript": "<PERSON><PERSON><PERSON> thị bản ghi", "text.noAnswer": "<PERSON><PERSON><PERSON><PERSON> trả lời", "text.detail": "<PERSON> ti<PERSON>", "text.returnResultPage": "Trở về trang kết quả", "text.showAllTransciptKey": "<PERSON><PERSON><PERSON> thị tất cả bản ghi/khóa", "text.testPractice": "<PERSON><PERSON><PERSON> tra thực hành", "text.infoTest": "<PERSON><PERSON><PERSON> tra thông tin", "text.topik": "TOPIK", "text.minutes": "<PERSON><PERSON><PERSON><PERSON>", "text.parts": "<PERSON><PERSON><PERSON>", "text.comments": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>t", "text.userTested": "Ngư<PERSON>i dùng đã kiểm tra", "text.practice": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>nh", "text.part": "<PERSON><PERSON><PERSON>", "text.fullTest": "<PERSON><PERSON><PERSON> tra đ<PERSON>y đủ", "text.startTest": "<PERSON><PERSON><PERSON> đ<PERSON>u kiểm tra", "text.alert": " Bạn đã sẵn sàng bắt đầu làm bài kiểm tra đầy đủ chưa? Để đạt được kết quả tốt nhất, bạn cần chi tiêu", "text.alert2": "số phút cho bài kiểm tra này.", "text.leaveAComment": "<PERSON><PERSON> lại bình luận", "text.postComment": "<PERSON><PERSON><PERSON> b<PERSON>nh lu<PERSON>n", "text.enterYourComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>nh luận của bạn...", "text.reply": "<PERSON><PERSON><PERSON> lờ<PERSON>"}}