# Kế hoạch triển khai kiểm thử Unit Test

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

Tài liệu này mô tả chi tiết kế hoạch triển khai kiểm thử Unit Test cho các controller liên quan đến quản lý nhập kho và tồn kho trong hệ thống <PERSON>, bao gồm:
- NhaCungCapController
- PhieuNhapController
- TonKhoController

## 2. Phạm vi kiểm thử

### 2.1. NhaCungCapController
- <PERSON><PERSON><PERSON> thử tất cả các API endpoint của controller quản lý nhà cung cấp
- Tổng số test case: 11

### 2.2. PhieuNhapController
- <PERSON><PERSON><PERSON> thử tất cả các API endpoint của controller quản lý phiếu nhập
- Tổng số test case: 13

### 2.3. TonKhoController
- <PERSON><PERSON><PERSON> thử tất cả các API endpoint của controller qu<PERSON>n lý tồn kho
- Tổng số test case: 9

## 3. <PERSON><PERSON><PERSON> cụ và môi trường kiểm thử

### 3.1. Công cụ
- JUnit 5: Framework kiểm thử
- Mockito: Framework mô phỏng (mock) các dependency
- Spring Boot Test: Hỗ trợ kiểm thử ứng dụng Spring Boot
- MockMvc: Kiểm thử các REST API

### 3.2. Môi trường
- Môi trường phát triển (Development)
- Cơ sở dữ liệu H2 in-memory cho kiểm thử

## 4. Cấu trúc thư mục kiểm thử

```
src/
└── test/
    └── java/
        └── com/
            └── example/
                └── hieuthuoc/
                    └── controller/
                        ├── NhaCungCapControllerTest.java
                        ├── PhieuNhapControllerTest.java
                        └── TonKhoControllerTest.java
```

## 5. Phương pháp kiểm thử

### 5.1. Kiểm thử đơn vị (Unit Testing)
- Kiểm thử từng phương thức của controller một cách độc lập
- Sử dụng mock để mô phỏng các service và repository
- Kiểm tra kết quả trả về của API (status code, message, data)

### 5.2. Kiểm thử tích hợp (Integration Testing)
- Kiểm thử tương tác giữa controller và service
- Kiểm tra luồng dữ liệu từ controller đến service và ngược lại

## 6. Quy trình triển khai

### 6.1. Chuẩn bị dữ liệu kiểm thử
- Tạo dữ liệu mẫu cho các test case
- Chuẩn bị các mock object cho service và repository

### 6.2. Viết test case
- Viết test case cho từng API endpoint
- Đảm bảo bao phủ các trường hợp thành công và thất bại
- Kiểm tra các điều kiện biên và xử lý ngoại lệ

### 6.3. Chạy kiểm thử
- Chạy từng test case riêng lẻ
- Chạy toàn bộ test suite
- Phân tích kết quả và sửa lỗi nếu cần

## 7. Mẫu code kiểm thử

### 7.1. Mẫu kiểm thử cho NhaCungCapController

```java
@WebMvcTest(NhaCungCapController.class)
public class NhaCungCapControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private NhaCungCapService nhaCungCapService;

    @Test
    public void testGetAll_Success() throws Exception {
        // Chuẩn bị dữ liệu
        List<NhaCungCap> nhaCungCaps = Arrays.asList(
            new NhaCungCap(1, "NCC001", "Nhà cung cấp 1", "Địa chỉ 1", "0123456789", "<EMAIL>"),
            new NhaCungCap(2, "NCC002", "Nhà cung cấp 2", "Địa chỉ 2", "0987654321", "<EMAIL>")
        );
        
        ResponseDTO<List<NhaCungCap>> responseDTO = ResponseDTO.<List<NhaCungCap>>builder()
            .status(200)
            .msg("Thành công")
            .data(nhaCungCaps)
            .build();
            
        // Mock service
        when(nhaCungCapService.getAll()).thenReturn(responseDTO);
        
        // Thực hiện request và kiểm tra kết quả
        mockMvc.perform(get("/nhacungcap/list"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.status").value(200))
            .andExpect(jsonPath("$.msg").value("Thành công"))
            .andExpect(jsonPath("$.data").isArray())
            .andExpect(jsonPath("$.data.length()").value(2))
            .andExpect(jsonPath("$.data[0].maNCC").value("NCC001"))
            .andExpect(jsonPath("$.data[1].maNCC").value("NCC002"));
    }
    
    // Các test case khác...
}
```

### 7.2. Mẫu kiểm thử cho PhieuNhapController

```java
@WebMvcTest(PhieuNhapController.class)
public class PhieuNhapControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PhieuNhapService phieuNhapService;
    
    @Test
    public void testSearch_Success() throws Exception {
        // Chuẩn bị dữ liệu
        List<PhieuNhap> phieuNhaps = Arrays.asList(
            // Dữ liệu mẫu phiếu nhập
        );
        
        PageDTO<List<PhieuNhap>> pageDTO = new PageDTO<>();
        pageDTO.setTotalElements(2L);
        pageDTO.setTotalPages(1);
        pageDTO.setData(phieuNhaps);
        
        ResponseDTO<PageDTO<List<PhieuNhap>>> responseDTO = ResponseDTO.<PageDTO<List<PhieuNhap>>>builder()
            .status(200)
            .msg("Thanh công")
            .data(pageDTO)
            .build();
            
        // Mock service
        when(phieuNhapService.search(any(SearchDTO.class))).thenReturn(responseDTO);
        
        // Thực hiện request và kiểm tra kết quả
        mockMvc.perform(post("/phieunhap/search")
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\"currentPage\": 0, \"size\": 20}"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.status").value(200))
            .andExpect(jsonPath("$.msg").value("Thanh công"))
            .andExpect(jsonPath("$.data.totalElements").value(2))
            .andExpect(jsonPath("$.data.totalPages").value(1))
            .andExpect(jsonPath("$.data.data").isArray());
    }
    
    // Các test case khác...
}
```

### 7.3. Mẫu kiểm thử cho TonKhoController

```java
@WebMvcTest(TonKhoController.class)
public class TonKhoControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TonKhoService tonKhoService;
    
    @Test
    public void testUpdate_Success() throws Exception {
        // Chuẩn bị dữ liệu
        TonKho tonKho = new TonKho();
        // Thiết lập dữ liệu cho tonKho
        
        ResponseDTO<TonKho> responseDTO = ResponseDTO.<TonKho>builder()
            .status(200)
            .msg("Thành công.")
            .data(tonKho)
            .build();
            
        // Mock service
        when(tonKhoService.update(any(TonKhoDTO.class))).thenReturn(responseDTO);
        
        // Thực hiện request và kiểm tra kết quả
        mockMvc.perform(put("/tonkho/update")
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\"id\": 1, \"thuocId\": 1, \"soLo\": \"L001\", \"hanSuDung\": \"2025-12-31\", \"soLuong\": 100, \"viTri\": \"Kho A\"}"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.status").value(200))
            .andExpect(jsonPath("$.msg").value("Thành công."))
            .andExpect(jsonPath("$.data").exists());
    }
    
    // Các test case khác...
}
```

## 8. Báo cáo kết quả kiểm thử

Sau khi hoàn thành kiểm thử, cần tạo báo cáo kết quả bao gồm:
- Tổng số test case đã thực hiện
- Số lượng test case thành công/thất bại
- Tỷ lệ bao phủ code (code coverage)
- Các vấn đề phát hiện được
- Đề xuất cải tiến

## 9. Lịch trình triển khai

| Giai đoạn | Thời gian | Người thực hiện |
|-----------|-----------|-----------------|
| Chuẩn bị dữ liệu kiểm thử | 1 ngày | Chưa phân công |
| Viết test case cho NhaCungCapController | 2 ngày | Chưa phân công |
| Viết test case cho PhieuNhapController | 2 ngày | Chưa phân công |
| Viết test case cho TonKhoController | 2 ngày | Chưa phân công |
| Chạy kiểm thử và sửa lỗi | 2 ngày | Chưa phân công |
| Tạo báo cáo kết quả | 1 ngày | Chưa phân công |

## 10. Kết luận

Kế hoạch kiểm thử này cung cấp hướng dẫn chi tiết để triển khai kiểm thử Unit Test cho các controller liên quan đến quản lý nhập kho và tồn kho. Việc thực hiện đầy đủ các test case sẽ giúp đảm bảo chất lượng code và giảm thiểu lỗi khi triển khai hệ thống.
