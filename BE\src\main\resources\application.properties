spring.application.name=hieuthuoc

server.port=8888
server.servlet.context-path=/hieuthuoc

spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB


#JDBC_Connection
#jdbc
spring.datasource.url=************************************************
spring.datasource.username=postgres
spring.datasource.password=123456
spring.datasource.driver-class-name=org.postgresql.Driver


#jpa
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
# seed data
#spring.jpa.hibernate.ddl-auto=create-drop
#spring.sql.init.mode=always
#spring.jpa.defer-datasource-initialization=true
##spring.jpa.hibernate.ddl-auto=create

#debug sql
#spring.jpa.show-sql=true
#spring.jpa.properties.hibernate.format_sql=true
#logging.level.org.hibernate.SQL=DEBUG
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

#tự động thêm bảng/cột nếu trong sql chưa có
spring.jpa.generate-ddl=true

#logging in ra màn hình các debug
logging.level.root=info
logging.file.name=myapp.log
logging.logback.rollingpolicy.max-file-size=2MB
logging.logback.rollingpolicy.max-history=7

# message config
spring.messages.basename=lang/message
spring.messages.encoding=UTF-8

# main server
spring.mail.host=smtp.gmail.com
spring.mail.username=<EMAIL>
# http://myaccount.google.com/u/1/apppasswords
spring.mail.password=ysxm lcas avzk dxhz
spring.mail.port=587
spring.mail.default-encoding=UTF-8

spring.mail.protocol=smtp
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

#jwwt
jwt.secret=123456
jwt.validity=5

#Cloudinary
cloud_name=dhuq2xqu3
api_key=***************
api_secret=sxC4F8wG7v2MeWmQzjqgYbgYyh4
