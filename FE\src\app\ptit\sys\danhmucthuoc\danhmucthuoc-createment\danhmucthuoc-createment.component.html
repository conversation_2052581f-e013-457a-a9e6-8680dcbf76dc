<p-dialog
  header="Thêm danh mục thuốc"
  [(visible)]="displayModal"
  [modal]="true"
  [style]="{ width: '50vw' }"
  [baseZIndex]="10000"
  [draggable]="true"
  [resizable]="false"
  [modal]="true"
  (onHide)="onCancel()"
>
  <div>
    <div class="mb-3">
      <label class="col-form-label" for="title"> Tên <span class="text-danger">*</span></label>
      <input
        type="text"
        name="title"
        class="form-control"
        id="title"
        placeholder="Nhập tên danh mục thuốc"
        #title="ngModel"
        [(ngModel)]="dmThuoc.tenDanhMuc"
        required
      />
    </div>
    <div
      class="text-danger"
      *ngIf="
        dmThuoc.tenDanhMuc == '' ||
        (title.invalid && (title.dirty || title.touched))
      "
    >
      H<PERSON><PERSON> nhập tên danh mục thuốc
    </div>

    <div class="mb-3">
      <label class="col-form-label" for="title"> <PERSON><PERSON> tả </label>
      <textarea
        type="text"
        name="mota"
        class="form-control"
        id="mota"
        placeholder="Nhập mô tả loại thuốc"
        [(ngModel)]="dmThuoc.moTa"
      >
      </textarea>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <p-button
      icon="pi pi-times"
      (click)="displayModal = false"
      label="Hủy"
      styleClass="btn btn-danger"
    ></p-button>
    <p-button
      icon="pi pi-check"
      label="Lưu"
      (click)="onSave()"
      styleClass="btn btn-success"
    ></p-button>
  </ng-template>
</p-dialog>
