#page-topbar {
  background-color: #3964e7; /* <PERSON><PERSON><PERSON> xanh dương nhạt */
}

/* <PERSON><PERSON><PERSON> bảo căn giữa theo chiều ngang */
.logo-dark {
  display: flex;
  align-items: center; /* Căn giữa dọc */
}

.logo-sm img {
  display: block; /* Tr<PERSON>h khoảng trống xung quanh hình ảnh */
}

.logo-text {
  font-size: 18px; /* <PERSON><PERSON><PERSON> thước chữ */
  font-weight: bold; /* Đậm chữ */
  line-height: 1.2; /* Khoảng cách giữa các dòng */
  color: white; /* <PERSON><PERSON><PERSON> chữ */
  text-align: left; /* Văn bản căn trái */
}

/* Nút thông báo */
#notification-dropdown {
  background-color: white !important; /* Nền trắng */
  color: black; /* Màu biểu tượng */
  border: none; /* <PERSON><PERSON><PERSON> viền */
}

/* Hover hiệu ứng */
#notification-dropdown:hover {
  background-color: #f0f0f0; /* <PERSON>à<PERSON> xám nhạt khi hover */
  color: black;
}

/* Đảm bảo icon đư<PERSON><PERSON> căn chỉnh */
#notification-dropdown i {
  font-size: 22px; /* Kích thước icon */
}

/* Dropdown menu cho thông báo */
.dropdown-menu {
  background-color: white; /* Nền trắng */
  border-radius: 6px; /* Bo góc nhẹ */
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); /* Đổ bóng */
}

.dropdown-item {
  color: black; /* Màu chữ */
}

.dropdown-item:hover {
  background-color: #f8f9fa; /* Màu nền khi hover */
  color: black; /* Màu chữ khi hover */
}

.dropdown-header {
  font-size: 14px; /* Kích thước chữ nhỏ */
  font-weight: bold;
  color: #6c757d; /* Màu xám */
}
