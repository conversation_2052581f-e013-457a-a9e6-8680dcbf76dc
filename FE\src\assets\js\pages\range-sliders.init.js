var sliderColorScheme=document.querySelectorAll("[data-rangeslider]");sliderColorScheme&&Array.from(sliderColorScheme).forEach(function(e){noUiSlider.create(e,{start:127,connect:"lower",range:{min:0,max:255}})});var multielementslider=document.querySelectorAll("[data-multielement]");multielementslider&&Array.from(multielementslider).forEach(function(e){noUiSlider.create(e,{start:[20,80],connect:!0,range:{min:0,max:100}})});var resultElement=document.getElementById("result"),sliders=document.getElementsByClassName("sliders"),colors=[0,0,0];sliders&&Array.from([].slice.call(sliders)).forEach(function(i,t){noUiSlider.create(i,{start:127,connect:[!0,!1],orientation:"vertical",range:{min:0,max:255},format:wNumb({decimals:0})}),i.noUiSlider.on("update",function(){colors[t]=i.noUiSlider.get();var e="rgb("+colors.join(",")+")";resultElement.style.background=e,resultElement.style.color=e})});for(var select=document.getElementById("input-select"),i=-20;i<=40;i++){var option=document.createElement("option");option.text=i,option.value=i,select.appendChild(option)}var html5Slider=document.getElementById("html5");html5Slider&&noUiSlider.create(html5Slider,{start:[10,30],connect:!0,range:{min:-20,max:40}});var inputNumber=document.getElementById("input-number");inputNumber&&html5Slider&&(html5Slider.noUiSlider.on("update",function(e,i){e=e[i];i?inputNumber.value=e:select.value=Math.round(e)}),select.addEventListener("change",function(){html5Slider.noUiSlider.set([this.value,null])}),inputNumber.addEventListener("change",function(){html5Slider.noUiSlider.set([null,this.value])}));var nonLinearSlider=document.getElementById("nonlinear");nonLinearSlider&&noUiSlider.create(nonLinearSlider,{connect:!0,behaviour:"tap",start:[500,4e3],range:{min:[0],"10%":[500,500],"50%":[4e3,1e3],max:[1e4]}});var nodes=[document.getElementById("lower-value"),document.getElementById("upper-value")];nonLinearSlider.noUiSlider.on("update",function(e,i,t,r,n){nodes[i].innerHTML=e[i]+", "+n[i].toFixed(2)+"%"});var lockedState=!1,lockedSlider=!1,lockedValues=[60,80],slider1=document.getElementById("slider1"),slider2=document.getElementById("slider2"),lockButton=document.getElementById("lockbutton"),slider1Value=document.getElementById("slider1-span"),slider2Value=document.getElementById("slider2-span");function crossUpdate(e,i){var t;lockedState&&(e-=lockedValues[(t=slider1===i?0:1)?0:1]-lockedValues[t],i.noUiSlider.set(e))}function setLockedValues(){lockedValues=[Number(slider1.noUiSlider.get()),Number(slider2.noUiSlider.get())]}lockButton&&lockButton.addEventListener("click",function(){lockedState=!lockedState,this.textContent=lockedState?"unlock":"lock"}),noUiSlider.create(slider1,{start:60,animate:!1,range:{min:50,max:100}}),noUiSlider.create(slider2,{start:80,animate:!1,range:{min:50,max:100}}),slider1&&slider2&&(slider1.noUiSlider.on("update",function(e,i){slider1Value.innerHTML=e[i]}),slider2.noUiSlider.on("update",function(e,i){slider2Value.innerHTML=e[i]}),slider1.noUiSlider.on("change",setLockedValues),slider2.noUiSlider.on("change",setLockedValues),slider1.noUiSlider.on("slide",function(e,i){crossUpdate(e[i],slider2)}),slider2.noUiSlider.on("slide",function(e,i){crossUpdate(e[i],slider1)}));var mergingTooltipSlider=document.getElementById("slider-merging-tooltips");function mergeTooltips(e,c,m){var u="rtl"===getComputedStyle(e).direction,S="rtl"===e.noUiSlider.options.direction,g="vertical"===e.noUiSlider.options.orientation,p=e.noUiSlider.getTooltips(),t=e.noUiSlider.getOrigins();Array.from(p).forEach(function(e,i){e&&t[i].appendChild(e)}),e&&e.noUiSlider.on("update",function(e,i,t,r,n){var l=[[]],a=[[]],s=[[]],o=0;p[0]&&(l[0][0]=0,a[0][0]=n[0],s[0][0]=e[0]);for(var d=1;d<n.length;d++)(!p[d]||n[d]-n[d-1]>c)&&(l[++o]=[],s[o]=[],a[o]=[]),p[d]&&(l[o].push(d),s[o].push(e[d]),a[o].push(n[d]));Array.from(l).forEach(function(e,i){for(var t=e.length,r=0;r<t;r++){var n,l,o,d=e[r];r===t-1?(o=0,Array.from(a[i]).forEach(function(e){o+=1e3-e}),n=g?"bottom":"right",l=1e3-a[i][S?0:t-1],o=(u&&!g?100:0)+o/t-l,p[d].innerHTML=s[i].join(m),p[d].style.display="block",p[d].style[n]=o+"%"):p[d].style.display="none"}})})}mergingTooltipSlider&&(noUiSlider.create(mergingTooltipSlider,{start:[20,75],connect:!0,tooltips:[!0,!0],range:{min:0,max:100}}),mergeTooltips(mergingTooltipSlider,5," - "));var hidingTooltipSlider=document.getElementById("slider-hide");hidingTooltipSlider&&noUiSlider.create(hidingTooltipSlider,{range:{min:0,max:100},start:[20,80],tooltips:!0});var pipsSlider=document.getElementById("slider-pips");pipsSlider&&noUiSlider.create(pipsSlider,{range:{min:0,max:100},start:[50],pips:{mode:"count",values:5}});var pips=pipsSlider.querySelectorAll(".noUi-value");function clickOnPip(){var e=Number(this.getAttribute("data-value"));pipsSlider.noUiSlider.set(e)}pips&&Array.from(pips).forEach(function(e){e.style.cursor="pointer",e.addEventListener("click",clickOnPip)});var slider=document.getElementById("slider-color");slider&&noUiSlider.create(slider,{start:[4e3,8e3,12e3,16e3],connect:[!1,!0,!0,!0,!0],range:{min:[2e3],max:[2e4]}});var connect=slider.querySelectorAll(".noUi-connect"),classes=["c-1-color","c-2-color","c-3-color","c-4-color","c-5-color"],i=0;Array.from(connect).forEach(function(e){e.classList.add(classes[i]),i++});var toggleSlider=document.getElementById("slider-toggle");toggleSlider&&(noUiSlider.create(toggleSlider,{orientation:"vertical",start:0,range:{min:[0,1],max:1},format:wNumb({decimals:0})}),toggleSlider.noUiSlider.on("update",function(e,i){"1"===e[i]?toggleSlider.classList.add("off"):toggleSlider.classList.remove("off")}));var softSlider=document.getElementById("soft");softSlider&&(noUiSlider.create(softSlider,{start:50,range:{min:0,max:100},pips:{mode:"values",values:[20,80],density:4}}),softSlider.noUiSlider.on("change",function(e,i){e[i]<20?softSlider.noUiSlider.set(20):80<e[i]&&softSlider.noUiSlider.set(80)}));