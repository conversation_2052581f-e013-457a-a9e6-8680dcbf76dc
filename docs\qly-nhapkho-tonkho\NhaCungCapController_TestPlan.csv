ID,Tên <PERSON>,<PERSON><PERSON> tả,Endpoint,Method,Input,Expected Output,<PERSON><PERSON><PERSON><PERSON> kiện tiên quyết
NCC-TC-01,<PERSON><PERSON><PERSON> danh sách nhà cung cấp thành công,<PERSON><PERSON><PERSON> tra API lấy danh sách tất cả nhà cung cấp,/nhacungcap/list,GET,Không có,Status: 200; Msg: "Thành công"; Data: <PERSON>h sách nhà cung cấp,<PERSON><PERSON> dữ liệu nhà cung cấp trong database
NCC-TC-02,<PERSON><PERSON>y danh sách nhà cung cấp khi không có dữ liệu,<PERSON><PERSON>m tra API lấy danh sách nhà cung cấp khi không có dữ liệu,/nhacungcap/list,GET,Không có,Status: 200; Msg: "Thành công"; Data: <PERSON><PERSON> sách rỗng,Không có dữ liệu nhà cung cấp trong database
NCC-TC-03,<PERSON><PERSON><PERSON> kiếm nhà cung cấp theo tên thành công,<PERSON><PERSON><PERSON> tra API tìm kiếm nhà cung cấp theo tên,/nhacungcap/search_by_ten_nha_cung_cap,GET,tenNhaCungCap="ABC",Status: 200; Msg: "Thành công"; Data: Danh sách nhà cung cấp có tên chứa "ABC",Có nhà cung cấp có tên chứa "ABC" trong database
NCC-TC-04,Tìm kiếm nhà cung cấp theo tên không tồn tại,Kiểm tra API tìm kiếm nhà cung cấp với tên không tồn tại,/nhacungcap/search_by_ten_nha_cung_cap,GET,tenNhaCungCap="XYZ123",Status: 409; Msg: "Nhà sản xuất không tồn tại",Không có nhà cung cấp nào có tên chứa "XYZ123"
NCC-TC-05,Tìm kiếm nhà cung cấp với tên rỗng,Kiểm tra API tìm kiếm nhà cung cấp với tên rỗng,/nhacungcap/search_by_ten_nha_cung_cap,GET,tenNhaCungCap="",Status: 409; Msg: "Nhà sản xuất không tồn tại",Không có nhà cung cấp nào có tên rỗng
NCC-TC-06,Tìm kiếm nhà cung cấp với tên chứa ký tự đặc biệt,Kiểm tra API tìm kiếm nhà cung cấp với tên chứa ký tự đặc biệt,/nhacungcap/search_by_ten_nha_cung_cap,GET,tenNhaCungCap="ABC@#$",Status: 409; Msg: "Nhà sản xuất không tồn tại",Không có nhà cung cấp nào có tên chứa ký tự đặc biệt
NCC-TC-07,Tạo mới nhà cung cấp thành công,Kiểm tra API tạo mới nhà cung cấp,/nhacungcap/create,POST,NhaCungCapDTO với thông tin hợp lệ,Status: 201; Msg: "Thành công"; Data: Thông tin nhà cung cấp vừa tạo,Mã NCC chưa tồn tại trong hệ thống
NCC-TC-08,Tạo mới nhà cung cấp với mã đã tồn tại,Kiểm tra API tạo mới nhà cung cấp với mã đã tồn tại,/nhacungcap/create,POST,NhaCungCapDTO với mã NCC đã tồn tại,Status: 409; Msg: "Nhà cung cấp đã tồn tại",Mã NCC đã tồn tại trong hệ thống
NCC-TC-09,Tạo mới nhà cung cấp với dữ liệu không hợp lệ,Kiểm tra API tạo mới nhà cung cấp với dữ liệu không hợp lệ,/nhacungcap/create,POST,NhaCungCapDTO với email không đúng định dạng,Status: 400; Lỗi validation,Dữ liệu không hợp lệ
NCC-TC-10,Tạo mới nhà cung cấp với số điện thoại không hợp lệ,Kiểm tra API tạo mới nhà cung cấp với số điện thoại không hợp lệ,/nhacungcap/create,POST,NhaCungCapDTO với số điện thoại không đúng định dạng,Status: 400; Lỗi validation,Dữ liệu không hợp lệ
NCC-TC-11,Cập nhật thông tin nhà cung cấp thành công,Kiểm tra API cập nhật thông tin nhà cung cấp,/nhacungcap/update,PUT,NhaCungCapDTO với thông tin hợp lệ và ID tồn tại,Status: 200; Msg: "Thành công"; Data: Thông tin nhà cung cấp sau khi cập nhật,Nhà cung cấp với ID đã tồn tại
NCC-TC-12,Cập nhật thông tin nhà cung cấp không tồn tại,Kiểm tra API cập nhật thông tin nhà cung cấp không tồn tại,/nhacungcap/update,PUT,NhaCungCapDTO với ID không tồn tại,Status: 404; Msg: "Không tìm thấy nhà cung cấp",Không có nhà cung cấp với ID cần cập nhật
NCC-TC-13,Cập nhật thông tin nhà cung cấp với mã đã tồn tại,Kiểm tra API cập nhật thông tin nhà cung cấp với mã đã tồn tại,/nhacungcap/update,PUT,NhaCungCapDTO với mã NCC đã tồn tại của nhà cung cấp khác,Status: 409; Msg: "Mã Nhà cung cấp đã tồn tại",Mã NCC đã tồn tại cho nhà cung cấp khác
NCC-TC-14,Cập nhật thông tin nhà cung cấp với dữ liệu không hợp lệ,Kiểm tra API cập nhật thông tin nhà cung cấp với dữ liệu không hợp lệ,/nhacungcap/update,PUT,NhaCungCapDTO với email không đúng định dạng,Status: 400; Lỗi validation,Dữ liệu không hợp lệ
NCC-TC-15,Xóa nhà cung cấp thành công,Kiểm tra API xóa nhà cung cấp,/nhacungcap/delete,DELETE,id=1,Status: 200; Msg: "Thành công",Nhà cung cấp với ID=1 tồn tại
NCC-TC-16,Xóa nhà cung cấp không tồn tại,Kiểm tra API xóa nhà cung cấp không tồn tại,/nhacungcap/delete,DELETE,id=999,Status: 200; Msg: "Thành công" (Không báo lỗi khi xóa ID không tồn tại),Không có nhà cung cấp với ID=999
NCC-TC-17,Xóa nhà cung cấp đã được sử dụng trong phiếu nhập,Kiểm tra API xóa nhà cung cấp đã được sử dụng trong phiếu nhập,/nhacungcap/delete,DELETE,id=1,Status: 500; Lỗi ràng buộc khóa ngoại,Nhà cung cấp với ID=1 đã được sử dụng trong phiếu nhập
