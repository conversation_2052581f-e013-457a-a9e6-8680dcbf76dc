.responsive-img {
  object-fit: contain;
  display: flex;
  justify-content: center; /* Căn giữa theo chiều ngang */
  align-items: center; /* Căn giữa theo chiều dọc */
  width: 100%; /* Chiều rộng khung chứa */
  height: 175px; /* <PERSON><PERSON>u cao khung chứa */
}

.category-name {
  margin-bottom: 20px; /* <PERSON><PERSON>ảng cách dưới tên loại thuốc */
  font-size: 24px; /* <PERSON><PERSON><PERSON> thước chữ */
  font-weight: bold; /* Làm đậm tên loại thuốc */
}
.text-truncate {
  display: inline-block;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Nền màu xanh cho toàn bộ thanh tìm kiếm */
.search-box-custom {
  display: flex;
  align-items: center;
  border-radius: 50px;
  padding: 5px;
  width: 100%;
  max-width: 1500px;
  margin: 0 auto; /* <PERSON><PERSON><PERSON> giữa */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Input tìm kiếm */
.search-input {
  flex: 1;
  border: none;
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 50px 0 0 50px;
  outline: none;
  color: #333333; /* Màu chữ trong input */
  background: #ffffff; /* Nền input màu trắng */
}

.search-input::placeholder {
  color: #9ca3af; /* Màu placeholder */
}

/* Nút tìm kiếm */
.search-button {
  background: #1d4ed8; /* Màu xanh đậm hơn nút */
  border: none;
  border-radius: 50px;
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background 0.3s;
}

.search-button i {
  font-size: 20px;
  color: #ffffff; /* Màu icon trắng */
}

.search-button:hover {
  background: #1742a3; /* Đổi màu nút khi hover */
}

.btn-custom-primary {
  background-color: #3964e7; /* Màu xanh lam tùy chỉnh */
  border-color: #3964e7;
  color: white;
}

.card {
  border-radius: 16px;
  overflow: hidden;
}

.card-link {
  text-decoration: none; /* Loại bỏ gạch chân mặc định của thẻ <a> */
  display: block;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.card-link:hover .card {
  transform: scale(1.02); /* Phóng to nhẹ khi hover */
  box-shadow: 0px 4px 10px #3964e7; /* Hiệu ứng đổ bóng */
}

.card-link:active .card {
  transform: scale(0.98); /* Hiệu ứng bấm xuống */
}

.text-container {
  display: flex;
  justify-content: center; /* Căn giữa theo chiều ngang */
  align-items: center; /* Căn giữa theo chiều dọc */
}

/* Responsive cho thiết bị nhỏ */
/* @media (max-width: 768px) {
  .right-image {
    margin-bottom: 12px;
  }
} */

.filter-container {
  /* width: 300px; */
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
}

.filter-header {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 16px;
}

.filter-category {
  margin-bottom: 16px;
}

.filter-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.filter-options label {
  display: block;
  margin-bottom: 8px;
}

.filter-options input[type="checkbox"] {
  margin-right: 8px;
}

.filter-options button {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f9fa;
  cursor: pointer;
}

.filter-options button.active {
  background: #007bff;
  color: #fff;
}

.filter-options input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 8px;
}

.filter-options ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.filter-options li {
  padding: 8px;
  cursor: pointer;
}

.filter-options li:hover {
  background: #f8f9fa;
}

/* Kiểu chung cho bộ lọc */
.filter-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  /* padding: 15px; */
  background-color: #fff;
}

/* Tiêu đề của mỗi phần */
.filter-title {
  font-size: 16px;
  font-weight: bold;
  /* margin-bottom: 10px; */
}

/* Tùy chọn bên trong mỗi phần */
.filter-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  /* margin-bottom: 15px;  */
  /* Khoảng cách bên dưới các tùy chọn */
}

/* Gạch chân ngăn cách mỗi phần */
.filter-category {
  /* padding-bottom: 15px; */
  margin-bottom: 15px;
  border-bottom: 1px solid #ddd; /* Dòng gạch chân */
}

/* Xóa dòng gạch chân ở phần cuối cùng */
.filter-category:last-child {
  border-bottom: none;
}

/* Nút lọc giá bán */
.filter-options button {
  /* padding: 10px 15px; */
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s;
}

.filter-options button:hover {
  background-color: #f5f5f5;
}

.filter-options button.active {
  border-color: #007bff;
  background-color: #e9f3ff;
  color: #007bff;
  font-weight: bold;
}

/* Label cho radio */
.filter-options label {
  font-size: 14px;
  cursor: pointer;
}

.filter-options label input[type="radio"] {
  margin-right: 8px;
}
