 Checklist xem xét kết thúc giai đoạn,,,,
,,,,
Mã dự án:,,,,
Người xem xét:,,,,
Giai đoạn:,,,,
Ng<PERSON>y xem xét:,,,,
Nguồn lực dùng để xem xét (person-hour):,,,,
,,,,
<PERSON><PERSON><PERSON> hỏi,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON> chú
<PERSON>ể<PERSON> tra các mục tiêu,,,,
Quality gate có phù hợp với lịch trình trong kế hoạch dự án không ?,,,,
"<PERSON><PERSON><PERSON> mục tiêu chất lượng trong mỗi giai đoạn có phù hợp với mục tiêu đề ra trong kế hoạch dự án không? Nếu không,",,,,
<PERSON><PERSON> tiến hành phân tích các mục tiêu không đạt được không? ,,,,
<PERSON><PERSON> lập kế hoạch cho các hoạt động sửa không?,,,,
<PERSON><PERSON><PERSON> chỉ tiêu chất lượng trong từng giai đoạn có phù hợp với chuẩn của navisoft không? Nếu không,,,,
Có tiến hành phân tích các chỉ tiêu ngoài tầm kiểm soát không?,,,,
Có lập kế hoạch cho các hoạt động sửa không?,,,,
Các hoạt động nằm trong kế hoạch của từng giai đoạn có được thực hiện đầy đủ không?,,,,
Các công việc chưa giải quyết từ giai đoạn trước có được đóng không?,,,,
Các hành động phòng ngừa rủi ro có được thực hiện phù hợp với kế hoạch dự án?,,,,
Có theo dõi các rủi ro hay không?,,,,
Có tiến hành ước tính lại hay cập nhật lại kế hoạch cho giai đoạn tiếp theo không? Kết quả của việc ước tính lại và cập nhật lại kế hoạch có được ghi nhận và được các bên liên quan xem xét không ?,,,,
Kiểm tra số liệu để tính chỉ tiêu,,,,
"Các nguồn dùng để lấy số liệu để tính chỉ tiêu như Timesheet, TestTrack có đúng với số đang tính ra chỉ tiêu đó không ? (Schedule và effort)",,,,
Số liệu dùng để tình chỉ tiêu Requirement Stability có đúng với số trong RMS không ? (RMS phải được cập nhật tình trạng yêu cầu),,,,
Số liệu tính chỉ tiêu timeliness có đúng với FI không ? FI có được cập nhật đúng với biên bản bàn giao hoặc các tài liệu có liên quan ?,,,,
Số liệu tính chỉ tiêu Defect Removal Efficiency có đúng với số trong TestTrack không ? TestTrack có được cập nhật đầy đủ lỗi acceptant test ?,,,,
Số liệu tính chỉ tiêu Process Compliance có đúng với báo cáo đánh giá lần cuối cùng không ?,,,,
Số liệu tính chỉ tiêu Review Effectiveness có đúng với số liệu trong timesheet và TestTrack không ? TestTrack có được cập nhật đầy đủ lỗi không ? Tĩmesheet có ghi nhận effort review không ?,,,,
Kiểm tra các sản phẩm làm việc của dự án,,,,
Các sản phẩm trong từng giai đoạn có phù hợp với danh sách sản phẩm được liệt kê trong kế hoạch dự án không?,,,,
"Có sản phẩm do khách hàng cung cấp không? Nếu có,",,,,
Các sản phẩm đó có được xem xét không?,,,,
Các vấn đề nêu ra khi xem xét có được đóng không?,,,,
Có được xem xét bởi PdQA không?,,,,
Thành phần tham gia review có tuân theo navisoft RADIO không?,,,,
Những người tham gia review có được đào tạo hay có kinh nghiệm trong vai trò của họ không?,,,,
Các lỗi được tìm thấy trên sản phẩm có ghi nhận thỏa đáng trên TestTrack không?,,,,
Tất cả các lỗi đã được xác định có được đóng không?,,,,
Có liệt kê toàn bộ lỗi ở trạng thái mở không?,,,,
Có thực hiện việc kiểm tra cuối cùng cho các sản phẩm không?,,,,
"Tất cả các nhận xét, nếu có, có được theo dõi để đóng hay không? ",,,,
Các thông tin phản hồi của khách hàng về sản phẩm có được ghi nhận và theo dõi để đóng hay không? ,,,,
Các bản ghi có đươc đặt trong thư mục của dự án không? ,,,,
Tất cả các lỗi được phát hiện bởi khách hàng có được ghi nhận vào TestTrack không?,,,,
Các lỗi đó có được đóng không?,,,,
Mọi phàn nàn của khách hàng có được giải quyết không?,,,,
Bản theo dõi liên kết các sản phẩm có hợp nhất các thay đổi không?,,,,
Kiểm tra các hoạt động quản lý cấu hình,,,,
"Các CI bắt buộc sau có được baseline không: chuẩn lập trình, file doc kế hoạch dự án, Tài liệu quá trình phân tích, thiết kế, Test, Các tài liệu khác, các sản phẩm do khách hàng cung cấp?",,,,
 Số hiệu phiên bản của các đơn vị cấu hình trong thư mục Baseline có phải là phiên bản ổn định nhất của đơn vị cấu hình tương ứng trong báo cáo baseline không? Danh sách cấu hình có được update không?,,,,
"Số hiệu phiên bản ở trang tiêu đề của tất cả các tài liệu của dự án có phù hợp với tên file, header và bản ghi nhận thay đổi tài liệu hay không? ",,,,
Ngày hiệu lực của bản ghi nhận thay đổi tài liệu có phù hợp với ngày phê duyệt ở trang ký và ngày ở trang tiêu đề và ngày bàn giao sản phẩm trên FI không?,,,,
Có ghi nhận các yêu cầu thay đổi của khách hàng không?,,,,
Có phân tích ảnh hưởng các yêu cầu thay đổi không?,,,,
Việc phân tích ảnh hưởng các yêu cầu thay đổi có được xem xét và phê duyệt không?,,,,
Các thay đổi về CI của dự án có được ghi nhận trong bản ghi nhận thay đổi không? Số hiệu phiên bản có được cập nhật tương ứng với các thay đổi không?,,,,
Các thay đổi về CI của dự án có được thông báo cho toàn bộ đội dự án và các bên liên quan không?,,,,
Bản theo dõi liên kết các sản phẩm có phù hợp với các sản phẩm làm việc của dự án không?,,,,
Có bằng chứng backup không?,,,,
Kiểm tra hồ sơ của dự án,,,,
Tất cả thành viên của đội dự án đều khai đầy đủ timesheet không?,,,,
Nguồn lực sử dụng trong các hoạt động của dự án đựợc ghi nhận có khớp với Timesheet không?,,,,
"Các thông tin về yêu cầu, tiến độ, kế hoạch, nguồn lực, sản phẩm, rủi ro và vấn đề có được cập nhật chính xác trên FI không? ",,,,
Có yêu cầu bảo mật đặc biệt từ phía khách hàng không? Nếu có:,,,,
Các yêu cầu có được thông báo cho toàn bộ đội dự án không?,,,,
Có các cam kết cá nhân đối với từng thành viên trong đội dự án không?,,,,
Có thực hiện các hoạt động đào tạo cho đội dự án không?,,,,
Có ghi nhận các hoạt động đào tạo không?,,,,
Có chuẩn bị các báo cáo đào tạo và đưa ra cho mọi người xem xét theo quy định ở navisoft RADIO hay không?,,,,
Họp dự án có được thực hiện đúng tần suất theo kế hoạch dự án không? Có biên bản họp không?,,,,
"Các biên bản xem xét của QA có được lưu trong thư mục dự án không? (Bao gồm: Xem xét cuối giai đoạn, kiểm tra trước bàn giao, xem xét các sản phẩm, đánh giá Baseline, đánh giá nội bộ)",,,,
,,,,
,,,,
* Nhận xét,,,,
,,,,
,,,,
,,,,
,,,,
* Đề xuất,,,,
,,,,
[X] - Đạt,,,,
[  ] - Xem xét lại,,,,
[  ] - Khác,,,,
