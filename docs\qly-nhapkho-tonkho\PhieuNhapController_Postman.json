{"info": {"_postman_id": "b2c3d4e5-f6g7-8901-hijk-lm2345678901", "name": "HieuThuoc - PhieuNhap API Tests", "description": "Collection for testing PhieuNhapController APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Search PhieuNhap", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/search", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm phiếu nhập với phân trang"}, "response": []}, {"name": "Search PhieuNhap By NhaCungCap Name", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"keyWord\": \"ABC\",\n  \"currentPage\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/search", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm phiếu nhập theo tên nhà cung cấp"}, "response": []}, {"name": "Search PhieuNhap - Invalid Page Si<PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 0,\n  \"size\": 0\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/search", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm phiếu nhập với kích thước trang không hợp lệ"}, "response": []}, {"name": "Search PhieuNhap - Invalid Sort Field", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 0,\n  \"size\": 20,\n  \"sortedField\": \"fieldNotExist\"\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/search", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "search"]}, "description": "<PERSON><PERSON><PERSON> kiếm phiếu nhập với sắp xếp theo trường không tồn tại"}, "response": []}, {"name": "Get PhieuNhap By ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/phieunhap/get?id=1", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "get"], "query": [{"key": "id", "value": "1"}]}, "description": "<PERSON><PERSON><PERSON> thông tin phiếu nhập theo ID"}, "response": []}, {"name": "Get PhieuNhap By ID - Not Found", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/phieunhap/get?id=999", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "get"], "query": [{"key": "id", "value": "999"}]}, "description": "<PERSON><PERSON><PERSON> thông tin phiếu nhập với ID không tồn tại"}, "response": []}, {"name": "Create <PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 1000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 100,\n      \"donGia\": 10000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/create", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "create"]}, "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> nh<PERSON>p mới"}, "response": []}, {"name": "Create PhieuNhap - Invalid NhaCungCap", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nhaCungCapId\": 999,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 1000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 100,\n      \"donGia\": 10000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/create", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "create"]}, "description": "<PERSON><PERSON><PERSON> phi<PERSON>u nhập với nhà cung cấp không tồn tại"}, "response": []}, {"name": "Create <PERSON>eu<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 999,\n  \"tongTien\": 1000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 100,\n      \"donGia\": 10000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/create", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "create"]}, "description": "<PERSON><PERSON><PERSON> p<PERSON>u nhập với người dùng không tồn tại"}, "response": []}, {"name": "Create <PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 1000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"thuocId\": 999,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 100,\n      \"donGia\": 10000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/create", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "create"]}, "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>u nhập với thuốc không tồn tại"}, "response": []}, {"name": "Create PhieuNhap - Invalid Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": -1000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": -10,\n      \"donGia\": -1000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/create", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "create"]}, "description": "<PERSON><PERSON><PERSON> phi<PERSON>u nhập với dữ liệu không hợp lệ"}, "response": []}, {"name": "Update PhieuNhap", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 2000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"id\": 1,\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 200,\n      \"donGia\": 10000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/update", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật thông tin phiếu nhập"}, "response": []}, {"name": "Update PhieuNhap - Not Found", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 999,\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 1000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 100,\n      \"donGia\": 10000\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/phieunhap/update", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật thông tin phiếu nhập không tồn tại"}, "response": []}, {"name": "Delete PhieuNhap", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/phieunhap/delete?id=1", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "delete"], "query": [{"key": "id", "value": "1"}]}, "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> nh<PERSON>p theo <PERSON>"}, "response": []}, {"name": "Delete PhieuNhap - Not Found", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/phieunhap/delete?id=999", "host": ["{{baseUrl}}"], "path": ["p<PERSON><PERSON>nh<PERSON>", "delete"], "query": [{"key": "id", "value": "999"}]}, "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>u nhập không tồn tại"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}]}