<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <div class="row">
        <div class="col-xxl-3">
          <div class="card">
            <div class="card-body p-4">
              <div class="text-center">
                <div
                  class="profile-user position-relative d-inline-block mx-auto mb-4"
                >
                  <img
                    [src]="imageUrl"
                    class="rounded-circle avatar-xl img-thumbnail user-profile-image shadow"
                    alt="user-profile-image"
                    *ngIf="imageUrl"
                  />
                  <img
                    [src]="userUpdate.avatar"
                    class="rounded-circle avatar-xl img-thumbnail user-profile-image shadow"
                    *ngIf="userUpdate.avatar && !imageUrl"
                    alt="user-profile-image"
                  />

                  <img
                    src="assets/images/no_image.png"
                    class="rounded-circle avatar-xl img-thumbnail user-profile-image shadow"
                    *ngIf="!userUpdate.avatar && !imageUrl"
                    alt="user-profile-image"
                  />
                </div>
                <h5 class="fs-16 mb-1">
                  {{ user.hoTen }} / {{ user.soDienThoai }}
                </h5>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-body">
              <div
                class="mb-3 d-flex align-items-center cursor-pointer"
                [routerLink]="['/user/profile']"
              >
                <div class="avatar-xs d-block flex-shrink-0 me-3">
                  <i
                    class="mdi mdi-account-circle text-muted fs-3 align-middle me-1"
                  ></i>
                </div>
                <div>Thông tin cá nhân</div>
              </div>
              <div
                class="d-flex align-items-center mb-3 cursor-pointer"
                [routerLink]="['/user/thongbao']"
              >
                <div class="avatar-xs d-block flex-shrink-0 me-3">
                  <i class="bx bx-bell fs-24 align-middle"></i>
                </div>
                <div>Thông báo</div>
              </div>
              <div
                class="d-flex align-items-center bg-soft-info cursor-pointer mb-3"
                [routerLink]="['/user/donmua']"
              >
                <div class="avatar-xs d-block flex-shrink-0 me-3">
                  <i class="ri-store-2-fill me-1 align-middle fs-3"></i>
                </div>
                <div>Đơn mua</div>
              </div>

              <div
                class="d-flex align-items-center cursor-pointer"
                (click)="logout()"
              >
                <div class="avatar-xs d-block flex-shrink-0 me-3">
                  <i
                    class="mdi mdi-logout text-muted fs-3 align-middle me-1"
                  ></i>
                </div>
                <div>Đăng xuất</div>
              </div>
            </div>
          </div>
          <!--end card-->
        </div>
        <!--end col-->
        <div class="col-xxl-9">
          <div class="col-lg-12">
            <div class="card" id="orderList">
              <div class="card-header border-0">
                <div class="d-flex align-items-center">
                  <!-- <h5 class="card-title mb-0 flex-grow-1 ">Order History</h5> -->
                  <!-- <div class="flex-shrink-0">
                    <button
                      type="button"
                      class="btn btn-success add-btn"
                      data-bs-toggle="modal"
                      id="create-btn"
                      data-bs-target="#showModal"
                    >
                      <i class="ri-add-line align-bottom me-1"></i> Create Order
                    </button>
                  </div> -->
                </div>
              </div>

              <div class="card-body pt-0">
                <div>
                  <ul
                    class="nav nav-tabs nav-tabs-custom nav-success mb-3"
                    role="tablist"
                  >
                    <li class="nav-item">
                      <a
                        class="nav-link active All py-3 cursor-pointer"
                        data-bs-toggle="tab"
                        id="All"
                        (click)="changeTab(1)"
                        role="tab"
                        aria-selected="true"
                      >
                        <i class="ri-store-2-fill me-1 align-bottom"></i>
                        Tất cả
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link py-3 Delivered cursor-pointer"
                        data-bs-toggle="tab"
                        id="Delivered"
                        (click)="changeTab(2)"
                        role="tab"
                        aria-selected="false"
                      >
                        <i
                          class="ri-checkbox-circle-line me-1 align-bottom"
                        ></i>
                        Đang xử lý
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link py-3 Pickups cursor-pointer"
                        data-bs-toggle="tab"
                        id="Pickups"
                        (click)="changeTab(3)"
                        role="tab"
                        aria-selected="false"
                      >
                        <i class="ri-truck-line me-1 align-bottom"></i>
                        Đang giao
                        <span class="badge bg-danger align-middle ms-1"></span>
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link py-3 Returns cursor-pointer"
                        data-bs-toggle="tab"
                        id="Returns"
                        (click)="changeTab(4)"
                        role="tab"
                        aria-selected="false"
                      >
                        <i
                          class="ri-checkbox-circle-line me-1 align-bottom"
                        ></i>
                        Đã giao
                      </a>
                    </li>
                    <li class="nav-item">
                      <a
                        class="nav-link py-3 Cancelled cursor-pointer"
                        data-bs-toggle="tab"
                        id="Cancelled"
                        (click)="changeTab(5)"
                        role="tab"
                        aria-selected="false"
                      >
                        <i class="ri-close-circle-line me-1 align-bottom"></i>
                        Đã hủy
                      </a>
                    </li>

                    <li class="nav-item">
                      <a
                        class="nav-link py-3 Cancelled cursor-pointer"
                        data-bs-toggle="tab"
                        id="Cancelled"
                        (click)="changeTab(6)"
                        role="tab"
                        aria-selected="false"
                      >
                        <i
                          class="ri-arrow-left-right-fill me-1 align-bottom"
                        ></i>
                        Trả hàng
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div *ngIf="donhangLst.length == 0">
              <div class="card" id="orderList">
                <div class="card-header border-0">
                  <div class="d-flex align-items-center"></div>
                </div>

                <div class="card-body pt-0">
                  <div>
                    <div class="table-responsive table-card mb-1">
                      <div class="noresult">
                        <div class="text-center">
                          <lord-icon
                            src="https://cdn.lordicon.com/msoeawqm.json"
                            trigger="loop"
                            colors="primary:#405189,secondary:#0ab39c"
                            style="width: 75px; height: 75px"
                          ></lord-icon>
                          <h5 class="mt-2">Không có đơn hàng nào</h5>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div *ngIf="donhangLst.length > 0">
              <div
                class="card"
                id="orderList"
                *ngFor="let donhang of donhangLst"
              >
                <div class="card-header border-1">
                  <div class="d-flex align-items-center">
                    <h5 class="card-title mb-0 flex-grow-1"></h5>
                    <div class="flex-shrink-0">
                      <h5
                        class="text-danger"
                        *ngIf="
                          donhang.trangThaiGiaoHang ==
                          TrangThaiGiaoHang.DANG_XU_LY
                        "
                      >
                        ĐANG XỬ LÝ
                      </h5>

                      <h5
                        class="text-danger"
                        *ngIf="
                          donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DA_HUY
                        "
                      >
                        ĐÃ HỦY
                      </h5>

                      <h5
                        class="text-danger"
                        *ngIf="
                          donhang.trangThaiGiaoHang ==
                          TrangThaiGiaoHang.DANG_GIAO
                        "
                      >
                        ĐANG GIAO
                      </h5>

                      <h5
                        class="text-danger"
                        *ngIf="
                          donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DA_GIAO
                        "
                      >
                        ĐÃ GIAO
                      </h5>

                      <h5
                        class="text-danger"
                        *ngIf="
                          donhang.trangThaiGiaoHang ==
                          TrangThaiGiaoHang.TRA_HANG
                        "
                      >
                        TRẢ HÀNG
                      </h5>
                    </div>
                  </div>
                </div>

                <div class="card-body pt-0 mt-3">
                  <div
                    class="row gy-3 mb-3"
                    *ngFor="let item of donhang.chiTietDonHangs"
                  >
                    <div class="col-sm-auto">
                      <div
                        class="avatar-lg bg-light rounded p-1 cursor-pointer"
                        (click)="showDetail(item.thuoc)"
                      >
                        <img
                          [src]="item.thuoc?.avatar"
                          alt=""
                          class="img-fluid d-block"
                        />
                      </div>
                    </div>

                    <div class="col-sm">
                      <h5 class="fs-14 text-truncate">
                        <a class="text-dark">{{ item.thuoc?.tenThuoc }}</a>
                      </h5>
                      <ul class="list-inline text-muted">
                        <li class="list-inline-item">
                          Số lượng :
                          <span class="fw-medium">{{ item.soLuong }}</span>
                        </li>
                        <!-- <li class="list-inline-item">
                            Size : <span class="fw-medium">M</span>
                          </li> -->
                      </ul>
                    </div>

                    <div class="col-sm-auto">
                      <div class="text-lg-end">
                        <p class="text-muted mb-1">Giá:</p>
                        <h5 class="fs-14">
                          <span
                            id="ticket_price"
                            class="product-price text-danger"
                          >
                            {{
                              (item.donGia || 0) * (item.soLuong || 0)
                                | number : "1.0-0"
                            }}
                            VNĐ</span
                          >
                        </h5>
                      </div>
                    </div>
                  </div>

                  <div
                    class="modal fade"
                    id="showModal"
                    tabindex="-1"
                    aria-labelledby="exampleModalLabel"
                    aria-hidden="true"
                  >
                    <div class="modal-dialog modal-dialog-centered">
                      <div class="modal-content">
                        <div class="modal-header bg-light p-3">
                          <h5 class="modal-title" id="exampleModalLabel">
                            Đánh giá sản phẩm
                          </h5>
                          <button
                            type="button"
                            class="btn-close"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                            id="close-modal"
                          ></button>
                        </div>
                        <div class="modal-body">
                          <input type="hidden" id="id-field" />

                          <div
                            class="row gy-3 mb-3"
                            *ngFor="let item of donhangDanhGia.chiTietDonHangs"
                          >
                            <div class="col-sm-auto">
                              <div
                                class="avatar-lg bg-light rounded p-1 cursor-pointer"
                                (click)="showDetail(item.thuoc)"
                              >
                                <img
                                  [src]="item.thuoc?.avatar"
                                  alt=""
                                  class="img-fluid d-block"
                                />
                              </div>
                            </div>

                            <div class="col-sm">
                              <h5 class="fs-14 text-truncate">
                                <a class="text-dark">{{
                                  item.thuoc?.tenThuoc
                                }}</a>
                              </h5>
                              <ul class="list-inline text-muted">
                                <li class="list-inline-item">
                                  Số lượng :
                                  <span class="fw-medium">{{
                                    item.soLuong
                                  }}</span>
                                </li>
                                <!-- <li class="list-inline-item">
                                    Size : <span class="fw-medium">M</span>
                                  </li> -->
                              </ul>
                            </div>

                            <div class="col-sm-auto">
                              <div class="text-lg-end">
                                <p class="text-muted mb-1">Giá:</p>
                                <h5 class="fs-14">
                                  <span
                                    id="ticket_price"
                                    class="product-price text-danger"
                                  >
                                    {{
                                      (item.donGia || 0) * (item.soLuong || 0)
                                        | number : "1.0-0"
                                    }}
                                    VNĐ</span
                                  >
                                </h5>
                              </div>
                            </div>
                          </div>

                          <div class="mb-3" id="modal-id">
                            <div
                              class="d-flex flex-wrap gap-2 align-items-center mt-3"
                            >
                              <div class="fw-semibold">Chất lượng sản phẩm</div>

                              <div class="text-muted fs-16">
                                <span
                                  *ngFor="
                                    let star of [].constructor(5);
                                    let i = index
                                  "
                                  class="mdi mdi-star"
                                  [class.text-warning]="
                                    i < (danhGia.diemSo || 0)
                                  "
                                  [class.text-muted]="
                                    i >= (danhGia.diemSo || 0)
                                  "
                                  (click)="updateDiemSo(i + 1)"
                                  style="cursor: pointer"
                                ></span>
                              </div>
                            </div>
                          </div>

                          <div class="mb-3" id="modal-id">
                            <label for="orderId" class="form-label"
                              >Đánh giá</label
                            >
                            <textarea
                              type="text"
                              id="orderId"
                              class="form-control"
                              placeholder="Nhập đánh giá"
                              [(ngModel)]="danhGia.danhGia"
                            >
                            </textarea>
                          </div>
                        </div>
                        <div class="modal-footer">
                          <div class="hstack gap-2 justify-content-end">
                            <button
                              type="button"
                              class="btn btn-light"
                              data-bs-dismiss="modal"
                            >
                              Trở lại
                            </button>
                            <button
                              type="submit"
                              class="btn btn-success"
                              (click)="danhGiaDH()"
                              id="add-btn"
                              data-bs-dismiss="modal"
                            >
                              Đánh giá
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="card-footer">
                  <div class="row align-items-center gy-3">
                    <div class="col-sm">
                      <div class="d-flex flex-wrap my-n1">
                        <!-- <div>
                            <a href="#" class="d-block text-body p-1 px-2"
                              ><i
                                class="ri-star-fill text-muted align-bottom me-1"
                              ></i>
                              Add Wishlist</a
                            >
                          </div> -->
                      </div>
                    </div>
                    <div class="col-sm-auto d-flex">
                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang ==
                          TrangThaiGiaoHang.DANG_XU_LY
                        "
                      >
                        <div>
                          <button
                            (click)="huyDonHang(donhang)"
                            type="submit"
                            class="btn btn-outline-danger me-2"
                          >
                            Hủy đơn hàng
                          </button>
                        </div>
                      </div>

                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang ==
                          TrangThaiGiaoHang.DANG_GIAO
                        "
                      >
                        <div>
                          <button
                            (click)="nhanDonHang(donhang)"
                            type="submit"
                            class="btn btn-danger me-2"
                          >
                            Đã nhận hàng
                          </button>
                        </div>
                      </div>

                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DA_GIAO
                        "
                      >
                        <div>
                          <button
                            type="submit"
                            class="btn btn-info me-2"
                            id="danhgia"
                            data-bs-target="#showModal"
                            data-bs-toggle="modal"
                            id="create-btn"
                            (click)="donhangDanhGia = donhang"
                          >
                            Đánh giá
                          </button>
                        </div>
                      </div>

                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DA_GIAO
                        "
                      >
                        <div>
                          <button
                            type="submit"
                            class="btn btn-outline-info me-2"
                            (click)="traDonHang(donhang)"
                          >
                            Yêu cầu trả hàng/ hoàn tiền
                          </button>
                        </div>
                      </div>

                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang ==
                          TrangThaiGiaoHang.TRA_HANG
                        "
                      >
                        <div>
                          <button
                            type="submit"
                            class="btn btn-outline-info me-2"
                            (click)="xemChiTiet(donhang)"
                          >
                            Xem chi tiết
                          </button>
                        </div>
                      </div>

                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang ==
                          TrangThaiGiaoHang.DANG_GIAO
                        "
                      >
                        <div>
                          <button
                            type="submit"
                            class="btn btn-outline-info me-2"
                            (click)="xemChiTiet(donhang)"
                          >
                            Xem chi tiết
                          </button>
                        </div>
                      </div>

                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang ==
                          TrangThaiGiaoHang.DANG_XU_LY
                        "
                      >
                        <div>
                          <button
                            type="submit"
                            class="btn btn-outline-info me-2"
                            (click)="xemChiTiet(donhang)"
                          >
                            Xem chi tiết
                          </button>
                        </div>
                      </div>

                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DA_GIAO
                        "
                      >
                        <div>
                          <button
                            type="submit"
                            class="btn btn-outline-info me-2"
                            (click)="xemChiTiet(donhang)"
                          >
                            Xem chi tiết
                          </button>
                        </div>
                      </div>

                      <div
                        class="d-flex flex-wrap my-n1"
                        *ngIf="
                          donhang.trangThaiGiaoHang == TrangThaiGiaoHang.DA_HUY
                        "
                      >
                        <div>
                          <button
                            type="submit"
                            class="btn btn-outline-info me-2"
                            (click)="xemChiTiet(donhang)"
                          >
                            Xem chi tiết
                          </button>
                        </div>
                      </div>

                      <div class="d-flex align-items-center gap-2 text-muted">
                        <div>Tổng :</div>
                        <h5 class="fs-14 mb-0">
                          <span
                            class="fw-semibold product-line-price text-danger"
                          >
                            {{ donhang.tongTien || 0 | number : "1.0-0" }}
                            VNĐ
                          </span>
                        </h5>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
