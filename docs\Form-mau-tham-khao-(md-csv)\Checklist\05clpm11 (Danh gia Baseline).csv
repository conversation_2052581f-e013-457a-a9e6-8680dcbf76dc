Checklist đánh gi<PERSON> ,,,,
,,,,
<PERSON>ã dự án,,,,
<PERSON><PERSON> hiệu <PERSON>,,,,
<PERSON><PERSON><PERSON> đ<PERSON>h giá,,,,
<PERSON><PERSON><PERSON><PERSON> thực hiện,,,,
<PERSON>uồ<PERSON> lực dành để đánh giá (MH) ,,,,
,,,,
<PERSON><PERSON><PERSON> hỏi,<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON> chú
Kiểm tra các sản phẩm ,,,,
<PERSON><PERSON>ững sản phẩm do khách hàng cung cấp:,,,,
Lịch trình,,,,
<PERSON><PERSON> tả yêu cầu của người sử dụng,,,,
Đặc tả,,,,
Mã nguồn nguyên thủy,,,,
<PERSON><PERSON><PERSON> template,,,,
<PERSON>ẩ<PERSON> lập trình,,,,
Kiểm tra những sản phẩm tái sử dụng trong dự án có lưu đủ không?,,,,
<PERSON><PERSON><PERSON> tra checklists,,,,
<PERSON><PERSON> bằng chứng xem xét và phê duyệt KHDA không?,,,,
<PERSON><PERSON> sản phẩm nào của khách hàng bắt buộc review không?,,,,
<PERSON><PERSON><PERSON> có thì có được review không?,,,,
Có tên người review không?,,,,
Kiểm tra navisoft Insight,,,,
WO,,,,
Deliverable,,,,
Lịch bàn giao có đúng như trong K<PERSON>A không?,,,,
Metrics,,,,
<PERSON>ó cập nhật các chỉ tiêu do khách hàng yêu cầu không?,,,,
Product,,,,
Có liệt kê đủ các sản phẩm của dự án không?,,,,
Có liệt kê các sản phẩm do khách hàng cung cấp không?,,,,
Độ lớn sản phẩm,,,,
Có độ lớn dự kiến cho các sản phẩm không?,,,,
Có độ lớn thực tế cho các sản phẩm được baseline không?,,,,
Schedule,,,,
Review schedule,,,,
"Có lịch xem xét cho tất cả các sản phẩm, kể cả do khách hàng cung cấp không?",,,,
Other Quality schedule,,,,
Đánh giá Baseline ,,,,
Kiểm soát trước bàn giao không?,,,,
Effort,,,,
Review effort,,,,
Có dự kiến nhân công xem xét không?,,,,
Review effort for other quality activities,,,,
Có dự kiến nhân công cho đánh giá Baseline,,,,
Có dự kiến nhân công cho kiểm soát trước bàn giao không?,,,,
Có dự kiến nhân công cho kiểm soát cuối giai đoạn không?,,,,
"Kiểm tra thư mục dự án, backup",,,,
Thư mục dự án có đúng như trong KHDA không?,,,,
Kiểm tra thư mục dự án xem có lưu đủ các thư mục phục vụ các mục đích: WIP - Control - Baseline hay không?,,,,
"Kiểm tra trong thư mục Control, Các CI đã được cập nhật đúng chưa?",,,,
Các CI được baseline có được đặt đúng thư mục Baseline như xác định trong kế hoạch quản lý cấu hình không?,,,,
Có thực hiện backup như xác định trong kế hoạch quản lý cấu hình không?,,,,
Quyền truy cập của các nhóm trong dự án vào các CI có đúng không?,,,,
Quản lý yêu cầu,,,,
Các yêu cầu đã được hoàn thành có được cập nhật vào các CI được baseline không?,,,,
Các thay đổi yêu cầu đã được hoàn thiện trong các sản phẩm chưa?,,,,
Các yêu cầu đã được cập nhật trạng thái trong Requirement-Sheet (RMSheet) hay chưa?,,,,
Đánh giá Baseline,,,,
CI Register có được cập nhật đầy đủ không?,,,,
Số lượng CI?,,,,
Phiên bản?,,,,
Baseline plan,,,,
Có định nghĩa Baseline Audit trong KHDA không?,,,,
Có thời điểm ghi nhận để thực hiện baseline không?,,,,
Các sản phẩm được Baseline có theo như kế hoạch quản lý cấu hình không?,,,,
Baseline có được hoàn thành với tất cả các CI yêu cầu không?,,,,
Các CI trong baseline có được xem xét và phê duyệt không?,,,,
Có báo cáo xem xét\lỗi không? ,,,,
Các lỗi của từng CI có được đóng trước khi làm baseline không?,,,,
"Có liệt kê ra các lỗi chưa đóng trong báo cáo BL không?
(Kiểm tra các lỗi mở trong TestTrack và so sánh với báo cáo)",,,,
"Các sản phẩm bàn giao có được kiểm tra cuối trước khi bàn giao không?
(Các sản phẩm trước khi bàn giao phải được SQA làm Final inspeciton, nếu không đạt thì trưởng dự án phải kiểm tra lại và sửa cho đạt trước khi bàn giao, nêu sản phẩm không cần final inspection thì trưởng dự án phải có tailoring).",,,,
Các CI trong baseline có tuân theo qui tắc đặt tên trong kế hoạch quản lý cấu hình không? ,,,,
Các CI trong baseline có tuân theo qui tắc đánh số phiên bản không? ,,,,
Các CI có Record of change hay không?,,,,
Báo cáo baseline và các phiên bản CI có tương thích không?  ,,,,
"Nếu có báo cáo cấu hình thì đã phê duyệt chưa?
(Thường là người phê duyệt phải trên 1 cấp so với người lập)",,,,
Các vấn đề thiếu sót của lần baseline trước đã được đóng chưa?,,,,
,,0,,0
 * Nhận xét,,,,
* Đề xuất,,,,
[X] - Đạt,,,,
[   ] - Đánh giá lại,,,,
[   ] - Khác,,,,
