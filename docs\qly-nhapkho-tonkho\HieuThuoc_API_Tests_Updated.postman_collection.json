{
	"info": {
		"_postman_id": "d4e5f6g7-h8i9-0123-jklm-n45678901234",
		"name": "HieuThuoc - API Tests",
		"description": "Collection for testing HieuThuoc APIs",
		"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
	},
	"item": [
		{
			"name": "NhaCungCap API",
			"item": [
				{
					"name": "Get All NhaCungCap",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/nhacungcap/list",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"nhacungcap",
								"list"
							]
						},
						"description": "<PERSON><PERSON>y danh sách tất cả nhà cung cấp"
					},
					"response": []
				},
				{
					"name": "Search NhaCungCap By Name",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=ABC",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"nhacungcap",
								"search_by_ten_nha_cung_cap"
							],
							"query": [
								{
									"key": "tenNhaCungCap",
									"value": "ABC"
								}
							]
						},
						"description": "Tìm kiếm nhà cung cấp theo tên"
					},
					"response": []
				},
				{
					"name": "Create NhaCungCap",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"maNCC\": \"NCC001\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 1\",\n  \"diaChi\": \"Địa chỉ 1\",\n  \"soDienThoai\": \"0123456789\",\n  \"email\": \"<EMAIL>\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/nhacungcap/create",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"nhacungcap",
								"create"
							]
						},
						"description": "Tạo mới nhà cung cấp"
					},
					"response": []
				},
				{
					"name": "Update NhaCungCap",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"id\": 1,\n  \"maNCC\": \"NCC001\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 1 Updated\",\n  \"diaChi\": \"Địa chỉ 1 Updated\",\n  \"soDienThoai\": \"0123456789\",\n  \"email\": \"<EMAIL>\"\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/nhacungcap/update",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"nhacungcap",
								"update"
							]
						},
						"description": "Cập nhật thông tin nhà cung cấp"
					},
					"response": []
				},
				{
					"name": "Delete NhaCungCap",
					"request": {
						"method": "DELETE",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/nhacungcap/delete?id=1",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"nhacungcap",
								"delete"
							],
							"query": [
								{
									"key": "id",
									"value": "1"
								}
							]
						},
						"description": "Xóa nhà cung cấp theo ID"
					},
					"response": []
				}
			],
			"description": "API endpoints for NhaCungCap"
		},
		{
			"name": "PhieuNhap API",
			"item": [
				{
					"name": "Search PhieuNhap",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"currentPage\": 0,\n  \"size\": 20\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/phieunhap/search",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"phieunhap",
								"search"
							]
						},
						"description": "Tìm kiếm phiếu nhập với phân trang"
					},
					"response": []
				},
				{
					"name": "Get PhieuNhap By ID",
					"request": {
						"method": "GET",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/phieunhap/get?id=1",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"phieunhap",
								"get"
							],
							"query": [
								{
									"key": "id",
									"value": "1"
								}
							]
						},
						"description": "Lấy thông tin phiếu nhập theo ID"
					},
					"response": []
				},
				{
					"name": "Create PhieuNhap",
					"request": {
						"method": "POST",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 1000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 100,\n      \"donGia\": 10000\n    }\n  ]\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/phieunhap/create",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"phieunhap",
								"create"
							]
						},
						"description": "Tạo phiếu nhập mới"
					},
					"response": []
				},
				{
					"name": "Update PhieuNhap",
					"request": {
						"method": "PUT",
						"header": [
							{
								"key": "Content-Type",
								"value": "application/json"
							}
						],
						"body": {
							"mode": "raw",
							"raw": "{\n  \"id\": 1,\n  \"nhaCungCapId\": 1,\n  \"nguoiDungId\": 1,\n  \"tongTien\": 2000000,\n  \"chiTietPhieuNhaps\": [\n    {\n      \"id\": 1,\n      \"thuocId\": 1,\n      \"hanSuDung\": \"2025-12-31\",\n      \"soLuong\": 200,\n      \"donGia\": 10000\n    }\n  ]\n}"
						},
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/phieunhap/update",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"phieunhap",
								"update"
							]
						},
						"description": "Cập nhật thông tin phiếu nhập"
					},
					"response": []
				},
				{
					"name": "Delete PhieuNhap",
					"request": {
						"method": "DELETE",
						"header": [],
						"url": {
							"raw": "{{baseUrl}}/hieuthuoc/phieunhap/delete?id=1",
							"host": [
								"{{baseUrl}}"
							],
							"path": [
								"hieuthuoc",
								"phieunhap",
								"delete"
							],
							"query": [
								{
									"key": "id",
									"value": "1"
								}
							]
						},
						"description": "Xóa phiếu nhập theo ID"
					},
					"response": []
				}
			],
			"description": "API endpoints for PhieuNhap"
		}
	},
	{
		"name": "TonKho API",
		"item": [
			{
				"name": "Search TonKho",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"currentPage\": 0,\n  \"size\": 20\n}"
					},
					"url": {
						"raw": "{{baseUrl}}/hieuthuoc/tonkho/search",
						"host": [
							"{{baseUrl}}"
						],
						"path": [
							"hieuthuoc",
							"tonkho",
							"search"
						]
					},
					"description": "Tìm kiếm tồn kho với phân trang"
				},
				"response": []
			},
			{
				"name": "Search TonKho By Thuoc Name",
				"request": {
					"method": "POST",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"tenThuoc\": \"Paracetamol\",\n  \"currentPage\": 0,\n  \"size\": 20\n}"
					},
					"url": {
						"raw": "{{baseUrl}}/hieuthuoc/tonkho/search",
						"host": [
							"{{baseUrl}}"
						],
						"path": [
							"hieuthuoc",
							"tonkho",
							"search"
						]
					},
					"description": "Tìm kiếm tồn kho theo tên thuốc"
				},
				"response": []
			},
			{
				"name": "Update TonKho",
				"request": {
					"method": "PUT",
					"header": [
						{
							"key": "Content-Type",
							"value": "application/json"
						}
					],
					"body": {
						"mode": "raw",
						"raw": "{\n  \"id\": 1,\n  \"thuocId\": 1,\n  \"soLo\": \"L001\",\n  \"hanSuDung\": \"2025-12-31\",\n  \"soLuong\": 200,\n  \"viTri\": \"Kệ A-01\"\n}"
					},
					"url": {
						"raw": "{{baseUrl}}/hieuthuoc/tonkho/update",
						"host": [
							"{{baseUrl}}"
						],
						"path": [
							"hieuthuoc",
							"tonkho",
							"update"
						]
					},
					"description": "Cập nhật tồn kho"
				},
				"response": []
			}
		],
		"description": "API endpoints for TonKho"
	}
],
"event": [
	{
		"listen": "prerequest",
		"script": {
			"type": "text/javascript",
			"exec": [
				""
			]
		}
	},
	{
		"listen": "test",
		"script": {
			"type": "text/javascript",
			"exec": [
				""
			]
		}
	}
],
"variable": [
	{
		"key": "baseUrl",
		"value": "http://localhost:8888",
		"type": "string"
	}
]
}