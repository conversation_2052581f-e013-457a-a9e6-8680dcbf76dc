Test Case ID,UI Screen,Purpose,Steps,Expected Results,Actual Results,<PERSON><PERSON><PERSON>ails,Evaluation
UI-TK-001,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> tra tiêu đề trang,"1. Mở màn hình Tồn <PERSON>ho
2. <PERSON><PERSON><PERSON> tra tiêu đề trang","Tiêu đề ""TỒN KHO"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-002,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> tra nhãn cột ID,"1. Mở màn hình Tồn Kho
2. <PERSON><PERSON><PERSON> tra nhãn cột ID","<PERSON>hãn ""ID"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-003,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> tra nhãn cột <PERSON>ã số,"1. Mở màn hình <PERSON>ồ<PERSON>
2. <PERSON><PERSON><PERSON> tra nhãn cột <PERSON>ã số","<PERSON>h<PERSON><PERSON> ""<PERSON><PERSON> số"" hiển thị đúng ch<PERSON> tả, font chữ và kích thư<PERSON>c phù hợp",,,
UI-TK-004,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> tra nhãn cột Tên thuốc,"1. Mở màn hình Tồn Kho
2. Kiểm tra nhãn cột Tên thuốc","Nhãn ""Tên thuốc"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-005,Tồn Kho,Kiểm tra nhãn cột Đơn vị,"1. Mở màn hình Tồn Kho
2. Kiểm tra nhãn cột Đơn vị","Nhãn ""Đơn vị"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-006,Tồn Kho,Kiểm tra nhãn cột Số lượng tồn,"1. Mở màn hình Tồn Kho
2. Kiểm tra nhãn cột Số lượng tồn","Nhãn ""Số lượng tồn"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-007,Tồn Kho,Kiểm tra căn chỉnh các cột,"1. Mở màn hình Tồn Kho
2. Kiểm tra căn chỉnh của các cột","Các cột được căn chỉnh hợp lý (ID và Mã số căn trái, Tên thuốc căn trái, Đơn vị căn giữa, Số lượng tồn căn phải)",,,
UI-TK-008,Tồn Kho,Kiểm tra ô tìm kiếm,"1. Mở màn hình Tồn Kho
2. Kiểm tra ô tìm kiếm","Ô tìm kiếm hiển thị với placeholder phù hợp, kích thước đủ lớn để nhập dữ liệu",,,
UI-TK-009,Tồn Kho,Kiểm tra nút Tìm kiếm,"1. Mở màn hình Tồn Kho
2. Kiểm tra nút Tìm kiếm","Nút Tìm kiếm hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-TK-010,Tồn Kho,Kiểm tra tìm kiếm với từ khóa hợp lệ,"1. Nhập từ khóa hợp lệ (ví dụ: ""Amoxicillin"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa ""Amoxicillin""",,,
UI-TK-011,Tồn Kho,Kiểm tra tìm kiếm với từ khóa không hợp lệ,"1. Nhập từ khóa không hợp lệ (ví dụ: ""xyz123"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị thông báo không tìm thấy kết quả hoặc danh sách trống",,,
UI-TK-012,Tồn Kho,Kiểm tra tìm kiếm với ký tự đặc biệt,"1. Nhập ký tự đặc biệt (ví dụ: ""@#$%"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống xử lý đúng, không bị lỗi và hiển thị kết quả phù hợp hoặc thông báo không tìm thấy",,,
UI-TK-013,Tồn Kho,Kiểm tra tìm kiếm với chuỗi dài,"1. Nhập chuỗi dài (>50 ký tự) vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống xử lý đúng, không bị lỗi và hiển thị kết quả phù hợp hoặc thông báo không tìm thấy",,,
UI-TK-014,Tồn Kho,Kiểm tra nút phân trang Trước,"1. Nếu có nhiều hơn 10 mục, chuyển đến trang 2
2. Nhấn nút Trước","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-TK-015,Tồn Kho,Kiểm tra nút phân trang Sau,"1. Nếu đang ở trang 1 và có nhiều hơn 10 mục
2. Nhấn nút Sau","Hệ thống chuyển đến trang 2 và hiển thị dữ liệu chính xác",,,
UI-TK-016,Tồn Kho,Kiểm tra nút phân trang Đầu tiên,"1. Nếu đang ở trang khác trang 1
2. Nhấn nút Đầu tiên (<<)","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-TK-017,Tồn Kho,Kiểm tra nút phân trang Cuối cùng,"1. Nếu đang ở trang không phải trang cuối
2. Nhấn nút Cuối cùng (>>)","Hệ thống chuyển đến trang cuối và hiển thị dữ liệu chính xác",,,
UI-TK-018,Tồn Kho,Kiểm tra nhấp vào số trang,"1. Nếu có nhiều trang
2. Nhấn vào số trang cụ thể (ví dụ: 3)","Hệ thống chuyển đến trang đã chọn và hiển thị dữ liệu chính xác",,,
UI-TK-019,Tồn Kho,Kiểm tra hiển thị mã thuốc,"1. Mở màn hình Tồn Kho
2. Kiểm tra hiển thị mã thuốc trong bảng","Mã thuốc hiển thị đúng định dạng, không bị cắt ngắn",,,
UI-TK-020,Tồn Kho,Kiểm tra hiển thị tên thuốc,"1. Mở màn hình Tồn Kho
2. Kiểm tra hiển thị tên thuốc trong bảng","Tên thuốc hiển thị đúng, không bị cắt ngắn hoặc tràn ra ngoài cột",,,
UI-TK-021,Tồn Kho,Kiểm tra hiển thị đơn vị,"1. Mở màn hình Tồn Kho
2. Kiểm tra hiển thị đơn vị trong bảng","Đơn vị hiển thị đúng, không bị cắt ngắn",,,
UI-TK-022,Tồn Kho,Kiểm tra hiển thị số lượng tồn,"1. Mở màn hình Tồn Kho
2. Kiểm tra hiển thị số lượng tồn trong bảng","Số lượng tồn hiển thị đúng định dạng số, căn phải và không bị cắt ngắn",,,
UI-TK-023,Tồn Kho,Kiểm tra hiển thị trên màn hình desktop,"1. Mở màn hình Tồn Kho trên desktop (>1200px)
2. Kiểm tra hiển thị tổng thể","Giao diện hiển thị đầy đủ, các cột căn chỉnh đúng, không có thanh cuộn ngang",,,
UI-TK-024,Tồn Kho,Kiểm tra hiển thị trên màn hình tablet,"1. Mở màn hình Tồn Kho trên tablet (768px-1024px)
2. Kiểm tra hiển thị tổng thể","Giao diện hiển thị phù hợp, có thể có thanh cuộn ngang nhưng vẫn dễ đọc",,,
UI-TK-025,Tồn Kho,Kiểm tra hiển thị trên màn hình mobile,"1. Mở màn hình Tồn Kho trên mobile (<768px)
2. Kiểm tra hiển thị tổng thể","Giao diện hiển thị phù hợp với thiết bị di động, có thể chuyển sang dạng danh sách thay vì bảng",,,
UI-PN-001,Phiếu Nhập,Kiểm tra tiêu đề trang,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra tiêu đề trang","Tiêu đề ""PHIẾU NHẬP"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-002,Phiếu Nhập,Kiểm tra nhãn cột ID,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột ID","Nhãn ""ID"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-003,Phiếu Nhập,Kiểm tra nhãn cột Mã công ty,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột Mã công ty","Nhãn ""Mã công ty"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-004,Phiếu Nhập,Kiểm tra nhãn cột Người nhập,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột Người nhập","Nhãn ""Người nhập"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-005,Phiếu Nhập,Kiểm tra nhãn cột Ngày nhập,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột Ngày nhập","Nhãn ""Ngày nhập"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-006,Phiếu Nhập,Kiểm tra nhãn cột Tổng tiền,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột Tổng tiền","Nhãn ""Tổng tiền"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-007,Phiếu Nhập,Kiểm tra căn chỉnh các cột,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra căn chỉnh của các cột","Các cột được căn chỉnh hợp lý (ID và Mã công ty căn trái, Người nhập căn trái, Ngày nhập căn giữa, Tổng tiền căn phải)",,,
UI-PN-008,Phiếu Nhập,Kiểm tra ô tìm kiếm,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra ô tìm kiếm","Ô tìm kiếm hiển thị với placeholder phù hợp, kích thước đủ lớn để nhập dữ liệu",,,
UI-PN-009,Phiếu Nhập,Kiểm tra nút Tìm kiếm,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nút Tìm kiếm","Nút Tìm kiếm hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-PN-010,Phiếu Nhập,Kiểm tra tìm kiếm với từ khóa hợp lệ,"1. Nhập từ khóa hợp lệ (ví dụ: tên công ty) vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa đã nhập",,,
UI-PN-011,Phiếu Nhập,Kiểm tra tìm kiếm với từ khóa không hợp lệ,"1. Nhập từ khóa không hợp lệ (ví dụ: ""xyz123"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị thông báo không tìm thấy kết quả hoặc danh sách trống",,,
UI-PN-012,Phiếu Nhập,Kiểm tra tìm kiếm với ký tự đặc biệt,"1. Nhập ký tự đặc biệt (ví dụ: ""@#$%"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống xử lý đúng, không bị lỗi và hiển thị kết quả phù hợp hoặc thông báo không tìm thấy",,,
UI-PN-013,Phiếu Nhập,Kiểm tra nút Thêm phiếu nhập - hiển thị,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nút Thêm phiếu nhập","Nút Thêm phiếu nhập hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-PN-014,Phiếu Nhập,Kiểm tra nút Thêm phiếu nhập - chức năng,"1. Nhấn nút Thêm phiếu nhập","Hệ thống chuyển đến màn hình Thêm phiếu nhập",,,
UI-PN-015,Phiếu Nhập,Kiểm tra nút xóa - hiển thị,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra biểu tượng xóa bên cạnh một phiếu nhập","Biểu tượng xóa hiển thị rõ ràng và dễ nhận biết",,,
UI-PN-016,Phiếu Nhập,Kiểm tra nút xóa - chức năng,"1. Nhấn biểu tượng xóa bên cạnh một phiếu nhập","1. Hệ thống hiển thị hộp thoại xác nhận xóa
2. Sau khi xác nhận, phiếu nhập được xóa khỏi danh sách",,,
UI-PN-017,Phiếu Nhập,Kiểm tra hộp thoại xác nhận xóa,"1. Nhấn biểu tượng xóa bên cạnh một phiếu nhập
2. Kiểm tra hộp thoại xác nhận","1. Hộp thoại hiển thị với nội dung rõ ràng
2. Có nút Xác nhận và nút Hủy
3. Nút Hủy hoạt động đúng khi nhấn",,,
UI-PN-018,Phiếu Nhập,Kiểm tra nút phân trang Trước,"1. Nếu có nhiều hơn 10 mục, chuyển đến trang 2
2. Nhấn nút Trước","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-PN-019,Phiếu Nhập,Kiểm tra nút phân trang Sau,"1. Nếu đang ở trang 1 và có nhiều hơn 10 mục
2. Nhấn nút Sau","Hệ thống chuyển đến trang 2 và hiển thị dữ liệu chính xác",,,
UI-PN-020,Phiếu Nhập,Kiểm tra nút phân trang Đầu tiên,"1. Nếu đang ở trang khác trang 1
2. Nhấn nút Đầu tiên (<<)","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-PN-021,Phiếu Nhập,Kiểm tra nút phân trang Cuối cùng,"1. Nếu đang ở trang không phải trang cuối
2. Nhấn nút Cuối cùng (>>)","Hệ thống chuyển đến trang cuối và hiển thị dữ liệu chính xác",,,
UI-PN-022,Phiếu Nhập,Kiểm tra nhấp vào số trang,"1. Nếu có nhiều trang
2. Nhấn vào số trang cụ thể (ví dụ: 3)","Hệ thống chuyển đến trang đã chọn và hiển thị dữ liệu chính xác",,,
UI-PN-023,Phiếu Nhập,Kiểm tra hiển thị mã công ty,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra hiển thị mã công ty trong bảng","Mã công ty hiển thị đúng định dạng, không bị cắt ngắn",,,
UI-PN-024,Phiếu Nhập,Kiểm tra hiển thị người nhập,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra hiển thị người nhập trong bảng","Tên người nhập hiển thị đúng, không bị cắt ngắn hoặc tràn ra ngoài cột",,,
UI-PN-025,Phiếu Nhập,Kiểm tra hiển thị ngày nhập,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra hiển thị ngày nhập trong bảng","Ngày nhập hiển thị đúng định dạng (DD-MM-YYYY), nhất quán giữa các dòng",,,
UI-PN-026,Phiếu Nhập,Kiểm tra hiển thị tổng tiền,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra hiển thị tổng tiền trong bảng","Tổng tiền hiển thị đúng định dạng số, có dấu phân cách hàng nghìn, căn phải và không bị cắt ngắn",,,
UI-TPN-001,Thêm Phiếu Nhập,Kiểm tra tiêu đề trang,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra tiêu đề trang","Tiêu đề ""THÊM PHIẾU NHẬP"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TPN-002,Thêm Phiếu Nhập,Kiểm tra nhãn Người nhập,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nhãn Người nhập","Nhãn ""Người nhập"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TPN-003,Thêm Phiếu Nhập,Kiểm tra nhãn Nhà cung cấp,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nhãn Nhà cung cấp","Nhãn ""Nhà cung cấp"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TPN-004,Thêm Phiếu Nhập,Kiểm tra nhãn Thuốc,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nhãn Thuốc","Nhãn ""Thuốc"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TPN-005,Thêm Phiếu Nhập,Kiểm tra trường nhập Người nhập,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra trường nhập Người nhập","Trường nhập Người nhập hiển thị đúng, có kích thước phù hợp và có thể nhập dữ liệu",,,
UI-TPN-006,Thêm Phiếu Nhập,Kiểm tra dropdown nhà cung cấp - hiển thị,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra dropdown nhà cung cấp","Dropdown nhà cung cấp hiển thị đúng, có kích thước phù hợp và có biểu tượng dropdown",,,
UI-TPN-007,Thêm Phiếu Nhập,Kiểm tra dropdown nhà cung cấp - chức năng,"1. Nhấn vào dropdown nhà cung cấp","Dropdown hiển thị danh sách nhà cung cấp đầy đủ",,,
UI-TPN-008,Thêm Phiếu Nhập,Kiểm tra chọn nhà cung cấp,"1. Nhấn vào dropdown nhà cung cấp
2. Chọn một nhà cung cấp từ danh sách","Sau khi chọn, giá trị được hiển thị trong ô dropdown",,,
UI-TPN-009,Thêm Phiếu Nhập,Kiểm tra nút Thêm - hiển thị,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nút Thêm","Nút Thêm hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-TPN-010,Thêm Phiếu Nhập,Kiểm tra nút Thêm - dữ liệu hợp lệ,"1. Điền đầy đủ thông tin hợp lệ vào form
2. Nhấn nút Thêm","1. Hệ thống kiểm tra dữ liệu hợp lệ
2. Phiếu nhập được tạo thành công
3. Hiển thị thông báo thành công",,,
UI-TPN-011,Thêm Phiếu Nhập,Kiểm tra nút Thêm - dữ liệu không hợp lệ,"1. Điền thông tin không hợp lệ vào form (ví dụ: số lượng âm)
2. Nhấn nút Thêm","1. Hệ thống kiểm tra dữ liệu không hợp lệ
2. Hiển thị thông báo lỗi phù hợp
3. Không tạo phiếu nhập mới",,,
UI-TPN-012,Thêm Phiếu Nhập,Kiểm tra nút Hủy - hiển thị,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nút Hủy","Nút Hủy hiển thị với nhãn đúng, kích thước phù hợp",,,
UI-TPN-013,Thêm Phiếu Nhập,Kiểm tra nút Hủy - chức năng,"1. Điền một số thông tin vào form
2. Nhấn nút Hủy","1. Hệ thống hiển thị hộp thoại xác nhận hủy (nếu có)
2. Sau khi xác nhận, hệ thống quay lại màn hình Phiếu Nhập
3. Dữ liệu đã nhập không được lưu",,,
UI-TPN-014,Thêm Phiếu Nhập,Kiểm tra trường Người nhập bắt buộc,"1. Để trống trường Người nhập
2. Điền đầy đủ các trường khác
3. Nhấn nút Thêm","Hệ thống hiển thị thông báo lỗi cho trường Người nhập",,,
UI-TPN-015,Thêm Phiếu Nhập,Kiểm tra trường Nhà cung cấp bắt buộc,"1. Để trống trường Nhà cung cấp
2. Điền đầy đủ các trường khác
3. Nhấn nút Thêm","Hệ thống hiển thị thông báo lỗi cho trường Nhà cung cấp",,,
UI-TPN-016,Thêm Phiếu Nhập,Kiểm tra thêm thuốc vào phiếu nhập,"1. Nhấn nút thêm thuốc
2. Chọn một loại thuốc từ modal
3. Nhập số lượng và đơn giá","1. Thuốc được thêm vào danh sách
2. Thành tiền được tính đúng (Số lượng x Đơn giá)
3. Tổng tiền được cập nhật",,,
UI-TPN-017,Thêm Phiếu Nhập,Kiểm tra xóa thuốc khỏi phiếu nhập,"1. Thêm ít nhất một thuốc vào phiếu nhập
2. Nhấn nút xóa bên cạnh thuốc đó","1. Thuốc được xóa khỏi danh sách
2. Tổng tiền được cập nhật lại",,,
UI-TPN-018,Thêm Phiếu Nhập,Kiểm tra tính toán thành tiền,"1. Thêm thuốc vào phiếu nhập
2. Nhập số lượng và đơn giá
3. Kiểm tra giá trị thành tiền","Thành tiền được tính đúng (Số lượng x Đơn giá)",,,
UI-TPN-019,Thêm Phiếu Nhập,Kiểm tra tính toán tổng tiền,"1. Thêm nhiều thuốc vào phiếu nhập
2. Kiểm tra giá trị tổng tiền","Tổng tiền bằng tổng các thành tiền của từng thuốc",,,
UI-TPN-020,Thêm Phiếu Nhập,Kiểm tra nhập số lượng âm,"1. Thêm thuốc vào phiếu nhập
2. Nhập số lượng âm
3. Nhấn nút Thêm","Hệ thống hiển thị thông báo lỗi cho trường số lượng",,,
UI-CTPN-001,Chi Tiết Phiếu Nhập,Kiểm tra tiêu đề trang,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra tiêu đề trang","Tiêu đề ""CHI TIẾT PHIẾU NHẬP"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-002,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Mã phiếu,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Mã phiếu","Nhãn ""Mã phiếu"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-003,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Số lượng,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Số lượng","Nhãn ""Số lượng"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-004,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Đơn giá,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Đơn giá","Nhãn ""Đơn giá"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-005,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Thành tiền,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Thành tiền","Nhãn ""Thành tiền"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-006,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Ghi chú,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Ghi chú","Nhãn ""Ghi chú"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-007,Chi Tiết Phiếu Nhập,Kiểm tra trường nhập Mã phiếu,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường nhập Mã phiếu","Trường nhập Mã phiếu hiển thị đúng, có kích thước phù hợp và có thể nhập dữ liệu",,,
UI-CTPN-008,Chi Tiết Phiếu Nhập,Kiểm tra trường nhập Số lượng,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường nhập Số lượng","Trường nhập Số lượng hiển thị đúng, có kích thước phù hợp và chỉ chấp nhận giá trị số",,,
UI-CTPN-009,Chi Tiết Phiếu Nhập,Kiểm tra trường nhập Đơn giá,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường nhập Đơn giá","Trường nhập Đơn giá hiển thị đúng, có kích thước phù hợp và chỉ chấp nhận giá trị số",,,
UI-CTPN-010,Chi Tiết Phiếu Nhập,Kiểm tra trường hiển thị Thành tiền,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường hiển thị Thành tiền","Trường hiển thị Thành tiền có định dạng số đúng, có dấu phân cách hàng nghìn",,,
UI-CTPN-011,Chi Tiết Phiếu Nhập,Kiểm tra trường nhập Ghi chú,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường nhập Ghi chú","Trường nhập Ghi chú hiển thị đúng, có kích thước phù hợp và có thể nhập nhiều dòng",,,
UI-CTPN-012,Chi Tiết Phiếu Nhập,Kiểm tra giới hạn trường Số lượng,"1. Nhập giá trị âm vào trường Số lượng
2. Nhấn nút Lưu","Hệ thống hiển thị thông báo lỗi cho trường Số lượng",,,
UI-CTPN-013,Chi Tiết Phiếu Nhập,Kiểm tra giới hạn trường Đơn giá,"1. Nhập giá trị âm vào trường Đơn giá
2. Nhấn nút Lưu","Hệ thống hiển thị thông báo lỗi cho trường Đơn giá",,,
UI-CTPN-014,Chi Tiết Phiếu Nhập,Kiểm tra tính toán thành tiền,"1. Nhập số lượng 5 và đơn giá 10000
2. Kiểm tra giá trị thành tiền","Thành tiền hiển thị giá trị 50000 (5 x 10000)",,,
UI-CTPN-015,Chi Tiết Phiếu Nhập,Kiểm tra cập nhật thành tiền,"1. Nhập số lượng 5 và đơn giá 10000
2. Thay đổi số lượng thành 10
3. Kiểm tra giá trị thành tiền","Thành tiền được cập nhật thành 100000 (10 x 10000)",,,
UI-CTPN-016,Chi Tiết Phiếu Nhập,Kiểm tra nút chọn thuốc - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nút chọn thuốc","Nút chọn thuốc hiển thị rõ ràng và dễ nhận biết",,,
UI-CTPN-017,Chi Tiết Phiếu Nhập,Kiểm tra nút chọn thuốc - chức năng,"1. Nhấn nút chọn thuốc","Modal thuốc hiển thị với danh sách thuốc",,,
UI-CTPN-018,Chi Tiết Phiếu Nhập,Kiểm tra chọn thuốc từ modal,"1. Nhấn nút chọn thuốc
2. Chọn một loại thuốc từ modal
3. Nhấn nút Lưu trong modal","1. Modal đóng lại
2. Thông tin thuốc được điền vào form (tên thuốc, mã thuốc)",,,
UI-CTPN-019,Chi Tiết Phiếu Nhập,Kiểm tra nút Lưu - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nút Lưu","Nút Lưu hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-CTPN-020,Chi Tiết Phiếu Nhập,Kiểm tra nút Lưu - dữ liệu hợp lệ,"1. Điền đầy đủ thông tin hợp lệ vào form
2. Nhấn nút Lưu","1. Hệ thống kiểm tra dữ liệu hợp lệ
2. Chi tiết phiếu nhập được lưu thành công
3. Hiển thị thông báo thành công",,,
UI-CTPN-021,Chi Tiết Phiếu Nhập,Kiểm tra nút Lưu - dữ liệu không hợp lệ,"1. Điền thông tin không hợp lệ vào form (ví dụ: số lượng âm)
2. Nhấn nút Lưu","1. Hệ thống kiểm tra dữ liệu không hợp lệ
2. Hiển thị thông báo lỗi phù hợp
3. Không lưu chi tiết phiếu nhập",,,
UI-CTPN-022,Chi Tiết Phiếu Nhập,Kiểm tra nút Hủy - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nút Hủy","Nút Hủy hiển thị với nhãn đúng, kích thước phù hợp",,,
UI-CTPN-023,Chi Tiết Phiếu Nhập,Kiểm tra nút Hủy - chức năng,"1. Điền một số thông tin vào form
2. Nhấn nút Hủy","1. Hệ thống hiển thị hộp thoại xác nhận hủy
2. Sau khi xác nhận, hệ thống quay lại màn hình trước đó
3. Dữ liệu đã nhập không được lưu",,,
UI-CTPN-024,Chi Tiết Phiếu Nhập,Kiểm tra nút thêm dòng chi tiết - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nút thêm dòng chi tiết","Nút thêm dòng chi tiết hiển thị rõ ràng và dễ nhận biết",,,
UI-CTPN-025,Chi Tiết Phiếu Nhập,Kiểm tra nút thêm dòng chi tiết - chức năng,"1. Nhập thông tin cho một dòng chi tiết
2. Nhấn nút thêm dòng chi tiết mới","1. Dòng chi tiết hiện tại được lưu
2. Một dòng chi tiết mới trống được thêm vào form",,,
UI-CTPN-026,Chi Tiết Phiếu Nhập,Kiểm tra nút xóa dòng chi tiết - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập với ít nhất một dòng chi tiết
2. Kiểm tra nút xóa bên cạnh dòng chi tiết","Nút xóa hiển thị rõ ràng và dễ nhận biết",,,
UI-CTPN-027,Chi Tiết Phiếu Nhập,Kiểm tra nút xóa dòng chi tiết - chức năng,"1. Nhấn nút xóa bên cạnh một dòng chi tiết","1. Hệ thống hiển thị hộp thoại xác nhận xóa
2. Sau khi xác nhận, dòng chi tiết được xóa khỏi form
3. Tổng tiền được cập nhật lại",,,
UI-MT-001,Modal Thuốc,Kiểm tra tiêu đề modal,"1. Mở modal Thuốc
2. Kiểm tra tiêu đề modal","Tiêu đề ""Thuốc"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-002,Modal Thuốc,Kiểm tra nhãn cột STT,"1. Mở modal Thuốc
2. Kiểm tra nhãn cột STT","Nhãn ""STT"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-003,Modal Thuốc,Kiểm tra nhãn cột Tên,"1. Mở modal Thuốc
2. Kiểm tra nhãn cột Tên","Nhãn ""Tên"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-004,Modal Thuốc,Kiểm tra nhãn cột Mã thuốc,"1. Mở modal Thuốc
2. Kiểm tra nhãn cột Mã thuốc","Nhãn ""Mã thuốc"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-005,Modal Thuốc,Kiểm tra nhãn cột Số lượng tồn,"1. Mở modal Thuốc
2. Kiểm tra nhãn cột Số lượng tồn","Nhãn ""Số lượng tồn"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-006,Modal Thuốc,Kiểm tra căn chỉnh các cột,"1. Mở modal Thuốc
2. Kiểm tra căn chỉnh của các cột","Các cột được căn chỉnh hợp lý (STT căn giữa, Tên căn trái, Mã thuốc căn trái, Số lượng tồn căn phải)",,,
UI-MT-007,Modal Thuốc,Kiểm tra ô tìm kiếm,"1. Mở modal Thuốc
2. Kiểm tra ô tìm kiếm","Ô tìm kiếm hiển thị với placeholder phù hợp, kích thước đủ lớn để nhập dữ liệu",,,
UI-MT-008,Modal Thuốc,Kiểm tra nút Tìm kiếm,"1. Mở modal Thuốc
2. Kiểm tra nút Tìm kiếm","Nút Tìm kiếm hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-MT-009,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa hợp lệ,"1. Nhập từ khóa hợp lệ (ví dụ: ""Amoxicillin"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa ""Amoxicillin""",,,
UI-MT-010,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa không hợp lệ,"1. Nhập từ khóa không hợp lệ (ví dụ: ""xyz123"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị thông báo không tìm thấy kết quả hoặc danh sách trống",,,
UI-MT-011,Modal Thuốc,Kiểm tra chọn thuốc - checkbox,"1. Mở modal Thuốc
2. Nhấn vào checkbox bên cạnh một loại thuốc","Checkbox được chọn và hàng tương ứng được đánh dấu",,,
UI-MT-012,Modal Thuốc,Kiểm tra chọn nhiều thuốc,"1. Mở modal Thuốc
2. Nhấn vào checkbox bên cạnh nhiều loại thuốc","Nhiều checkbox được chọn và các hàng tương ứng được đánh dấu",,,
UI-MT-013,Modal Thuốc,Kiểm tra nút Lưu - hiển thị,"1. Mở modal Thuốc
2. Kiểm tra nút Lưu","Nút Lưu hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-MT-014,Modal Thuốc,Kiểm tra nút Lưu - chức năng,"1. Chọn một loại thuốc từ danh sách
2. Nhấn nút Lưu","1. Modal đóng lại
2. Thông tin thuốc được chọn được truyền về form chính",,,
UI-MT-015,Modal Thuốc,Kiểm tra nút Hủy - hiển thị,"1. Mở modal Thuốc
2. Kiểm tra nút Hủy","Nút Hủy hiển thị với nhãn đúng, kích thước phù hợp",,,
UI-MT-016,Modal Thuốc,Kiểm tra nút Hủy - chức năng,"1. Chọn một loại thuốc từ danh sách
2. Nhấn nút Hủy","1. Modal đóng lại
2. Không có thay đổi nào được áp dụng vào form chính",,,
UI-MT-017,Modal Thuốc,Kiểm tra nút Thêm thuốc - hiển thị,"1. Mở modal Thuốc
2. Kiểm tra nút Thêm thuốc","Nút Thêm thuốc hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-MT-018,Modal Thuốc,Kiểm tra nút Thêm thuốc - chức năng,"1. Nhấn nút Thêm thuốc","Hệ thống chuyển đến form thêm thuốc mới",,,
UI-MT-019,Modal Thuốc,Kiểm tra nút phân trang Trước,"1. Nếu có nhiều hơn 10 mục, chuyển đến trang 2
2. Nhấn nút Trước","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-MT-020,Modal Thuốc,Kiểm tra nút phân trang Sau,"1. Nếu đang ở trang 1 và có nhiều hơn 10 mục
2. Nhấn nút Sau","Hệ thống chuyển đến trang 2 và hiển thị dữ liệu chính xác",,,
UI-MT-021,Modal Thuốc,Kiểm tra nút phân trang Đầu tiên,"1. Nếu đang ở trang khác trang 1
2. Nhấn nút Đầu tiên (<<)","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-MT-022,Modal Thuốc,Kiểm tra nút phân trang Cuối cùng,"1. Nếu đang ở trang không phải trang cuối
2. Nhấn nút Cuối cùng (>>)","Hệ thống chuyển đến trang cuối và hiển thị dữ liệu chính xác",,,
UI-MT-023,Modal Thuốc,Kiểm tra nhấp vào số trang,"1. Nếu có nhiều trang
2. Nhấn vào số trang cụ thể (ví dụ: 3)","Hệ thống chuyển đến trang đã chọn và hiển thị dữ liệu chính xác",,,
UI-MT-024,Modal Thuốc,Kiểm tra hiển thị tên thuốc,"1. Mở modal Thuốc
2. Kiểm tra hiển thị tên thuốc trong bảng","Tên thuốc hiển thị đúng, không bị cắt ngắn hoặc tràn ra ngoài cột",,,
UI-MT-025,Modal Thuốc,Kiểm tra hiển thị mã thuốc,"1. Mở modal Thuốc
2. Kiểm tra hiển thị mã thuốc trong bảng","Mã thuốc hiển thị đúng định dạng, không bị cắt ngắn",,,
UI-MT-026,Modal Thuốc,Kiểm tra hiển thị số lượng tồn,"1. Mở modal Thuốc
2. Kiểm tra hiển thị số lượng tồn trong bảng","Số lượng tồn hiển thị đúng định dạng số, căn phải và không bị cắt ngắn",,,
UI-ALL-001,Tất cả màn hình,Kiểm tra font chữ tiêu đề,"1. Kiểm tra font chữ của tiêu đề trên tất cả các màn hình","Font chữ tiêu đề nhất quán trên tất cả các màn hình, sử dụng font và kích thước đúng",,,
UI-ALL-002,Tất cả màn hình,Kiểm tra font chữ nhãn,"1. Kiểm tra font chữ của các nhãn trên tất cả các màn hình","Font chữ nhãn nhất quán trên tất cả các màn hình, sử dụng font và kích thước đúng",,,
UI-ALL-003,Tất cả màn hình,Kiểm tra font chữ nút,"1. Kiểm tra font chữ của các nút trên tất cả các màn hình","Font chữ nút nhất quán trên tất cả các màn hình, sử dụng font và kích thước đúng",,,
UI-ALL-004,Tất cả màn hình,Kiểm tra font chữ bảng,"1. Kiểm tra font chữ trong bảng trên tất cả các màn hình","Font chữ trong bảng nhất quán trên tất cả các màn hình, sử dụng font và kích thước đúng",,,
UI-ALL-005,Tất cả màn hình,Kiểm tra màu sắc tiêu đề,"1. Kiểm tra màu sắc của tiêu đề trên tất cả các màn hình","Màu sắc tiêu đề nhất quán và tuân theo bảng màu của ứng dụng",,,
UI-ALL-006,Tất cả màn hình,Kiểm tra màu sắc nút chính,"1. Kiểm tra màu sắc của nút chính (Thêm, Lưu) trên tất cả các màn hình","Màu sắc nút chính nhất quán và tuân theo bảng màu của ứng dụng",,,
UI-ALL-007,Tất cả màn hình,Kiểm tra màu sắc nút phụ,"1. Kiểm tra màu sắc của nút phụ (Hủy, Đóng) trên tất cả các màn hình","Màu sắc nút phụ nhất quán và tuân theo bảng màu của ứng dụng",,,
UI-ALL-008,Tất cả màn hình,Kiểm tra màu sắc bảng,"1. Kiểm tra màu sắc của bảng trên tất cả các màn hình","Màu sắc bảng (header, dòng chẵn/lẻ) nhất quán và tuân theo bảng màu của ứng dụng",,,
UI-ALL-009,Tất cả màn hình,Kiểm tra thông báo lỗi trường bắt buộc,"1. Để trống các trường bắt buộc
2. Nhấn nút lưu/thêm
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, đúng chính tả và hữu ích",,,
UI-ALL-010,Tất cả màn hình,Kiểm tra thông báo lỗi định dạng,"1. Nhập dữ liệu sai định dạng (ví dụ: chữ vào trường số)
2. Nhấn nút lưu/thêm
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, đúng chính tả và hữu ích",,,
UI-ALL-011,Tất cả màn hình,Kiểm tra thông báo lỗi giá trị không hợp lệ,"1. Nhập giá trị không hợp lệ (ví dụ: số âm cho số lượng)
2. Nhấn nút lưu/thêm
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, đúng chính tả và hữu ích",,,
UI-ALL-012,Tất cả màn hình,Kiểm tra thông báo thành công khi thêm,"1. Thực hiện thao tác thêm thành công
2. Kiểm tra thông báo thành công","Thông báo thành công hiển thị rõ ràng, đúng chính tả và biến mất sau thời gian phù hợp",,,
UI-ALL-013,Tất cả màn hình,Kiểm tra thông báo thành công khi cập nhật,"1. Thực hiện thao tác cập nhật thành công
2. Kiểm tra thông báo thành công","Thông báo thành công hiển thị rõ ràng, đúng chính tả và biến mất sau thời gian phù hợp",,,
UI-ALL-014,Tất cả màn hình,Kiểm tra thông báo thành công khi xóa,"1. Thực hiện thao tác xóa thành công
2. Kiểm tra thông báo thành công","Thông báo thành công hiển thị rõ ràng, đúng chính tả và biến mất sau thời gian phù hợp",,,
UI-ALL-015,Tất cả màn hình,Kiểm tra tính nhất quán của nút,"1. So sánh các nút giữa các màn hình","Các nút có thiết kế nhất quán (kích thước, màu sắc, góc bo tròn) giữa các màn hình",,,
UI-ALL-016,Tất cả màn hình,Kiểm tra tính nhất quán của form,"1. So sánh các form giữa các màn hình","Các form có thiết kế nhất quán (khoảng cách, căn lề, bố cục) giữa các màn hình",,,
UI-ALL-017,Tất cả màn hình,Kiểm tra tính nhất quán của bảng,"1. So sánh các bảng giữa các màn hình","Các bảng có thiết kế nhất quán (header, dòng, phân trang) giữa các màn hình",,,
UI-ALL-018,Tất cả màn hình,Kiểm tra tốc độ tải trang Tồn Kho,"1. Đo thời gian tải của màn hình Tồn Kho","Thời gian tải trang nhanh (<3 giây) và không có độ trễ đáng kể",,,
UI-ALL-019,Tất cả màn hình,Kiểm tra tốc độ tải trang Phiếu Nhập,"1. Đo thời gian tải của màn hình Phiếu Nhập","Thời gian tải trang nhanh (<3 giây) và không có độ trễ đáng kể",,,
UI-ALL-020,Tất cả màn hình,Kiểm tra tốc độ tải modal Thuốc,"1. Đo thời gian tải của modal Thuốc","Thời gian tải modal nhanh (<2 giây) và không có độ trễ đáng kể",,,
UI-ALL-021,Tất cả màn hình,Kiểm tra khả năng truy cập bằng bàn phím,"1. Sử dụng phím Tab để di chuyển qua các phần tử
2. Sử dụng phím Enter để kích hoạt nút","Có thể điều hướng và tương tác với tất cả các phần tử bằng bàn phím",,,
UI-ALL-022,Tất cả màn hình,Kiểm tra thuộc tính alt cho hình ảnh,"1. Kiểm tra thuộc tính alt cho các hình ảnh và biểu tượng","Tất cả hình ảnh và biểu tượng có thuộc tính alt mô tả phù hợp",,,
UI-ALL-023,Tất cả màn hình,Kiểm tra tương thích Chrome,"1. Mở ứng dụng trên trình duyệt Chrome
2. Kiểm tra hiển thị và chức năng","Giao diện hiển thị đúng và tất cả chức năng hoạt động bình thường trên Chrome",,,
UI-ALL-024,Tất cả màn hình,Kiểm tra tương thích Firefox,"1. Mở ứng dụng trên trình duyệt Firefox
2. Kiểm tra hiển thị và chức năng","Giao diện hiển thị đúng và tất cả chức năng hoạt động bình thường trên Firefox",,,
UI-ALL-025,Tất cả màn hình,Kiểm tra tương thích Edge,"1. Mở ứng dụng trên trình duyệt Edge
2. Kiểm tra hiển thị và chức năng","Giao diện hiển thị đúng và tất cả chức năng hoạt động bình thường trên Edge",,,
UI-ALL-026,Tất cả màn hình,Kiểm tra hiệu ứng hover nút,"1. Di chuột qua các nút
2. Kiểm tra hiệu ứng hover","Hiệu ứng hover hiển thị đúng và nhất quán trên tất cả các nút",,,
UI-ALL-027,Tất cả màn hình,Kiểm tra hiệu ứng focus trường nhập liệu,"1. Nhấp vào các trường nhập liệu
2. Kiểm tra hiệu ứng focus","Hiệu ứng focus hiển thị đúng và nhất quán trên tất cả các trường nhập liệu",,,
UI-ALL-028,Tất cả màn hình,Kiểm tra hiệu ứng active nút,"1. Nhấn và giữ chuột trên các nút
2. Kiểm tra hiệu ứng active","Hiệu ứng active hiển thị đúng và nhất quán trên tất cả các nút",,,
UI-ALL-029,Tất cả màn hình,Kiểm tra hiển thị tiếng Việt có dấu,"1. Kiểm tra hiển thị các ký tự tiếng Việt có dấu trên tất cả các màn hình","Các ký tự tiếng Việt có dấu hiển thị đúng, không bị lỗi font",,,
UI-ALL-030,Tất cả màn hình,Kiểm tra nhập liệu tiếng Việt,"1. Nhập dữ liệu tiếng Việt có dấu vào các trường
2. Lưu và kiểm tra hiển thị","Dữ liệu tiếng Việt có dấu được nhập, lưu và hiển thị đúng",,,
UI-ALL-031,Tất cả màn hình,Kiểm tra độ tương phản màu sắc,"1. Kiểm tra độ tương phản giữa văn bản và nền
2. Sử dụng công cụ kiểm tra độ tương phản","Độ tương phản đạt tiêu chuẩn WCAG 2.0 AA (tỷ lệ tối thiểu 4.5:1 cho văn bản thường)",,,
UI-ALL-032,Tất cả màn hình,Kiểm tra khả năng phóng to,"1. Sử dụng chức năng phóng to của trình duyệt (Ctrl + +)
2. Phóng to đến 200%","Giao diện vẫn hiển thị đúng và có thể sử dụng được khi phóng to đến 200%",,,
UI-ALL-033,Tất cả màn hình,Kiểm tra chế độ tối (Dark mode),"1. Bật chế độ tối trên trình duyệt
2. Kiểm tra hiển thị của ứng dụng","Ứng dụng hiển thị phù hợp trong chế độ tối hoặc có chế độ tối riêng",,,
UI-ALL-034,Tất cả màn hình,Kiểm tra hiển thị khi mất kết nối,"1. Ngắt kết nối internet
2. Thực hiện các thao tác trên ứng dụng","Ứng dụng hiển thị thông báo lỗi phù hợp khi mất kết nối",,,
UI-ALL-035,Tất cả màn hình,Kiểm tra khả năng phục hồi sau lỗi,"1. Gây ra lỗi (ví dụ: nhập dữ liệu không hợp lệ)
2. Kiểm tra khả năng tiếp tục sử dụng sau lỗi","Ứng dụng có thể tiếp tục hoạt động bình thường sau khi xảy ra lỗi",,,
UI-ALL-036,Tất cả màn hình,Kiểm tra hiển thị trên màn hình độ phân giải thấp,"1. Mở ứng dụng trên màn hình có độ phân giải thấp (800x600)
2. Kiểm tra hiển thị","Giao diện hiển thị phù hợp và có thể sử dụng được trên màn hình độ phân giải thấp",,,
UI-ALL-037,Tất cả màn hình,Kiểm tra hiển thị trên màn hình độ phân giải cao,"1. Mở ứng dụng trên màn hình có độ phân giải cao (4K)
2. Kiểm tra hiển thị","Giao diện hiển thị phù hợp và không bị quá nhỏ trên màn hình độ phân giải cao",,,
UI-ALL-038,Tất cả màn hình,Kiểm tra hiển thị khi thay đổi hướng màn hình,"1. Mở ứng dụng trên thiết bị có thể xoay (tablet)
2. Xoay màn hình từ ngang sang dọc và ngược lại","Giao diện tự động điều chỉnh và hiển thị phù hợp khi thay đổi hướng màn hình",,,
UI-ALL-039,Tất cả màn hình,Kiểm tra hiển thị với font chữ lớn,"1. Tăng kích thước font chữ trong cài đặt trình duyệt
2. Kiểm tra hiển thị","Giao diện vẫn hiển thị đúng và có thể sử dụng được với font chữ lớn",,,
UI-ALL-040,Tất cả màn hình,Kiểm tra hiển thị với độ rộng cửa sổ thay đổi,"1. Thay đổi kích thước cửa sổ trình duyệt
2. Kiểm tra hiển thị","Giao diện tự động điều chỉnh và hiển thị phù hợp khi thay đổi kích thước cửa sổ",,,
UI-TK-026,Tồn Kho,Kiểm tra sắp xếp theo ID,"1. Mở màn hình Tồn Kho
2. Nhấp vào tiêu đề cột ID","Dữ liệu được sắp xếp theo ID tăng dần hoặc giảm dần",,,
UI-TK-027,Tồn Kho,Kiểm tra sắp xếp theo Mã số,"1. Mở màn hình Tồn Kho
2. Nhấp vào tiêu đề cột Mã số","Dữ liệu được sắp xếp theo Mã số tăng dần hoặc giảm dần",,,
UI-TK-028,Tồn Kho,Kiểm tra sắp xếp theo Tên thuốc,"1. Mở màn hình Tồn Kho
2. Nhấp vào tiêu đề cột Tên thuốc","Dữ liệu được sắp xếp theo Tên thuốc tăng dần hoặc giảm dần",,,
UI-TK-029,Tồn Kho,Kiểm tra sắp xếp theo Số lượng tồn,"1. Mở màn hình Tồn Kho
2. Nhấp vào tiêu đề cột Số lượng tồn","Dữ liệu được sắp xếp theo Số lượng tồn tăng dần hoặc giảm dần",,,
UI-TK-030,Tồn Kho,Kiểm tra tìm kiếm với từ khóa tiếng Việt có dấu,"1. Nhập từ khóa tiếng Việt có dấu (ví dụ: ""Thuốc đau đầu"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa tiếng Việt có dấu",,,
UI-TK-031,Tồn Kho,Kiểm tra tìm kiếm với từ khóa tiếng Việt không dấu,"1. Nhập từ khóa tiếng Việt không dấu (ví dụ: ""Thuoc dau dau"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả phù hợp, có thể tìm được các từ có dấu tương ứng",,,
UI-TK-032,Tồn Kho,Kiểm tra tìm kiếm với từ khóa viết hoa,"1. Nhập từ khóa viết hoa (ví dụ: ""AMOXICILLIN"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả không phân biệt chữ hoa/thường",,,
UI-TK-033,Tồn Kho,Kiểm tra tìm kiếm với từ khóa một phần,"1. Nhập một phần của từ khóa (ví dụ: ""amox"" thay vì ""amoxicillin"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa phần từ khóa đã nhập",,,
UI-TK-034,Tồn Kho,Kiểm tra tìm kiếm với mã thuốc,"1. Nhập mã thuốc (ví dụ: ""T001"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả có mã thuốc tương ứng",,,
UI-TK-035,Tồn Kho,Kiểm tra hiển thị khi không có dữ liệu,"1. Xóa tất cả dữ liệu hoặc tìm kiếm với từ khóa không có kết quả
2. Kiểm tra hiển thị","Hệ thống hiển thị thông báo ""Không có dữ liệu"" hoặc bảng trống với thông báo phù hợp",,,
UI-PN-027,Phiếu Nhập,Kiểm tra sắp xếp theo ID,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột ID","Dữ liệu được sắp xếp theo ID tăng dần hoặc giảm dần",,,
UI-PN-028,Phiếu Nhập,Kiểm tra sắp xếp theo Mã công ty,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột Mã công ty","Dữ liệu được sắp xếp theo Mã công ty tăng dần hoặc giảm dần",,,
UI-PN-029,Phiếu Nhập,Kiểm tra sắp xếp theo Người nhập,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột Người nhập","Dữ liệu được sắp xếp theo Người nhập tăng dần hoặc giảm dần",,,
UI-PN-030,Phiếu Nhập,Kiểm tra sắp xếp theo Ngày nhập,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột Ngày nhập","Dữ liệu được sắp xếp theo Ngày nhập tăng dần hoặc giảm dần",,,
UI-PN-031,Phiếu Nhập,Kiểm tra sắp xếp theo Tổng tiền,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột Tổng tiền","Dữ liệu được sắp xếp theo Tổng tiền tăng dần hoặc giảm dần",,,
UI-PN-032,Phiếu Nhập,Kiểm tra tìm kiếm theo ngày,"1. Nhập ngày (ví dụ: ""20-05-2023"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả có ngày nhập tương ứng",,,
UI-PN-033,Phiếu Nhập,Kiểm tra tìm kiếm theo khoảng tiền,"1. Nhập khoảng tiền (ví dụ: ""1000000-2000000"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả có tổng tiền trong khoảng đã nhập",,,
UI-PN-034,Phiếu Nhập,Kiểm tra hiển thị chi tiết phiếu nhập,"1. Nhấp vào một dòng phiếu nhập trong bảng","Hệ thống chuyển đến màn hình Chi tiết phiếu nhập tương ứng",,,
UI-PN-035,Phiếu Nhập,Kiểm tra hiển thị khi không có dữ liệu,"1. Xóa tất cả dữ liệu hoặc tìm kiếm với từ khóa không có kết quả
2. Kiểm tra hiển thị","Hệ thống hiển thị thông báo ""Không có dữ liệu"" hoặc bảng trống với thông báo phù hợp",,,
UI-TPN-021,Thêm Phiếu Nhập,Kiểm tra nhập số lượng lớn,"1. Thêm thuốc vào phiếu nhập
2. Nhập số lượng rất lớn (ví dụ: 999999999)
3. Nhấn nút Thêm","Hệ thống xử lý đúng hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-TPN-022,Thêm Phiếu Nhập,Kiểm tra nhập đơn giá lớn,"1. Thêm thuốc vào phiếu nhập
2. Nhập đơn giá rất lớn (ví dụ: 999999999)
3. Nhấn nút Thêm","Hệ thống xử lý đúng hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-TPN-023,Thêm Phiếu Nhập,Kiểm tra nhập số lượng thập phân,"1. Thêm thuốc vào phiếu nhập
2. Nhập số lượng thập phân (ví dụ: 1.5)
3. Nhấn nút Thêm","Hệ thống xử lý đúng hoặc hiển thị thông báo lỗi phù hợp nếu không chấp nhận số thập phân",,,
UI-TPN-024,Thêm Phiếu Nhập,Kiểm tra thêm nhiều thuốc cùng lúc,"1. Thêm nhiều thuốc khác nhau vào phiếu nhập
2. Nhấn nút Thêm","Tất cả thuốc được thêm vào phiếu nhập và tổng tiền được tính đúng",,,
UI-TPN-025,Thêm Phiếu Nhập,Kiểm tra thêm thuốc trùng lặp,"1. Thêm một thuốc vào phiếu nhập
2. Thêm lại cùng thuốc đó
3. Nhấn nút Thêm","Hệ thống xử lý đúng (cộng dồn số lượng hoặc hiển thị thông báo trùng lặp)",,,
UI-CTPN-028,Chi Tiết Phiếu Nhập,Kiểm tra nhập số lượng lớn,"1. Nhập số lượng rất lớn (ví dụ: 999999999) vào trường Số lượng
2. Nhấn nút Lưu","Hệ thống xử lý đúng hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-CTPN-029,Chi Tiết Phiếu Nhập,Kiểm tra nhập đơn giá lớn,"1. Nhập đơn giá rất lớn (ví dụ: 999999999) vào trường Đơn giá
2. Nhấn nút Lưu","Hệ thống xử lý đúng hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-CTPN-030,Chi Tiết Phiếu Nhập,Kiểm tra nhập số lượng thập phân,"1. Nhập số lượng thập phân (ví dụ: 1.5) vào trường Số lượng
2. Nhấn nút Lưu","Hệ thống xử lý đúng hoặc hiển thị thông báo lỗi phù hợp nếu không chấp nhận số thập phân",,,
UI-CTPN-031,Chi Tiết Phiếu Nhập,Kiểm tra nhập đơn giá thập phân,"1. Nhập đơn giá thập phân (ví dụ: 10000.5) vào trường Đơn giá
2. Nhấn nút Lưu","Hệ thống xử lý đúng và hiển thị thành tiền với định dạng số phù hợp",,,
UI-CTPN-032,Chi Tiết Phiếu Nhập,Kiểm tra thêm nhiều dòng chi tiết,"1. Thêm nhiều dòng chi tiết khác nhau
2. Nhấn nút Lưu","Tất cả dòng chi tiết được lưu và tổng tiền được tính đúng",,,
UI-CTPN-033,Chi Tiết Phiếu Nhập,Kiểm tra thêm dòng chi tiết trùng lặp,"1. Thêm một dòng chi tiết
2. Thêm lại dòng chi tiết với cùng thuốc
3. Nhấn nút Lưu","Hệ thống xử lý đúng (cộng dồn số lượng hoặc hiển thị thông báo trùng lặp)",,,
UI-CTPN-034,Chi Tiết Phiếu Nhập,Kiểm tra nhập ghi chú dài,"1. Nhập ghi chú rất dài vào trường Ghi chú
2. Nhấn nút Lưu","Hệ thống xử lý đúng, có thể cắt bớt hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-CTPN-035,Chi Tiết Phiếu Nhập,Kiểm tra nhập ký tự đặc biệt vào ghi chú,"1. Nhập ký tự đặc biệt (ví dụ: @#$%&) vào trường Ghi chú
2. Nhấn nút Lưu","Hệ thống xử lý đúng, lưu và hiển thị ký tự đặc biệt trong ghi chú",,,
UI-MT-027,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa tiếng Việt có dấu,"1. Nhập từ khóa tiếng Việt có dấu (ví dụ: ""Thuốc đau đầu"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa tiếng Việt có dấu",,,
UI-MT-028,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa tiếng Việt không dấu,"1. Nhập từ khóa tiếng Việt không dấu (ví dụ: ""Thuoc dau dau"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả phù hợp, có thể tìm được các từ có dấu tương ứng",,,
UI-MT-029,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa viết hoa,"1. Nhập từ khóa viết hoa (ví dụ: ""AMOXICILLIN"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả không phân biệt chữ hoa/thường",,,
UI-MT-030,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa một phần,"1. Nhập một phần của từ khóa (ví dụ: ""amox"" thay vì ""amoxicillin"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa phần từ khóa đã nhập",,,
UI-MT-031,Modal Thuốc,Kiểm tra tìm kiếm với mã thuốc,"1. Nhập mã thuốc (ví dụ: ""T001"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả có mã thuốc tương ứng",,,
UI-MT-032,Modal Thuốc,Kiểm tra sắp xếp theo STT,"1. Mở modal Thuốc
2. Nhấp vào tiêu đề cột STT","Dữ liệu được sắp xếp theo STT tăng dần hoặc giảm dần",,,
UI-MT-033,Modal Thuốc,Kiểm tra sắp xếp theo Tên,"1. Mở modal Thuốc
2. Nhấp vào tiêu đề cột Tên","Dữ liệu được sắp xếp theo Tên tăng dần hoặc giảm dần",,,
UI-MT-034,Modal Thuốc,Kiểm tra sắp xếp theo Mã thuốc,"1. Mở modal Thuốc
2. Nhấp vào tiêu đề cột Mã thuốc","Dữ liệu được sắp xếp theo Mã thuốc tăng dần hoặc giảm dần",,,
UI-MT-035,Modal Thuốc,Kiểm tra sắp xếp theo Số lượng tồn,"1. Mở modal Thuốc
2. Nhấp vào tiêu đề cột Số lượng tồn","Dữ liệu được sắp xếp theo Số lượng tồn tăng dần hoặc giảm dần",,,
UI-MT-036,Modal Thuốc,Kiểm tra hiển thị khi không có dữ liệu,"1. Tìm kiếm với từ khóa không có kết quả
2. Kiểm tra hiển thị","Hệ thống hiển thị thông báo ""Không có dữ liệu"" hoặc bảng trống với thông báo phù hợp",,,
UI-MT-037,Modal Thuốc,Kiểm tra chọn nhiều trang,"1. Chọn một thuốc ở trang 1
2. Chuyển sang trang 2
3. Chọn một thuốc ở trang 2
4. Nhấn nút Lưu","Hệ thống xử lý đúng, lưu tất cả các thuốc đã chọn từ các trang khác nhau",,,
UI-MT-038,Modal Thuốc,Kiểm tra bỏ chọn thuốc,"1. Chọn một thuốc
2. Nhấp lại vào checkbox để bỏ chọn
3. Nhấn nút Lưu","Thuốc đã bỏ chọn không được lưu",,,
UI-MT-039,Modal Thuốc,Kiểm tra chọn tất cả,"1. Nếu có nút Chọn tất cả, nhấp vào nút đó
2. Kiểm tra trạng thái checkbox","Tất cả checkbox được chọn",,,
UI-MT-040,Modal Thuốc,Kiểm tra bỏ chọn tất cả,"1. Chọn tất cả thuốc
2. Nếu có nút Bỏ chọn tất cả, nhấp vào nút đó
3. Kiểm tra trạng thái checkbox","Tất cả checkbox được bỏ chọn",,,
UI-PERF-001,Hiệu năng,Kiểm tra thời gian tải trang Tồn Kho,"1. Đo thời gian tải của màn hình Tồn Kho với 10 mục
2. Đo thời gian tải với 100 mục
3. Đo thời gian tải với 1000 mục","Thời gian tải trang không tăng đáng kể khi số lượng mục tăng lên",,,
UI-PERF-002,Hiệu năng,Kiểm tra thời gian tải trang Phiếu Nhập,"1. Đo thời gian tải của màn hình Phiếu Nhập với 10 mục
2. Đo thời gian tải với 100 mục
3. Đo thời gian tải với 1000 mục","Thời gian tải trang không tăng đáng kể khi số lượng mục tăng lên",,,
UI-PERF-003,Hiệu năng,Kiểm tra thời gian tải modal Thuốc,"1. Đo thời gian tải của modal Thuốc với 10 mục
2. Đo thời gian tải với 100 mục
3. Đo thời gian tải với 1000 mục","Thời gian tải modal không tăng đáng kể khi số lượng mục tăng lên",,,
UI-PERF-004,Hiệu năng,Kiểm tra thời gian phản hồi tìm kiếm,"1. Đo thời gian phản hồi khi tìm kiếm với từ khóa đơn giản
2. Đo thời gian phản hồi khi tìm kiếm với từ khóa phức tạp","Thời gian phản hồi tìm kiếm nhanh (<2 giây)",,,
UI-PERF-005,Hiệu năng,Kiểm tra thời gian phản hồi sắp xếp,"1. Đo thời gian phản hồi khi sắp xếp bảng với 10 mục
2. Đo thời gian phản hồi khi sắp xếp bảng với 100 mục
3. Đo thời gian phản hồi khi sắp xếp bảng với 1000 mục","Thời gian phản hồi sắp xếp nhanh (<2 giây)",,,
UI-PERF-006,Hiệu năng,Kiểm tra thời gian phản hồi khi thêm phiếu nhập,"1. Đo thời gian phản hồi khi thêm phiếu nhập với 1 thuốc
2. Đo thời gian phản hồi khi thêm phiếu nhập với 10 thuốc
3. Đo thời gian phản hồi khi thêm phiếu nhập với 100 thuốc","Thời gian phản hồi khi thêm phiếu nhập nhanh (<3 giây)",,,
UI-PERF-007,Hiệu năng,Kiểm tra thời gian phản hồi khi xóa phiếu nhập,"1. Đo thời gian phản hồi khi xóa phiếu nhập","Thời gian phản hồi khi xóa phiếu nhập nhanh (<2 giây)",,,
UI-PERF-008,Hiệu năng,Kiểm tra sử dụng CPU,"1. Mở Task Manager hoặc công cụ giám sát hệ thống
2. Sử dụng các chức năng chính của ứng dụng
3. Theo dõi mức sử dụng CPU","Mức sử dụng CPU không vượt quá 50% khi sử dụng ứng dụng",,,
UI-PERF-009,Hiệu năng,Kiểm tra sử dụng bộ nhớ,"1. Mở Task Manager hoặc công cụ giám sát hệ thống
2. Sử dụng các chức năng chính của ứng dụng
3. Theo dõi mức sử dụng bộ nhớ","Mức sử dụng bộ nhớ không tăng đáng kể theo thời gian (không có memory leak)",,,
UI-PERF-010,Hiệu năng,Kiểm tra hiệu năng sau thời gian dài sử dụng,"1. Sử dụng ứng dụng liên tục trong 1 giờ
2. Thực hiện các thao tác thường xuyên
3. Kiểm tra tốc độ phản hồi","Tốc độ phản hồi không giảm đáng kể sau thời gian dài sử dụng",,,
UI-SEC-001,Bảo mật,Kiểm tra xác thực đầu vào,"1. Nhập các ký tự đặc biệt vào các trường nhập liệu
2. Nhập mã JavaScript vào các trường nhập liệu
3. Nhấn nút lưu/thêm","Hệ thống xử lý đúng, không bị lỗi và không thực thi mã độc",,,
UI-SEC-002,Bảo mật,Kiểm tra XSS (Cross-Site Scripting),"1. Nhập mã HTML/JavaScript vào các trường nhập liệu (ví dụ: <script>alert('XSS')</script>)
2. Lưu và kiểm tra hiển thị","Hệ thống hiển thị mã dưới dạng văn bản thông thường, không thực thi mã",,,
UI-SEC-003,Bảo mật,Kiểm tra SQL Injection,"1. Nhập chuỗi SQL Injection vào các trường nhập liệu (ví dụ: ' OR '1'='1)
2. Nhấn nút lưu/thêm","Hệ thống xử lý đúng, không bị lỗi và không thực hiện truy vấn SQL độc hại",,,
UI-SEC-004,Bảo mật,Kiểm tra CSRF (Cross-Site Request Forgery),"1. Đăng nhập vào ứng dụng
2. Mở một trang web khác trong cùng trình duyệt
3. Quay lại ứng dụng và thực hiện các thao tác","Hệ thống yêu cầu xác thực lại nếu phiên đã hết hạn",,,
UI-SEC-005,Bảo mật,Kiểm tra bảo vệ dữ liệu nhạy cảm,"1. Kiểm tra các trường nhập liệu nhạy cảm (nếu có)
2. Kiểm tra cách hiển thị dữ liệu nhạy cảm","Dữ liệu nhạy cảm được che giấu hoặc hiển thị an toàn",,,
UI-ACC-001,Khả năng truy cập,Kiểm tra điều hướng bằng bàn phím,"1. Sử dụng phím Tab để di chuyển qua các phần tử
2. Sử dụng phím Enter để kích hoạt nút
3. Sử dụng phím Space để chọn checkbox","Có thể điều hướng và tương tác với tất cả các phần tử bằng bàn phím",,,
UI-ACC-002,Khả năng truy cập,Kiểm tra thuộc tính ARIA,"1. Kiểm tra các thuộc tính ARIA (role, aria-label, aria-required, v.v.)
2. Sử dụng công cụ kiểm tra khả năng truy cập","Các phần tử UI có thuộc tính ARIA phù hợp",,,
UI-ACC-003,Khả năng truy cập,Kiểm tra độ tương phản màu sắc,"1. Kiểm tra độ tương phản giữa văn bản và nền
2. Sử dụng công cụ kiểm tra độ tương phản","Độ tương phản đạt tiêu chuẩn WCAG 2.0 AA (tỷ lệ tối thiểu 4.5:1 cho văn bản thường)",,,
UI-ACC-004,Khả năng truy cập,Kiểm tra thứ tự Tab,"1. Nhấn Tab liên tục từ đầu trang
2. Kiểm tra thứ tự di chuyển focus","Thứ tự Tab logic và tuân theo cấu trúc trang",,,
UI-ACC-005,Khả năng truy cập,Kiểm tra thông báo lỗi cho người dùng màn đọc,"1. Gây ra lỗi (ví dụ: để trống trường bắt buộc)
2. Kiểm tra thông báo lỗi với công cụ màn đọc","Thông báo lỗi được đọc bởi công cụ màn đọc",,,
UI-LOC-001,Bản địa hóa,Kiểm tra hiển thị tiếng Việt,"1. Kiểm tra hiển thị các ký tự tiếng Việt trên tất cả các màn hình","Các ký tự tiếng Việt hiển thị đúng, không bị lỗi font",,,
UI-LOC-002,Bản địa hóa,Kiểm tra định dạng ngày tháng,"1. Kiểm tra định dạng ngày tháng trên tất cả các màn hình","Định dạng ngày tháng nhất quán và phù hợp với quy ước Việt Nam (DD-MM-YYYY)",,,
UI-LOC-003,Bản địa hóa,Kiểm tra định dạng số,"1. Kiểm tra định dạng số trên tất cả các màn hình","Định dạng số nhất quán và phù hợp với quy ước Việt Nam (dấu phẩy làm dấu phân cách hàng nghìn)",,,
UI-LOC-004,Bản địa hóa,Kiểm tra định dạng tiền tệ,"1. Kiểm tra định dạng tiền tệ trên tất cả các màn hình","Định dạng tiền tệ nhất quán và phù hợp với quy ước Việt Nam (VND)",,,
UI-LOC-005,Bản địa hóa,Kiểm tra sắp xếp theo bảng chữ cái tiếng Việt,"1. Sắp xếp dữ liệu theo tên tiếng Việt
2. Kiểm tra thứ tự sắp xếp","Thứ tự sắp xếp tuân theo bảng chữ cái tiếng Việt",,,
UI-ERR-001,Xử lý lỗi,Kiểm tra thông báo lỗi khi để trống trường bắt buộc,"1. Để trống các trường bắt buộc
2. Nhấn nút lưu/thêm
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, đúng vị trí và chỉ ra trường nào bị thiếu",,,
UI-ERR-002,Xử lý lỗi,Kiểm tra thông báo lỗi khi nhập sai định dạng,"1. Nhập dữ liệu sai định dạng (ví dụ: chữ vào trường số)
2. Nhấn nút lưu/thêm
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, đúng vị trí và chỉ ra lỗi định dạng",,,
UI-ERR-003,Xử lý lỗi,Kiểm tra thông báo lỗi khi nhập giá trị không hợp lệ,"1. Nhập giá trị không hợp lệ (ví dụ: số âm cho số lượng)
2. Nhấn nút lưu/thêm
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, đúng vị trí và chỉ ra giá trị không hợp lệ",,,
UI-ERR-004,Xử lý lỗi,Kiểm tra thông báo lỗi khi nhập giá trị vượt quá giới hạn,"1. Nhập giá trị vượt quá giới hạn (ví dụ: chuỗi quá dài)
2. Nhấn nút lưu/thêm
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, đúng vị trí và chỉ ra giới hạn",,,
UI-ERR-005,Xử lý lỗi,Kiểm tra thông báo lỗi khi mất kết nối,"1. Ngắt kết nối internet
2. Thực hiện các thao tác trên ứng dụng
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, chỉ ra vấn đề kết nối",,,
UI-ERR-006,Xử lý lỗi,Kiểm tra thông báo lỗi khi máy chủ không phản hồi,"1. Mô phỏng tình huống máy chủ không phản hồi
2. Thực hiện các thao tác trên ứng dụng
3. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, chỉ ra vấn đề máy chủ",,,
UI-ERR-007,Xử lý lỗi,Kiểm tra thông báo lỗi khi xảy ra lỗi hệ thống,"1. Mô phỏng tình huống lỗi hệ thống
2. Kiểm tra thông báo lỗi","Thông báo lỗi hiển thị rõ ràng, không hiển thị thông tin kỹ thuật chi tiết cho người dùng",,,
UI-ERR-008,Xử lý lỗi,Kiểm tra khả năng phục hồi sau lỗi,"1. Gây ra lỗi (ví dụ: nhập dữ liệu không hợp lệ)
2. Sửa lỗi và thử lại
3. Kiểm tra khả năng tiếp tục sử dụng","Ứng dụng có thể tiếp tục hoạt động bình thường sau khi sửa lỗi",,,
UI-ERR-009,Xử lý lỗi,Kiểm tra ghi nhật ký lỗi,"1. Gây ra lỗi
2. Kiểm tra nhật ký lỗi (nếu có thể truy cập)","Lỗi được ghi lại trong nhật ký với đầy đủ thông tin",,,
UI-ERR-010,Xử lý lỗi,Kiểm tra báo cáo lỗi,"1. Nếu có chức năng báo cáo lỗi, kiểm tra chức năng này
2. Gửi báo cáo lỗi","Báo cáo lỗi được gửi thành công và có thông báo xác nhận",,,
UI-HELP-001,Trợ giúp,Kiểm tra tooltip,"1. Di chuột qua các phần tử UI có tooltip
2. Kiểm tra hiển thị tooltip","Tooltip hiển thị đúng, rõ ràng và hữu ích",,,
UI-HELP-002,Trợ giúp,Kiểm tra placeholder,"1. Kiểm tra placeholder trong các trường nhập liệu","Placeholder hiển thị đúng, rõ ràng và hữu ích",,,
UI-HELP-003,Trợ giúp,Kiểm tra thông báo xác nhận,"1. Thực hiện các thao tác cần xác nhận (ví dụ: xóa)
2. Kiểm tra thông báo xác nhận","Thông báo xác nhận hiển thị rõ ràng, có nút Xác nhận và Hủy",,,
UI-HELP-004,Trợ giúp,Kiểm tra thông báo thành công,"1. Thực hiện các thao tác thành công
2. Kiểm tra thông báo thành công","Thông báo thành công hiển thị rõ ràng và biến mất sau thời gian phù hợp",,,
UI-HELP-005,Trợ giúp,Kiểm tra tài liệu trợ giúp,"1. Nếu có tài liệu trợ giúp, truy cập tài liệu
2. Kiểm tra nội dung tài liệu","Tài liệu trợ giúp đầy đủ, rõ ràng và hữu ích",,,
UI-NAV-001,Điều hướng,Kiểm tra menu chính,"1. Kiểm tra hiển thị menu chính
2. Nhấp vào các mục menu","Menu chính hiển thị đúng và các liên kết hoạt động",,,
UI-NAV-002,Điều hướng,Kiểm tra breadcrumb,"1. Nếu có breadcrumb, kiểm tra hiển thị
2. Nhấp vào các liên kết trong breadcrumb","Breadcrumb hiển thị đúng và các liên kết hoạt động",,,
UI-NAV-003,Điều hướng,Kiểm tra liên kết quay lại,"1. Nếu có nút Quay lại, nhấp vào nút
2. Kiểm tra điều hướng","Nút Quay lại hoạt động đúng, đưa người dùng về trang trước đó",,,
UI-NAV-004,Điều hướng,Kiểm tra điều hướng bằng bàn phím,"1. Sử dụng phím Tab để di chuyển qua các phần tử
2. Sử dụng phím Enter để kích hoạt liên kết","Có thể điều hướng và kích hoạt liên kết bằng bàn phím",,,
UI-NAV-005,Điều hướng,Kiểm tra điều hướng giữa các trang,"1. Điều hướng giữa các trang khác nhau
2. Kiểm tra trạng thái và dữ liệu","Trạng thái và dữ liệu được duy trì đúng khi điều hướng",,,
