!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).Sweetalert2=t()}(this,function(){"use strict";const t="SweetAlert2:",i=e=>e.charAt(0).toUpperCase()+e.slice(1),a=e=>Array.prototype.slice.call(e),r=e=>{console.warn("".concat(t," ").concat("object"==typeof e?e.join(" "):e))},s=e=>{console.error("".concat(t," ").concat(e))},n=[],c=(e,t)=>{t='"'.concat(e,'" is deprecated and will be removed in the next major release. Please use "').concat(t,'" instead.'),n.includes(t)||(n.push(t),r(t))},l=e=>"function"==typeof e?e():e,u=e=>e&&"function"==typeof e.toPromise,d=e=>u(e)?e.toPromise():Promise.resolve(e),p=e=>e&&Promise.resolve(e)===e,m={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},o=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],g={},h=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],f=e=>Object.prototype.hasOwnProperty.call(m,e),b=e=>-1!==o.indexOf(e),y=e=>g[e],v=e=>{!e.backdrop&&e.allowOutsideClick&&r('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`');for(const o in e)n=o,f(n)||r('Unknown parameter "'.concat(n,'"')),e.toast&&(t=o,h.includes(t)&&r('The parameter "'.concat(t,'" is incompatible with toasts'))),t=o,y(t)&&c(t,y(t));var t,n};var e=e=>{const t={};for(const n in e)t[e[n]]="swal2-"+e[n];return t};const w=e(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),C=e(["success","warning","info","question","error"]),k=()=>document.body.querySelector(".".concat(w.container)),A=e=>{const t=k();return t?t.querySelector(e):null},P=e=>A(".".concat(e)),B=()=>P(w.popup),x=()=>P(w.icon),E=()=>P(w.title),T=()=>P(w["html-container"]),S=()=>P(w.image),L=()=>P(w["progress-steps"]),O=()=>P(w["validation-message"]),j=()=>A(".".concat(w.actions," .").concat(w.confirm)),M=()=>A(".".concat(w.actions," .").concat(w.deny));const D=()=>A(".".concat(w.loader)),I=()=>A(".".concat(w.actions," .").concat(w.cancel)),H=()=>P(w.actions),q=()=>P(w.footer),V=()=>P(w["timer-progress-bar"]),N=()=>P(w.close),R=()=>{const e=a(B().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort((e,t)=>{e=parseInt(e.getAttribute("tabindex")),t=parseInt(t.getAttribute("tabindex"));return t<e?1:e<t?-1:0});var t=a(B().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter(e=>"-1"!==e.getAttribute("tabindex"));return(t=>{const n=[];for(let e=0;e<t.length;e++)-1===n.indexOf(t[e])&&n.push(t[e]);return n})(e.concat(t)).filter(e=>ae(e))},F=()=>!K(document.body,w["toast-shown"])&&!K(document.body,w["no-backdrop"]),U=()=>B()&&K(B(),w.toast);function W(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];const n=V();ae(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout(()=>{n.style.transition="width ".concat(e/1e3,"s linear"),n.style.width="0%"},10))}const z={previousBodyPadding:null},_=(t,e)=>{if(t.textContent="",e){const n=new DOMParser,o=n.parseFromString(e,"text/html");a(o.querySelector("head").childNodes).forEach(e=>{t.appendChild(e)}),a(o.querySelector("body").childNodes).forEach(e=>{t.appendChild(e)})}},K=(t,e)=>{if(!e)return!1;var n=e.split(/\s+/);for(let e=0;e<n.length;e++)if(!t.classList.contains(n[e]))return!1;return!0},Y=(e,t,n)=>{var o,i;if(o=e,i=t,a(o.classList).forEach(e=>{Object.values(w).includes(e)||Object.values(C).includes(e)||Object.values(i.showClass).includes(e)||o.classList.remove(e)}),t.customClass&&t.customClass[n]){if("string"!=typeof t.customClass[n]&&!t.customClass[n].forEach)return r("Invalid type of customClass.".concat(n,'! Expected string or iterable object, got "').concat(typeof t.customClass[n],'"'));$(e,t.customClass[n])}},Z=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(".".concat(w.popup," > .").concat(w[t]));case"checkbox":return e.querySelector(".".concat(w.popup," > .").concat(w.checkbox," input"));case"radio":return e.querySelector(".".concat(w.popup," > .").concat(w.radio," input:checked"))||e.querySelector(".".concat(w.popup," > .").concat(w.radio," input:first-child"));case"range":return e.querySelector(".".concat(w.popup," > .").concat(w.range," input"));default:return e.querySelector(".".concat(w.popup," > .").concat(w.input))}},J=e=>{var t;e.focus(),"file"!==e.type&&(t=e.value,e.value="",e.value=t)},X=(e,t,n)=>{e&&t&&(t="string"==typeof t?t.split(/\s+/).filter(Boolean):t).forEach(t=>{Array.isArray(e)?e.forEach(e=>{n?e.classList.add(t):e.classList.remove(t)}):n?e.classList.add(t):e.classList.remove(t)})},$=(e,t)=>{X(e,t,!0)},G=(e,t)=>{X(e,t,!1)},Q=(e,t)=>{var n=a(e.childNodes);for(let e=0;e<n.length;e++)if(K(n[e],t))return n[e]},ee=(e,t,n)=>{(n=n==="".concat(parseInt(n))?parseInt(n):n)||0===parseInt(n)?e.style[t]="number"==typeof n?"".concat(n,"px"):n:e.style.removeProperty(t)},te=function(e){e.style.display=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"flex"},ne=e=>{e.style.display="none"},oe=(e,t,n,o)=>{const i=e.querySelector(t);i&&(i.style[n]=o)},ie=(e,t,n)=>{t?te(e,n):ne(e)},ae=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),re=()=>!ae(j())&&!ae(M())&&!ae(I()),se=e=>!!(e.scrollHeight>e.clientHeight),ce=e=>{const t=window.getComputedStyle(e);var n=parseFloat(t.getPropertyValue("animation-duration")||"0"),e=parseFloat(t.getPropertyValue("transition-duration")||"0");return 0<n||0<e},le=()=>"undefined"==typeof window||"undefined"==typeof document,ue=100,de={},pe=()=>{de.previousActiveElement&&de.previousActiveElement.focus?(de.previousActiveElement.focus(),de.previousActiveElement=null):document.body&&document.body.focus()},me=o=>new Promise(e=>{if(!o)return e();var t=window.scrollX,n=window.scrollY;de.restoreFocusTimeout=setTimeout(()=>{pe(),e()},ue),window.scrollTo(t,n)}),ge='\n <div aria-labelledby="'.concat(w.title,'" aria-describedby="').concat(w["html-container"],'" class="').concat(w.popup,'" tabindex="-1">\n   <button type="button" class="').concat(w.close,'"></button>\n   <ul class="').concat(w["progress-steps"],'"></ul>\n   <div class="').concat(w.icon,'"></div>\n   <img class="').concat(w.image,'" />\n   <h2 class="').concat(w.title,'" id="').concat(w.title,'"></h2>\n   <div class="').concat(w["html-container"],'" id="').concat(w["html-container"],'"></div>\n   <input class="').concat(w.input,'" />\n   <input type="file" class="').concat(w.file,'" />\n   <div class="').concat(w.range,'">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="').concat(w.select,'"></select>\n   <div class="').concat(w.radio,'"></div>\n   <label for="').concat(w.checkbox,'" class="').concat(w.checkbox,'">\n     <input type="checkbox" />\n     <span class="').concat(w.label,'"></span>\n   </label>\n   <textarea class="').concat(w.textarea,'"></textarea>\n   <div class="').concat(w["validation-message"],'" id="').concat(w["validation-message"],'"></div>\n   <div class="').concat(w.actions,'">\n     <div class="').concat(w.loader,'"></div>\n     <button type="button" class="').concat(w.confirm,'"></button>\n     <button type="button" class="').concat(w.deny,'"></button>\n     <button type="button" class="').concat(w.cancel,'"></button>\n   </div>\n   <div class="').concat(w.footer,'"></div>\n   <div class="').concat(w["timer-progress-bar-container"],'">\n     <div class="').concat(w["timer-progress-bar"],'"></div>\n   </div>\n </div>\n').replace(/(^|\n)\s*/g,""),he=()=>{const e=k();return!!e&&(e.remove(),G([document.documentElement,document.body],[w["no-backdrop"],w["toast-shown"],w["has-column"]]),!0)},fe=()=>{de.currentInstance.resetValidationMessage()},be=()=>{const e=B(),t=Q(e,w.input),n=Q(e,w.file),o=e.querySelector(".".concat(w.range," input")),i=e.querySelector(".".concat(w.range," output")),a=Q(e,w.select),r=e.querySelector(".".concat(w.checkbox," input")),s=Q(e,w.textarea);t.oninput=fe,n.onchange=fe,a.onchange=fe,r.onchange=fe,s.oninput=fe,o.oninput=()=>{fe(),i.value=o.value},o.onchange=()=>{fe(),o.nextSibling.value=o.value}},ye=e=>"string"==typeof e?document.querySelector(e):e,ve=e=>{const t=B();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")},we=e=>{"rtl"===window.getComputedStyle(e).direction&&$(k(),w.rtl)},Ce=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?((e,t)=>{if(e.jquery)ke(t,e);else _(t,e.toString())})(e,t):e&&_(t,e)},ke=(t,n)=>{if(t.textContent="",0 in n)for(let e=0;e in n;e++)t.appendChild(n[e].cloneNode(!0));else t.appendChild(n.cloneNode(!0))},Ae=(()=>{if(le())return!1;var e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",animation:"animationend"};for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&void 0!==e.style[n])return t[n];return!1})(),Pe=(e,t)=>{var n,o,i,a,r,s=H(),c=D();(t.showConfirmButton||t.showDenyButton||t.showCancelButton?te:ne)(s),Y(s,t,"actions"),n=s,o=c,i=t,a=j(),r=M(),s=I(),Be(a,"confirm",i),Be(r,"deny",i),Be(s,"cancel",i),function(e,t,n,o){if(!o.buttonsStyling)return G([e,t,n],w.styled);$([e,t,n],w.styled),o.confirmButtonColor&&(e.style.backgroundColor=o.confirmButtonColor,$(e,w["default-outline"]));o.denyButtonColor&&(t.style.backgroundColor=o.denyButtonColor,$(t,w["default-outline"]));o.cancelButtonColor&&(n.style.backgroundColor=o.cancelButtonColor,$(n,w["default-outline"]))}(a,r,s,i),i.reverseButtons&&(i.toast?(n.insertBefore(s,a),n.insertBefore(r,a)):(n.insertBefore(s,o),n.insertBefore(r,o),n.insertBefore(a,o))),_(c,t.loaderHtml),Y(c,t,"loader")};function Be(e,t,n){ie(e,n["show".concat(i(t),"Button")],"inline-block"),_(e,n["".concat(t,"ButtonText")]),e.setAttribute("aria-label",n["".concat(t,"ButtonAriaLabel")]),e.className=w[t],Y(e,n,"".concat(t,"Button")),$(e,n["".concat(t,"ButtonClass")])}const xe=(e,t)=>{var n,o,i=k();i&&(o=i,"string"==typeof(n=t.backdrop)?o.style.background=n:n||$([document.documentElement,document.body],w["no-backdrop"]),o=i,(n=t.position)in w?$(o,w[n]):(r('The "position" parameter is not valid, defaulting to "center"'),$(o,w.center)),n=i,!(o=t.grow)||"string"!=typeof o||(o="grow-".concat(o))in w&&$(n,w[o]),Y(i,t,"container"))};var Ee={awaitingPromise:new WeakMap,promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap};const Te=["input","file","range","select","radio","checkbox","textarea"],Se=(e,o)=>{const i=B();e=Ee.innerParams.get(e);const a=!e||o.input!==e.input;Te.forEach(e=>{var t=w[e];const n=Q(i,t);((e,t)=>{const n=Z(B(),e);if(n){Le(n);for(const o in t)n.setAttribute(o,t[o])}})(e,o.inputAttributes),n.className=t,a&&ne(n)}),o.input&&(a&&(e=>{if(!De[e.input])return s('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(e.input,'"'));const t=Me(e.input),n=De[e.input](t,e);te(n),setTimeout(()=>{J(n)})})(o),(e=>{const t=Me(e.input);if(e.customClass)$(t,e.customClass.input)})(o))},Le=t=>{for(let e=0;e<t.attributes.length;e++){var n=t.attributes[e].name;["type","value","style"].includes(n)||t.removeAttribute(n)}},Oe=(e,t)=>{e.placeholder&&!t.inputPlaceholder||(e.placeholder=t.inputPlaceholder)},je=(e,t,n)=>{if(n.inputLabel){e.id=w.input;const i=document.createElement("label");var o=w["input-label"];i.setAttribute("for",e.id),i.className=o,$(i,n.customClass.inputLabel),i.innerText=n.inputLabel,t.insertAdjacentElement("beforebegin",i)}},Me=e=>{e=w[e]||w.input;return Q(B(),e)},De={};De.text=De.email=De.password=De.number=De.tel=De.url=(e,t)=>("string"==typeof t.inputValue||"number"==typeof t.inputValue?e.value=t.inputValue:p(t.inputValue)||r('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(typeof t.inputValue,'"')),je(e,e,t),Oe(e,t),e.type=t.input,e),De.file=(e,t)=>(je(e,e,t),Oe(e,t),e),De.range=(e,t)=>{const n=e.querySelector("input"),o=e.querySelector("output");return n.value=t.inputValue,n.type=t.input,o.value=t.inputValue,je(n,e,t),e},De.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const n=document.createElement("option");_(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return je(e,e,t),e},De.radio=e=>(e.textContent="",e),De.checkbox=(e,t)=>{const n=Z(B(),"checkbox");n.value="1",n.id=w.checkbox,n.checked=Boolean(t.inputValue);var o=e.querySelector("span");return _(o,t.inputPlaceholder),e},De.textarea=(n,e)=>{n.value=e.inputValue,Oe(n,e),je(n,n,e);return setTimeout(()=>{if("MutationObserver"in window){const t=parseInt(window.getComputedStyle(B()).width);new MutationObserver(()=>{var e=n.offsetWidth+(e=n,parseInt(window.getComputedStyle(e).marginLeft)+parseInt(window.getComputedStyle(e).marginRight));e>t?B().style.width="".concat(e,"px"):B().style.width=null}).observe(n,{attributes:!0,attributeFilter:["style"]})}}),n};const Ie=(e,t)=>{const n=T();Y(n,t,"htmlContainer"),t.html?(Ce(t.html,n),te(n,"block")):t.text?(n.textContent=t.text,te(n,"block")):ne(n),Se(e,t)},He=(e,t)=>{var n=q();ie(n,t.footer),t.footer&&Ce(t.footer,n),Y(n,t,"footer")},qe=(e,t)=>{const n=N();_(n,t.closeButtonHtml),Y(n,t,"closeButton"),ie(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel)},Ve=(e,t)=>{var n=Ee.innerParams.get(e),e=x();return n&&t.icon===n.icon?(Fe(e,t),void Ne(e,t)):t.icon||t.iconHtml?t.icon&&-1===Object.keys(C).indexOf(t.icon)?(s('Unknown icon! Expected "success", "error", "warning", "info" or "question", got "'.concat(t.icon,'"')),ne(e)):(te(e),Fe(e,t),Ne(e,t),void $(e,t.showClass.icon)):ne(e)},Ne=(e,t)=>{for(const n in C)t.icon!==n&&G(e,C[n]);$(e,C[t.icon]),Ue(e,t),Re(),Y(e,t,"icon")},Re=()=>{const e=B();var t=window.getComputedStyle(e).getPropertyValue("background-color");const n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let e=0;e<n.length;e++)n[e].style.backgroundColor=t},Fe=(e,t)=>{var n;e.textContent="",t.iconHtml?_(e,We(t.iconHtml)):"success"===t.icon?_(e,'\n      <div class="swal2-success-circular-line-left"></div>\n      <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n      <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n      <div class="swal2-success-circular-line-right"></div>\n    '):"error"===t.icon?_(e,'\n      <span class="swal2-x-mark">\n        <span class="swal2-x-mark-line-left"></span>\n        <span class="swal2-x-mark-line-right"></span>\n      </span>\n    '):(n={question:"?",warning:"!",info:"i"},_(e,We(n[t.icon])))},Ue=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const n of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])oe(e,n,"backgroundColor",t.iconColor);oe(e,".swal2-success-ring","borderColor",t.iconColor)}},We=e=>'<div class="'.concat(w["icon-content"],'">').concat(e,"</div>"),ze=(e,t)=>{const n=S();if(!t.imageUrl)return ne(n);te(n,""),n.setAttribute("src",t.imageUrl),n.setAttribute("alt",t.imageAlt),ee(n,"width",t.imageWidth),ee(n,"height",t.imageHeight),n.className=w.image,Y(n,t,"image")},_e=(e,o)=>{const i=L();if(!o.progressSteps||0===o.progressSteps.length)return ne(i);te(i),i.textContent="",o.currentProgressStep>=o.progressSteps.length&&r("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),o.progressSteps.forEach((e,t)=>{var n,e=(n=e,e=document.createElement("li"),$(e,w["progress-step"]),_(e,n),e);i.appendChild(e),t===o.currentProgressStep&&$(e,w["active-progress-step"]),t!==o.progressSteps.length-1&&(t=(e=>{const t=document.createElement("li");return $(t,w["progress-step-line"]),e.progressStepsDistance&&(t.style.width=e.progressStepsDistance),t})(o),i.appendChild(t))})},Ke=(e,t)=>{const n=E();ie(n,t.title||t.titleText,"block"),t.title&&Ce(t.title,n),t.titleText&&(n.innerText=t.titleText),Y(n,t,"title")},Ye=(e,t)=>{var n=k();const o=B();t.toast?(ee(n,"width",t.width),o.style.width="100%",o.insertBefore(D(),x())):ee(o,"width",t.width),ee(o,"padding",t.padding),t.color&&(o.style.color=t.color),t.background&&(o.style.background=t.background),ne(O()),((e,t)=>{if(e.className="".concat(w.popup," ").concat(ae(e)?t.showClass.popup:""),t.toast){$([document.documentElement,document.body],w["toast-shown"]);$(e,w.toast)}else $(e,w.modal);if(Y(e,t,"popup"),typeof t.customClass==="string")$(e,t.customClass);if(t.icon)$(e,w["icon-".concat(t.icon)])})(o,t)},Ze=(e,t)=>{Ye(e,t),xe(e,t),_e(e,t),Ve(e,t),ze(e,t),Ke(e,t),qe(e,t),Ie(e,t),Pe(e,t),He(e,t),"function"==typeof t.didRender&&t.didRender(B())},Je=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Xe=()=>{const e=a(document.body.children);e.forEach(e=>{e===k()||e.contains(k())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))})},$e=()=>{const e=a(document.body.children);e.forEach(e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")})},Ge=["swal-title","swal-html","swal-footer"],Qe=e=>{const n={};return a(e.querySelectorAll("swal-param")).forEach(e=>{rt(e,["name","value"]);var t=e.getAttribute("name"),e=e.getAttribute("value");"boolean"==typeof m[t]&&"false"===e&&(n[t]=!1),"object"==typeof m[t]&&(n[t]=JSON.parse(e))}),n},et=e=>{const n={};return a(e.querySelectorAll("swal-button")).forEach(e=>{rt(e,["type","color","aria-label"]);var t=e.getAttribute("type");n["".concat(t,"ButtonText")]=e.innerHTML,n["show".concat(i(t),"Button")]=!0,e.hasAttribute("color")&&(n["".concat(t,"ButtonColor")]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(n["".concat(t,"ButtonAriaLabel")]=e.getAttribute("aria-label"))}),n},tt=e=>{const t={},n=e.querySelector("swal-image");return n&&(rt(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt"))),t},nt=e=>{const t={},n=e.querySelector("swal-icon");return n&&(rt(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},ot=e=>{const n={},t=e.querySelector("swal-input");t&&(rt(t,["type","label","placeholder","value"]),n.input=t.getAttribute("type")||"text",t.hasAttribute("label")&&(n.inputLabel=t.getAttribute("label")),t.hasAttribute("placeholder")&&(n.inputPlaceholder=t.getAttribute("placeholder")),t.hasAttribute("value")&&(n.inputValue=t.getAttribute("value")));e=e.querySelectorAll("swal-input-option");return e.length&&(n.inputOptions={},a(e).forEach(e=>{rt(e,["value"]);var t=e.getAttribute("value"),e=e.innerHTML;n.inputOptions[t]=e})),n},it=(e,t)=>{const n={};for(const o in t){const i=t[o],a=e.querySelector(i);a&&(rt(a,[]),n[i.replace(/^swal-/,"")]=a.innerHTML.trim())}return n},at=e=>{const t=Ge.concat(["swal-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);a(e.children).forEach(e=>{e=e.tagName.toLowerCase();-1===t.indexOf(e)&&r("Unrecognized element <".concat(e,">"))})},rt=(t,n)=>{a(t.attributes).forEach(e=>{-1===n.indexOf(e.name)&&r(['Unrecognized attribute "'.concat(e.name,'" on <').concat(t.tagName.toLowerCase(),">."),"".concat(n.length?"Allowed attributes are: ".concat(n.join(", ")):"To set the value, use HTML within the element.")])})};var st={email:(e,t)=>/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function ct(e){var t,n;(t=e).inputValidator||Object.keys(st).forEach(e=>{t.input===e&&(t.inputValidator=st[e])}),e.showLoaderOnConfirm&&!e.preConfirm&&r("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),(n=e).target&&("string"!=typeof n.target||document.querySelector(n.target))&&("string"==typeof n.target||n.target.appendChild)||(r('Target parameter is not valid, defaulting to "body"'),n.target="body"),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),(e=>{var t=he();if(le())s("SweetAlert2 requires document to initialize");else{const n=document.createElement("div");n.className=w.container,t&&$(n,w["no-transition"]),_(n,ge);const o=ye(e.target);o.appendChild(n),ve(e),we(o),be()}})(e)}class lt{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){var t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const ut=()=>{null===z.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(z.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight="".concat(z.previousBodyPadding+(()=>{const e=document.createElement("div");e.className=w["scrollbar-measure"],document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})(),"px"))},dt=()=>{null!==z.previousBodyPadding&&(document.body.style.paddingRight="".concat(z.previousBodyPadding,"px"),z.previousBodyPadding=null)},pt=()=>{var e;(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints)&&!K(document.body,w.iosfix)&&(e=document.body.scrollTop,document.body.style.top="".concat(-1*e,"px"),$(document.body,w.iosfix),(()=>{const e=k();let t;e.ontouchstart=e=>{t=mt(e)},e.ontouchmove=e=>{if(t){e.preventDefault();e.stopPropagation()}}})(),(()=>{const e=navigator.userAgent,t=!!e.match(/iPad/i)||!!e.match(/iPhone/i),n=!!e.match(/WebKit/i),o=t&&n&&!e.match(/CriOS/i);if(o){const i=44;if(B().scrollHeight>window.innerHeight-i)k().style.paddingBottom="".concat(i,"px")}})())},mt=e=>{var t,n=e.target,o=k();return!((t=e).touches&&t.touches.length&&"stylus"===t.touches[0].touchType||(e=e).touches&&1<e.touches.length)&&(n===o||!(se(o)||"INPUT"===n.tagName||"TEXTAREA"===n.tagName||se(T())&&T().contains(n)))},gt=()=>{var e;K(document.body,w.iosfix)&&(e=parseInt(document.body.style.top,10),G(document.body,w.iosfix),document.body.style.top="",document.body.scrollTop=-1*e)},ht=10,ft=e=>{const t=B();if(e.target===t){const n=k();t.removeEventListener(Ae,ft),n.style.overflowY="auto"}},bt=(e,t)=>{Ae&&ce(t)?(e.style.overflowY="hidden",t.addEventListener(Ae,ft)):e.style.overflowY="auto"},yt=(e,t,n)=>{pt(),t&&"hidden"!==n&&ut(),setTimeout(()=>{e.scrollTop=0})},vt=(e,t,n)=>{$(e,n.showClass.backdrop),t.style.setProperty("opacity","0","important"),te(t,"grid"),setTimeout(()=>{$(t,n.showClass.popup),t.style.removeProperty("opacity")},ht),$([document.documentElement,document.body],w.shown),n.heightAuto&&n.backdrop&&!n.toast&&$([document.documentElement,document.body],w["height-auto"])},wt=e=>{let t=B();t||new fn,t=B();var n=D();U()?ne(x()):((e,t)=>{const n=H(),o=D();if(!t&&ae(j()))t=j();if(te(n),t){ne(t);o.setAttribute("data-button-to-replace",t.className)}o.parentNode.insertBefore(o,t),$([e,n],w.loading)})(t,e),te(n),t.setAttribute("data-loading",!0),t.setAttribute("aria-busy",!0),t.focus()},Ct=(t,n)=>{const o=B(),i=e=>At[n.input](o,Pt(e),n);u(n.inputOptions)||p(n.inputOptions)?(wt(j()),d(n.inputOptions).then(e=>{t.hideLoading(),i(e)})):"object"==typeof n.inputOptions?i(n.inputOptions):s("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(typeof n.inputOptions))},kt=(t,n)=>{const o=t.getInput();ne(o),d(n.inputValue).then(e=>{o.value="number"===n.input?parseFloat(e)||0:"".concat(e),te(o),o.focus(),t.hideLoading()}).catch(e=>{s("Error in inputValue promise: ".concat(e)),o.value="",te(o),o.focus(),t.hideLoading()})},At={select:(e,t,i)=>{const a=Q(e,w.select),r=(e,t,n)=>{const o=document.createElement("option");o.value=n,_(o,t),o.selected=Bt(n,i.inputValue),e.appendChild(o)};t.forEach(e=>{var t=e[0];const n=e[1];if(Array.isArray(n)){const o=document.createElement("optgroup");o.label=t,o.disabled=!1,a.appendChild(o),n.forEach(e=>r(o,e[1],e[0]))}else r(a,n,t)}),a.focus()},radio:(e,t,a)=>{const r=Q(e,w.radio);t.forEach(e=>{var t=e[0],e=e[1];const n=document.createElement("input"),o=document.createElement("label");n.type="radio",n.name=w.radio,n.value=t,Bt(t,a.inputValue)&&(n.checked=!0);const i=document.createElement("span");_(i,e),i.className=w.label,o.appendChild(n),o.appendChild(i),r.appendChild(o)});const n=r.querySelectorAll("input");n.length&&n[0].focus()}},Pt=n=>{const o=[];return"undefined"!=typeof Map&&n instanceof Map?n.forEach((e,t)=>{let n=e;"object"==typeof n&&(n=Pt(n)),o.push([t,n])}):Object.keys(n).forEach(e=>{let t=n[e];"object"==typeof t&&(t=Pt(t)),o.push([e,t])}),o},Bt=(e,t)=>t&&t.toString()===e.toString(),xt=(e,t)=>{var n=Ee.innerParams.get(e);if(!n.input)return s('The "input" parameter is needed to be set when using returnInputValueOn'.concat(i(t)));var o=((e,t)=>{const n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return n.checked?1:0;case"radio":return(o=n).checked?o.value:null;case"file":return(o=n).files.length?null!==o.getAttribute("multiple")?o.files:o.files[0]:null;default:return t.inputAutoTrim?n.value.trim():n.value}var o})(e,n);n.inputValidator?((t,n,o)=>{const e=Ee.innerParams.get(t);t.disableInput();const i=Promise.resolve().then(()=>d(e.inputValidator(n,e.validationMessage)));i.then(e=>{t.enableButtons();t.enableInput();if(e)t.showValidationMessage(e);else if(o==="deny")Et(t,n);else Lt(t,n)})})(e,o,t):e.getInput().checkValidity()?("deny"===t?Et:Lt)(e,o):(e.enableButtons(),e.showValidationMessage(n.validationMessage))},Et=(t,n)=>{const e=Ee.innerParams.get(t||void 0);if(e.showLoaderOnDeny&&wt(M()),e.preDeny){Ee.awaitingPromise.set(t||void 0,!0);const o=Promise.resolve().then(()=>d(e.preDeny(n,e.validationMessage)));o.then(e=>{!1===e?t.hideLoading():t.closePopup({isDenied:!0,value:void 0===e?n:e})}).catch(e=>St(t||void 0,e))}else t.closePopup({isDenied:!0,value:n})},Tt=(e,t)=>{e.closePopup({isConfirmed:!0,value:t})},St=(e,t)=>{e.rejectPromise(t)},Lt=(t,n)=>{const e=Ee.innerParams.get(t||void 0);if(e.showLoaderOnConfirm&&wt(),e.preConfirm){t.resetValidationMessage(),Ee.awaitingPromise.set(t||void 0,!0);const o=Promise.resolve().then(()=>d(e.preConfirm(n,e.validationMessage)));o.then(e=>{ae(O())||!1===e?t.hideLoading():Tt(t,void 0===e?n:e)}).catch(e=>St(t||void 0,e))}else Tt(t,n)},Ot=(n,e,o)=>{e.popup.onclick=()=>{var e,t=Ee.innerParams.get(n);t&&((e=t).showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton||t.timer||t.input)||o(Je.close)}};let jt=!1;const Mt=t=>{t.popup.onmousedown=()=>{t.container.onmouseup=function(e){t.container.onmouseup=void 0,e.target===t.container&&(jt=!0)}}},Dt=t=>{t.container.onmousedown=()=>{t.popup.onmouseup=function(e){t.popup.onmouseup=void 0,e.target!==t.popup&&!t.popup.contains(e.target)||(jt=!0)}}},It=(n,o,i)=>{o.container.onclick=e=>{var t=Ee.innerParams.get(n);jt?jt=!1:e.target===o.container&&l(t.allowOutsideClick)&&i(Je.backdrop)}};const Ht=()=>j()&&j().click();const qt=(e,t,n)=>{const o=R();if(o.length)return(t+=n)===o.length?t=0:-1===t&&(t=o.length-1),o[t].focus();B().focus()},Vt=["ArrowRight","ArrowDown"],Nt=["ArrowLeft","ArrowUp"],Rt=(e,t,n)=>{var o,i,a=Ee.innerParams.get(e);a&&(a.stopKeydownPropagation&&t.stopPropagation(),"Enter"===t.key?(o=e,i=a,(e=t).isComposing||e.target&&o.getInput()&&e.target.outerHTML===o.getInput().outerHTML&&(["textarea","file"].includes(i.input)||(Ht(),e.preventDefault()))):"Tab"===t.key?((e,t)=>{const n=e.target,o=R();let i=-1;for(let e=0;e<o.length;e++)if(n===o[e]){i=e;break}if(!e.shiftKey)qt(t,i,1);else qt(t,i,-1);e.stopPropagation(),e.preventDefault()})(t,a):[...Vt,...Nt].includes(t.key)?(e=>{const t=j(),n=M(),o=I();if([t,n,o].includes(document.activeElement)){var i=Vt.includes(e)?"nextElementSibling":"previousElementSibling";const a=document.activeElement[i];a instanceof HTMLElement&&a.focus()}})(t.key):"Escape"===t.key&&((e,t,n)=>{if(l(t.allowEscapeKey)){e.preventDefault();n(Je.esc)}})(t,a,n))},Ft=e=>"object"==typeof e&&e.jquery,Ut=e=>e instanceof Element||Ft(e);const Wt=()=>{if(de.timeout)return(()=>{const e=V();var t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";t=t/parseInt(window.getComputedStyle(e).width)*100;e.style.removeProperty("transition"),e.style.width="".concat(t,"%")})(),de.timeout.stop()},zt=()=>{if(de.timeout){var e=de.timeout.start();return W(e),e}};let _t=!1;const Kt={};const Yt=t=>{for(let e=t.target;e&&e!==document;e=e.parentNode)for(const o in Kt){var n=e.getAttribute(o);if(n)return void Kt[o].fire({template:n})}};var Zt=Object.freeze({isValidParameter:f,isUpdatableParameter:b,isDeprecatedParameter:y,argsToParams:n=>{const o={};return"object"!=typeof n[0]||Ut(n[0])?["title","html","icon"].forEach((e,t)=>{t=n[t];"string"==typeof t||Ut(t)?o[e]=t:void 0!==t&&s("Unexpected type of ".concat(e,'! Expected "string" or "Element", got ').concat(typeof t))}):Object.assign(o,n[0]),o},isVisible:()=>ae(B()),clickConfirm:Ht,clickDeny:()=>M()&&M().click(),clickCancel:()=>I()&&I().click(),getContainer:k,getPopup:B,getTitle:E,getHtmlContainer:T,getImage:S,getIcon:x,getInputLabel:()=>P(w["input-label"]),getCloseButton:N,getActions:H,getConfirmButton:j,getDenyButton:M,getCancelButton:I,getLoader:D,getFooter:q,getTimerProgressBar:V,getFocusableElements:R,getValidationMessage:O,isLoading:()=>B().hasAttribute("data-loading"),fire:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return new this(...t)},mixin:function(n){class e extends this{_main(e,t){return super._main(e,Object.assign({},n,t))}}return e},showLoading:wt,enableLoading:wt,getTimerLeft:()=>de.timeout&&de.timeout.getTimerLeft(),stopTimer:Wt,resumeTimer:zt,toggleTimer:()=>{var e=de.timeout;return e&&(e.running?Wt:zt)()},increaseTimer:e=>{if(de.timeout){e=de.timeout.increase(e);return W(e,!0),e}},isTimerRunning:()=>de.timeout&&de.timeout.isRunning(),bindClickHandler:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"data-swal-template";Kt[e]=this,_t||(document.body.addEventListener("click",Yt),_t=!0)}});function Jt(){var e=Ee.innerParams.get(this);if(e){const t=Ee.domCache.get(this);ne(t.loader),U()?e.icon&&te(x()):(e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));if(t.length)te(t[0],"inline-block");else if(re())ne(e.actions)})(t),G([t.popup,t.actions],w.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}}var Xt={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};function $t(e,t,n,o){U()?tn(e,o):(me(n).then(()=>tn(e,o)),de.keydownTarget.removeEventListener("keydown",de.keydownHandler,{capture:de.keydownListenerCapture}),de.keydownHandlerAdded=!1),/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),F()&&(dt(),gt(),$e()),G([document.documentElement,document.body],[w.shown,w["height-auto"],w["no-backdrop"],w["toast-shown"]])}function Gt(e){e=void 0!==(n=e)?Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},n):{isConfirmed:!1,isDenied:!1,isDismissed:!0};const t=Xt.swalPromiseResolve.get(this);var n=(e=>{const t=B();if(!t)return false;const n=Ee.innerParams.get(e);if(!n||K(t,n.hideClass.popup))return false;G(t,n.showClass.popup),$(t,n.hideClass.popup);const o=k();return G(o,n.showClass.backdrop),$(o,n.hideClass.backdrop),en(e,t,n),true})(this);this.isAwaitingPromise()?e.isDismissed||(Qt(this),t(e)):n&&t(e)}const Qt=e=>{e.isAwaitingPromise()&&(Ee.awaitingPromise.delete(e),Ee.innerParams.get(e)||e._destroy())},en=(e,t,n)=>{var o,i,a,r=k(),s=Ae&&ce(t);"function"==typeof n.willClose&&n.willClose(t),s?(o=e,i=t,a=r,s=n.returnFocus,t=n.didClose,de.swalCloseEventFinishedCallback=$t.bind(null,o,a,s,t),i.addEventListener(Ae,function(e){e.target===i&&(de.swalCloseEventFinishedCallback(),delete de.swalCloseEventFinishedCallback)})):$t(e,r,n.returnFocus,n.didClose)},tn=(e,t)=>{setTimeout(()=>{"function"==typeof t&&t.bind(e.params)(),e._destroy()})};function nn(e,t,n){const o=Ee.domCache.get(e);t.forEach(e=>{o[e].disabled=n})}function on(e,t){if(!e)return!1;if("radio"===e.type){const n=e.parentNode.parentNode,o=n.querySelectorAll("input");for(let e=0;e<o.length;e++)o[e].disabled=t}else e.disabled=t}const an=e=>{e.isAwaitingPromise()?(rn(Ee,e),Ee.awaitingPromise.set(e,!0)):(rn(Xt,e),rn(Ee,e))},rn=(e,t)=>{for(const n in e)e[n].delete(t)};e=Object.freeze({hideLoading:Jt,disableLoading:Jt,getInput:function(e){var t=Ee.innerParams.get(e||this);return(e=Ee.domCache.get(e||this))?Z(e.popup,t.input):null},close:Gt,isAwaitingPromise:function(){return!!Ee.awaitingPromise.get(this)},rejectPromise:function(e){const t=Xt.swalPromiseReject.get(this);Qt(this),t&&t(e)},closePopup:Gt,closeModal:Gt,closeToast:Gt,enableButtons:function(){nn(this,["confirmButton","denyButton","cancelButton"],!1)},disableButtons:function(){nn(this,["confirmButton","denyButton","cancelButton"],!0)},enableInput:function(){return on(this.getInput(),!1)},disableInput:function(){return on(this.getInput(),!0)},showValidationMessage:function(e){const t=Ee.domCache.get(this);var n=Ee.innerParams.get(this);_(t.validationMessage,e),t.validationMessage.className=w["validation-message"],n.customClass&&n.customClass.validationMessage&&$(t.validationMessage,n.customClass.validationMessage),te(t.validationMessage);const o=this.getInput();o&&(o.setAttribute("aria-invalid",!0),o.setAttribute("aria-describedby",w["validation-message"]),J(o),$(o,w.inputerror))},resetValidationMessage:function(){var e=Ee.domCache.get(this);e.validationMessage&&ne(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),G(t,w.inputerror))},getProgressSteps:function(){return Ee.domCache.get(this).progressSteps},update:function(t){var e=B(),n=Ee.innerParams.get(this);if(!e||K(e,n.hideClass.popup))return r("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const o={};Object.keys(t).forEach(e=>{b(e)?o[e]=t[e]:r('Invalid parameter to update: "'.concat(e,'". Updatable params are listed here: https://github.com/sweetalert2/sweetalert2/blob/master/src/utils/params.js\n\nIf you think this parameter should be updatable, request it here: https://github.com/sweetalert2/sweetalert2/issues/new?template=02_feature_request.md'))}),n=Object.assign({},n,o),Ze(this,n),Ee.innerParams.set(this,n),Object.defineProperties(this,{params:{value:Object.assign({},this.params,t),writable:!1,enumerable:!0}})},_destroy:function(){var e=Ee.domCache.get(this);const t=Ee.innerParams.get(this);t?(e.popup&&de.swalCloseEventFinishedCallback&&(de.swalCloseEventFinishedCallback(),delete de.swalCloseEventFinishedCallback),de.deferDisposalTimer&&(clearTimeout(de.deferDisposalTimer),delete de.deferDisposalTimer),"function"==typeof t.didDestroy&&t.didDestroy(),e=this,an(e),delete e.params,delete de.keydownHandler,delete de.keydownTarget,delete de.currentInstance):an(this)}});let sn;class cn{constructor(){if("undefined"!=typeof window){sn=this;for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=Object.freeze(this.constructor.argsToParams(t));Object.defineProperties(this,{params:{value:o,writable:!1,enumerable:!0,configurable:!0}});o=this._main(this.params);Ee.promise.set(this,o)}}_main(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};v(Object.assign({},t,e)),de.currentInstance&&(de.currentInstance._destroy(),F()&&$e()),de.currentInstance=this;e=un(e,t);ct(e),Object.freeze(e),de.timeout&&(de.timeout.stop(),delete de.timeout),clearTimeout(de.restoreFocusTimeout);t=dn(this);return Ze(this,e),Ee.innerParams.set(this,e),ln(this,t,e)}then(e){const t=Ee.promise.get(this);return t.then(e)}finally(e){const t=Ee.promise.get(this);return t.finally(e)}}const ln=(r,s,c)=>new Promise((e,t)=>{const n=e=>{r.closePopup({isDismissed:!0,dismiss:e})};var o,i,a;Xt.swalPromiseResolve.set(r,e),Xt.swalPromiseReject.set(r,t),s.confirmButton.onclick=()=>(e=>{var t=Ee.innerParams.get(e);e.disableButtons(),t.input?xt(e,"confirm"):Lt(e,!0)})(r),s.denyButton.onclick=()=>(e=>{var t=Ee.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?xt(e,"deny"):Et(e,!1)})(r),s.cancelButton.onclick=()=>((e,t)=>{e.disableButtons(),t(Je.cancel)})(r,n),s.closeButton.onclick=()=>n(Je.close),o=r,e=s,t=n,Ee.innerParams.get(o).toast?Ot(o,e,t):(Mt(e),Dt(e),It(o,e,t)),i=r,e=de,t=c,a=n,e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1),t.toast||(e.keydownHandler=e=>Rt(i,e,a),e.keydownTarget=t.keydownListenerCapture?window:B(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0),t=r,"select"===(e=c).input||"radio"===e.input?Ct(t,e):["text","email","number","tel","textarea"].includes(e.input)&&(u(e.inputValue)||p(e.inputValue))&&(wt(j()),kt(t,e)),(e=>{const t=k(),n=B();"function"==typeof e.willOpen&&e.willOpen(n);var o=window.getComputedStyle(document.body).overflowY;vt(t,n,e),setTimeout(()=>{bt(t,n)},ht),F()&&(yt(t,e.scrollbarPadding,o),Xe()),U()||de.previousActiveElement||(de.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout(()=>e.didOpen(n)),G(t,w["no-transition"])})(c),pn(de,c,n),mn(s,c),setTimeout(()=>{s.container.scrollTop=0})}),un=(e,t)=>{var n=(e=>{e="string"==typeof e.template?document.querySelector(e.template):e.template;if(!e)return{};e=e.content;return at(e),Object.assign(Qe(e),et(e),tt(e),nt(e),ot(e),it(e,Ge))})(e);const o=Object.assign({},m,t,n,e);return o.showClass=Object.assign({},m.showClass,o.showClass),o.hideClass=Object.assign({},m.hideClass,o.hideClass),o},dn=e=>{var t={popup:B(),container:k(),actions:H(),confirmButton:j(),denyButton:M(),cancelButton:I(),loader:D(),closeButton:N(),validationMessage:O(),progressSteps:L()};return Ee.domCache.set(e,t),t},pn=(e,t,n)=>{var o=V();ne(o),t.timer&&(e.timeout=new lt(()=>{n("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(te(o),setTimeout(()=>{e.timeout&&e.timeout.running&&W(t.timer)})))},mn=(e,t)=>{if(!t.toast)return l(t.allowEnterKey)?void(gn(e,t)||qt(t,-1,1)):hn()},gn=(e,t)=>t.focusDeny&&ae(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&ae(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!ae(e.confirmButton))&&(e.confirmButton.focus(),!0),hn=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};Object.assign(cn.prototype,e),Object.assign(cn,Zt),Object.keys(e).forEach(e=>{cn[e]=function(){if(sn)return sn[e](...arguments)}}),cn.DismissReason=Je,cn.version="11.3.6";const fn=cn;return fn.default=fn,fn}),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2);