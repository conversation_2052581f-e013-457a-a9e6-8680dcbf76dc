@charset "UTF-8";
@import url(https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&amp;display=swap);
@import url(https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap);

/* @font-face {
    font-family: hkgrotesk;
    src: url(../fonts/hkgrotesk-light.eot);
    src: local("hkgrotesk light"), url(../fonts/hkgrotesk-light.woff) format("woff");
    font-weight: 300
}

@font-face {
    font-family: hkgrotesk;
    src: url(../fonts/hkgrotesk-regular.eot);
    src: local("hkgrotesk regular"), url(../fonts/hkgrotesk-regular.woff) format("woff");
    font-weight: 400
}

@font-face {
    font-family: hkgrotesk;
    src: url(../fonts/hkgrotesk-medium.eot);
    src: local("hkgrotesk medium"), url(../fonts/hkgrotesk-medium.woff) format("woff");
    font-weight: 500
}

@font-face {
    font-family: hkgrotesk;
    src: url(../fonts/hkgrotesk-semibold.eot);
    src: local("hkgrotesk semibold"), url(../fonts/hkgrotesk-semibold.woff) format("woff");
    font-weight: 600
}

@font-face {
    font-family: hkgrotesk;
    src: url(../fonts/hkgrotesk-bold.eot);
    src: local("hkgrotesk bold"), url(../fonts/hkgrotesk-bold.woff) format("woff");
    font-weight: 700
} */

:root {
    --vz-body-bg: #f2f2f7;
    --vz-body-color: #212529;
    --vz-body-color-rgb: 33, 37, 41;
    --vz-vertical-menu-bg: #fff;
    --vz-vertical-menu-item-color: #6d7080;
    --vz-vertical-menu-item-hover-color: #4b38b3;
    --vz-vertical-menu-item-active-color: #4b38b3;
    --vz-vertical-menu-sub-item-color: #7c7f90;
    --vz-vertical-menu-sub-item-hover-color: #4b38b3;
    --vz-vertical-menu-sub-item-active-color: #4b38b3;
    --vz-vertical-menu-title-color: #919da9;
    --vz-vertical-menu-bg-dark: #151529;
    --vz-vertical-menu-item-color-dark: #a3a6b7;
    --vz-vertical-menu-item-hover-color-dark: #fff;
    --vz-vertical-menu-item-active-color-dark: #fff;
    --vz-vertical-menu-sub-item-color-dark: #a3a6b7;
    --vz-vertical-menu-sub-item-hover-color-dark: #fff;
    --vz-vertical-menu-sub-item-active-color-dark: #fff;
    --vz-vertical-menu-title-color-dark: #6d7080;
    --vz-header-bg: #fff;
    --vz-header-item-color: #e9ecef;
    --vz-header-bg-dark: #1C1C36;
    --vz-header-item-color-dark: var(--vz-vertical-menu-item-color-dark);
    --vz-topbar-search-bg: #f3f3f9;
    --vz-topbar-user-bg: #f3f3f9;
    --vz-topbar-user-bg-dark: #2A2A50;
    --vz-footer-bg: #fff;
    --vz-footer-color: #98a6ad;
    --vz-topnav-bg: #fff;
    --vz-topnav-item-color: #6d7080;
    --vz-topnav-item-color-active: #4b38b3;
    --vz-twocolumn-menu-iconview-bg: #fff;
    --vz-twocolumn-menu-bg: #fff;
    --vz-twocolumn-menu-iconview-bg-dark: var(--vz-vertical-menu-bg-dark);
    --vz-twocolumn-menu-bg-dark: #1C1C36;
    --vz-twocolumn-menu-item-color-dark: var(--vz-vertical-menu-item-color-dark);
    --vz-twocolumn-menu-item-active-color-dark: #fff;
    --vz-twocolumn-menu-item-active-bg-dark: rgba(255, 255, 255, 0.15);
    --vz-boxed-body-bg: #e5e5ef;
    --vz-heading-color: #495057;
    --vz-box-shadow: 0 3px 3px rgba(56, 65, 74, 0.1);
    --vz-light: #f3f6f9;
    --vz-light-rgb: 243, 246, 249;
    --vz-dark: #212529;
    --vz-dark-rgb: 33, 37, 41;
    --vz-link-color: #4b38b3;
    --vz-link-hover-color: #4b38b3;
    --vz-border-color: #e9ebec;
    --vz-dropdown-bg: #fff;
    --vz-dropdown-link-color: #212529;
    --vz-dropdown-link-hover-color: #1e2125;
    --vz-dropdown-link-hover-bg: #f3f6f9;
    --vz-dropdown-border-width: 0;
    --vz-card-bg: #fff;
    --vz-card-cap-bg: #fff;
    --vz-card-logo-dark: block;
    --vz-card-logo-light: none;
    --vz-modal-bg: #fff;
    --vz-nav-tabs-link-active-color: #495057;
    --vz-nav-tabs-link-active-bg: #f2f2f7;
    --vz-accordion-button-active-color: #4432a1;
    --vz-progress-bg: #eff2f7;
    --vz-toast-background-color: rgba(255, 255, 255, 0.85);
    --vz-toast-border-color: rgba(0, 0, 0, 0.1);
    --vz-toast-header-border-color: rgba(0, 0, 0, 0.05);
    --vz-list-group-hover-bg: #f3f6f9;
    --vz-popover-bg: #fff;
    --vz-pagination-hover-bg: #eff2f7;
    --vz-input-bg: #fff;
    --vz-input-border: #ced4da;
    --vz-input-focus-border: #a59cd9;
    --vz-input-disabled-bg: #eff2f7;
    --vz-input-group-addon-bg: #eff2f7;
    --vz-input-check-border: var(--vz-input-border)
}

[data-layout-mode=dark] {
    --vz-gray-100: #1a1d21;
    --vz-gray-200: #212529;
    --vz-gray-300: #2a2f34;
    --vz-gray-400: #878a99;
    --vz-gray-500: #adb5bd;
    --vz-gray-600: #bfc8e2;
    --vz-gray-700: #ced4da;
    --vz-gray-800: #eff2f7;
    --vz-gray-900: #f3f6f9;
    --vz-body-bg: #1a1d21;
    --vz-body-color: #ced4da;
    --vz-body-color-rgb: 206, 212, 218;
    --vz-vertical-menu-bg: #fff;
    --vz-vertical-menu-item-color: #5f6270;
    --vz-vertical-menu-item-hover-color: #4b38b3;
    --vz-vertical-menu-item-active-color: #4b38b3;
    --vz-vertical-menu-sub-item-color: #686b7b;
    --vz-vertical-menu-sub-item-hover-color: #4b38b3;
    --vz-vertical-menu-sub-item-active-color: #4b38b3;
    --vz-vertical-menu-title-color: #878a99;
    --vz-vertical-menu-bg-dark: #212529;
    --vz-vertical-menu-item-color-dark: #878a99;
    --vz-vertical-menu-item-hover-color-dark: #fff;
    --vz-vertical-menu-item-active-color-dark: #fff;
    --vz-vertical-menu-sub-item-color-dark: #7c7f90;
    --vz-vertical-menu-sub-item-hover-color-dark: #fff;
    --vz-vertical-menu-sub-item-active-color-dark: #fff;
    --vz-vertical-menu-title-color-dark: #5f6270;
    --vz-header-bg: #262a2f;
    --vz-header-item-color: #e9ecef;
    --vz-header-bg-dark: var(--vz-header-bg);
    --vz-header-item-color-dark: var(--vz-header-item-color);
    --vz-topbar-search-bg: #1a1d21;
    --vz-topbar-user-bg: #2f343a;
    --vz-topbar-user-bg-dark: #2f343a;
    --vz-footer-bg: #23282c;
    --vz-footer-color: #878a99;
    --vz-topnav-bg: #272b30;
    --vz-topnav-item-color: #878a99;
    --vz-topnav-item-color-active: #fff;
    --vz-twocolumn-menu-iconview-bg: #fff;
    --vz-twocolumn-menu-bg: #fff;
    --vz-twocolumn-menu-iconview-bg-dark: #1b1f22;
    --vz-twocolumn-menu-bg-dark: var(--vz-vertical-menu-bg-dark);
    --vz-twocolumn-menu-item-color-dark: var(--vz-vertical-menu-item-color-dark);
    --vz-twocolumn-menu-item-active-color-dark: #fff;
    --vz-twocolumn-menu-item-active-bg-dark: rgba(255, 255, 255, 0.15);
    --vz-boxed-body-bg: #111316;
    --vz-heading-color: #ced4da;
    --vz-box-shadow: 0 3px 5px rgba(29, 35, 40, 0.15);
    --vz-light: #2a2f34;
    --vz-light-rgb: 42, 47, 52;
    --vz-dark: #eff2f7;
    --vz-dark-rgb: 239, 242, 247;
    --vz-link-color: #ced4da;
    --vz-link-hover-color: #ced4da;
    --vz-border-color: #32383e;
    --vz-dropdown-bg: #272b30;
    --vz-dropdown-link-color: #eff2f7;
    --vz-dropdown-link-hover-color: #d7dade;
    --vz-dropdown-link-hover-bg: #2f343a;
    --vz-dropdown-border-width: 1px;
    --vz-card-bg: #212529;
    --vz-card-cap-bg: #212529;
    --vz-card-logo-dark: none;
    --vz-card-logo-light: block;
    --vz-modal-bg: #212529;
    --vz-nav-tabs-link-active-color: #f3f6f9;
    --vz-nav-tabs-link-active-bg: #2a2f34;
    --vz-accordion-button-active-color: #fff;
    --vz-progress-bg: #2a2f34;
    --vz-toast-background-color: rgba(42, 47, 52, 0.85);
    --vz-toast-border-color: rgba(255, 255, 255, 0.1);
    --vz-toast-header-border-color: rgba(255, 255, 255, 0.05);
    --vz-list-group-hover-bg: #272b30;
    --vz-popover-bg: #23282c;
    --vz-pagination-hover-bg: #2a2f34;
    --vz-input-bg: #262a2f;
    --vz-input-border: #2a2f34;
    --vz-input-focus-border: #33393f;
    --vz-input-disabled-bg: #212529;
    --vz-input-group-addon-bg: #2a2f34;
    --vz-input-check-border: #33393f
}

[data-sidebar=gradient] {
    --vz-vertical-menu-bg-gradient: linear-gradient(to right, var(--vz-primary), var(--vz-success));
    --vz-vertical-menu-border-gradient: var(--vz-success);
    --vz-twocolumn-menu-bg-dark: var(--vz-success)
}

[data-sidebar=gradient-2] {
    --vz-vertical-menu-bg-gradient: linear-gradient(to right, var(--vz-info), var(--vz-secondary));
    --vz-vertical-menu-border-gradient: var(--vz-secondary);
    --vz-twocolumn-menu-bg-dark: var(--vz-secondary)
}

[data-sidebar=gradient-3] {
    --vz-vertical-menu-bg-gradient: linear-gradient(to right, var(--vz-info), var(--vz-success));
    --vz-vertical-menu-border-gradient: var(--vz-success);
    --vz-twocolumn-menu-bg-dark: var(--vz-success)
}

[data-sidebar=gradient-4] {
    --vz-vertical-menu-bg-gradient: linear-gradient(to right, var(--vz-dark), var(--vz-primary));
    --vz-vertical-menu-border-gradient: var(--vz-primary);
    --vz-twocolumn-menu-bg-dark: var(--vz-primary)
}

#page-topbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1002;
    background-color: var(--vz-header-bg);
    -webkit-transition: all .1s ease-out;
    transition: all .1s ease-out
}

#page-topbar.topbar-shadow {
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

@media (min-width:768px) {
    #page-topbar {
        left: 250px
    }
}

.navbar-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: justify;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: 0 auto;
    height: 70px;
    padding: 0 1.5rem 0 calc(1.5rem / 2)
}

@media (max-width:767.98px) {
    .navbar-header {
        padding: 0 calc(1.5rem / 2) 0 calc(1.5rem / 2)
    }
}

.navbar-header .topbar-head-dropdown .dropdown-menu.show {
    top: 13px !important
}

.navbar-header .btn-topbar {
    height: 42px;
    width: 42px
}

@media (max-width:360px) {
    .navbar-header .btn-topbar {
        height: 36px;
        width: 36px
    }
}

.navbar-header .user-name-text {
    color: var(--vz-gray-700)
}

.app-search {
    padding: calc(32px / 2) 0
}

.app-search .form-control {
    border: none;
    height: 38px;
    padding-left: 40px;
    padding-right: 30px;
    background-color: var(--vz-topbar-search-bg);
    -webkit-box-shadow: none;
    box-shadow: none
}

.app-search span.search-widget-icon {
    position: absolute;
    z-index: 10;
    font-size: 18px;
    line-height: 38px;
    left: 13px;
    top: 0;
    color: #878a99
}

.app-search .search-widget-icon-close {
    right: 7px;
    left: auto !important
}

@media (max-width:1023.99px) {
    .app-search {
        padding-left: calc(1.5rem / 2)
    }
}

.megamenu-list li {
    position: relative;
    padding: 5px 0
}

.megamenu-list li a {
    color: var(--vz-body-color)
}

@media (max-width:767.98px) {
    .logo span.logo-lg {
        display: none
    }

    .logo span.logo-sm {
        display: inline-block
    }
}

.header-item {
    height: 70px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.header-profile-user {
    height: 32px;
    width: 32px
}

.topbar-badge-sm {
    right: 0;
    top: 7px !important
}

.topbar-badge {
    right: -9px;
    top: 4px !important
}

@media (min-width:768px) {
    .topbar-user {
        background-color: var(--vz-topbar-user-bg)
    }
}

.topbar-user .dropdown-menu {
    top: 6px !important
}

.notification-item {
    padding: .75rem 1rem;
    white-space: inherit;
    position: relative
}

.notification-item .form-check-input {
    position: relative;
    z-index: 2
}

.dropdown-icon-item {
    display: block;
    border-radius: 3px;
    line-height: 34px;
    text-align: center;
    padding: 15px 0 9px;
    border: 1px solid transparent;
    color: var(--vz-dropdown-link-color)
}

.dropdown-icon-item img {
    height: 24px
}

.dropdown-icon-item span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.dropdown-icon-item:hover {
    background-color: var(--vz-dropdown-link-hover-bg)
}

.fullscreen-enable [data-toggle=fullscreen] .bx-fullscreen::before {
    content: "\eacb"
}

[data-layout-mode=dark] .light-dark-mode .bx-moon::before {
    content: "\ec34"
}

[data-topbar=dark] #page-topbar {
    background-color: var(--vz-header-bg-dark);
    border-color: var(--vz-header-bg-dark)
}

[data-topbar=dark] .navbar-header .btn-topbar {
    color: var(--vz-header-item-color-dark)
}

[data-topbar=dark] .navbar-header .btn-topbar:focus,
[data-topbar=dark] .navbar-header .btn-topbar:hover {
    background-color: rgba(255, 255, 255, .07);
    color: #fff
}

@media (min-width:767.99px) {
    [data-topbar=dark] .topbar-user {
        background-color: var(--vz-topbar-user-bg-dark)
    }
}

[data-topbar=dark] .topbar-user .user-name-text {
    color: rgba(255, 255, 255, .85) !important
}

[data-topbar=dark] .topbar-user .user-name-sub-text {
    color: var(--vz-header-item-color-dark) !important
}

[data-topbar=dark] .logo-dark {
    display: none
}

[data-topbar=dark] .logo-light {
    display: inline-block
}

[data-topbar=dark] .app-search .form-control {
    background-color: rgba(255, 255, 255, .05);
    color: #fff
}

[data-topbar=dark] .app-search input.form-control::-webkit-input-placeholder,
[data-topbar=dark] .app-search span.search-widget-icon {
    color: rgba(255, 255, 255, .5)
}

[data-topbar=dark] .hamburger-icon span {
    background-color: #e9ebec
}

@media (max-width:600px) {
    .navbar-header .dropdown {
        position: static
    }

    .navbar-header .dropdown .dropdown-menu {
        width: 100%
    }
}

@media (max-width:767.98px) {
    #search-dropdown-reponsive {
        top: 54px !important
    }
}

@media (min-width:1024.1px) {
    [data-layout=vertical][data-layout-style=detached] #page-topbar {
        left: 0 !important;
        -webkit-box-shadow: var(--vz-box-shadow);
        box-shadow: var(--vz-box-shadow)
    }

    [data-layout=vertical][data-layout-style=detached] .horizontal-logo {
        display: inline-block;
        padding-left: 0
    }

    [data-layout=vertical][data-layout-style=detached] .topnav-hamburger {
        visibility: hidden
    }

    [data-layout=vertical][data-layout-style=detached] .layout-width {
        max-width: 95%;
        margin: 0 auto
    }

    [data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm-hover] .navbar-brand-box,
    [data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] .navbar-brand-box {
        background-color: transparent !important;
        position: relative;
        width: auto;
        text-align: left
    }

    [data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm-hover] .navbar-brand-box .logo-sm,
    [data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] .navbar-brand-box .logo-sm {
        display: none
    }

    [data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm-hover] .navbar-brand-box .logo-lg,
    [data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] .navbar-brand-box .logo-lg {
        display: block
    }
}

[data-layout=vertical][data-layout-style=detached][data-topbar=dark] .horizontal-logo .logo-dark {
    display: none
}

[data-layout=vertical][data-layout-style=detached][data-topbar=dark] .horizontal-logo .logo-light {
    display: block
}

[data-layout=horizontal] #page-topbar {
    left: 0;
    border-bottom: 1px solid var(--vz-gray-300)
}

@media (min-width:1024.1px) {
    [data-layout=horizontal] #page-topbar.topbar-shadow {
        -webkit-box-shadow: none;
        box-shadow: none
    }
}

[data-layout=horizontal] .page-content {
    padding: calc(45px + 1.5rem) calc(1.5rem / 2) 60px calc(1.5rem / 2)
}

@media (min-width:1024.1px) {
    [data-layout=horizontal] .page-content {
        margin-top: 70px
    }
}

@media (min-width:1024.1px) {
    [data-layout=horizontal][data-layout-width=boxed] .page-content {
        min-height: calc(100vh - 130px)
    }
}

@media (min-width:768px) {

    [data-layout=vertical][data-sidebar-size=sm-hover] #page-topbar,
    [data-layout=vertical][data-sidebar-size=sm] #page-topbar {
        left: 70px
    }
}

@media (min-width:768px) {
    [data-layout=vertical][data-sidebar-size=md] #page-topbar {
        left: 180px
    }
}

@media (min-width:768px) {
    [data-layout=twocolumn] #page-topbar {
        left: calc(70px + 220px)
    }
}

[data-layout=twocolumn] .horizontal-logo {
    display: none
}

.page-title-box {
    padding: 10px 1.5rem;
    background-color: var(--vz-card-bg);
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow);
    border-bottom: 1px solid none;
    border-top: 1px solid none;
    margin: -23px -1.5rem 1.5rem -1.5rem
}

.page-title-box .breadcrumb {
    background-color: transparent;
    padding: 0
}

.page-title-box h4 {
    font-weight: 700;
    font-size: 15px !important;
    text-transform: uppercase
}

[data-layout=horizontal] .page-title-box {
    padding: 1.2rem 0;
    background-color: transparent !important;
    border-bottom: none;
    border-top: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 0
}

@media (min-width:1024.1px) {
    [data-layout=horizontal] .page-title-box {
        margin: -19px 0 0 0
    }
}

[data-layout=vertical][data-layout-style=detached] .page-title-box {
    padding: 1.2rem 0;
    background-color: transparent !important;
    border-bottom: none;
    border-top: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 0
}

@media (min-width:1024.1px) {
    [data-layout=vertical][data-layout-style=detached] .page-title-box {
        margin: -19px 0 0 0
    }
}

.footer {
    bottom: 0;
    padding: 20px calc(1.5rem / 2);
    position: absolute;
    right: 0;
    color: var(--vz-footer-color);
    left: 250px;
    height: 60px;
    background-color: var(--vz-footer-bg)
}

@media (max-width:991.98px) {
    .footer {
        left: 0
    }
}

[data-layout=vertical][data-sidebar-size=sm-hover] .footer,
[data-layout=vertical][data-sidebar-size=sm] .footer {
    left: 70px
}

@media (max-width:767.98px) {

    [data-layout=vertical][data-sidebar-size=sm-hover] .footer,
    [data-layout=vertical][data-sidebar-size=sm] .footer {
        left: 0
    }
}

[data-layout=vertical][data-sidebar-size=md] .footer {
    left: 180px
}

@media (max-width:991.98px) {
    [data-layout=vertical][data-sidebar-size=md] .footer {
        left: 0
    }
}

[data-layout=horizontal] .footer {
    left: 0 !important
}

@media (min-width:1024.1px) {
    [data-layout=vertical][data-layout-style=detached] .footer {
        left: 0 !important;
        background-color: transparent
    }
}

@media (min-width:768.1px) {
    [data-layout=twocolumn] .footer {
        left: calc(70px + 220px)
    }
}

.app-content {
    margin-left: 250px;
    overflow: hidden
}

.app-content .content {
    padding: 0 15px 10px 15px;
    margin-top: 70px
}

.main-content {
    -webkit-transition: all .1s ease-out;
    transition: all .1s ease-out
}

@media (min-width:768px) {
    .main-content {
        margin-left: 250px
    }
}

.page-content {
    padding: calc(70px + 1.5rem) calc(1.5rem / 2) 60px calc(1.5rem / 2)
}

.navbar-menu {
    width: 250px;
    z-index: 1002;
    background: var(--vz-vertical-menu-bg);
    border-right: 1px solid #fff;
    bottom: 0;
    margin-top: 0;
    position: fixed;
    top: 0;
    -webkit-box-shadow: 0 2px 4px rgba(15, 34, 58, .12);
    box-shadow: 0 2px 4px rgba(15, 34, 58, .12);
    padding: 0 0 calc(70px + 25px) 0;
    -webkit-transition: all .1s ease-out;
    transition: all .1s ease-out
}

.navbar-menu .navbar-nav .nav-link {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: .625rem 1.5rem;
    color: var(--vz-vertical-menu-item-color);
    font-size: .925rem;
    font-family: Inter, sans-serif
}

.navbar-menu .navbar-nav .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color)
}

.navbar-menu .navbar-nav .nav-link:hover {
    color: var(--vz-vertical-menu-item-hover-color)
}

.navbar-menu .navbar-nav .nav-link i {
    display: inline-block;
    min-width: 1.75rem;
    font-size: 18px;
    line-height: inherit
}

.navbar-menu .navbar-nav .nav-link svg {
    width: 18px;
    margin-right: .665rem;
    color: var(--vz-vertical-menu-item-color)
}

.navbar-menu .navbar-nav .nav-link .badge {
    margin-left: auto;
    margin-right: -2px;
    z-index: 1
}

.navbar-menu .navbar-nav .nav-link:hover {
    color: var(--vz-vertical-menu-item-hover-color)
}

.navbar-menu .navbar-nav .nav-link:hover .icon-dual {
    color: var(--vz-vertical-menu-item-hover-color);
    fill: rgba(75, 56, 179, .16)
}

.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse]:after {
    display: block;
    content: "\f0142";
    font-family: "Material Design Icons";
    margin-left: auto;
    -webkit-transition: -webkit-transform .2s;
    transition: -webkit-transform .2s;
    transition: transform .2s;
    transition: transform .2s, -webkit-transform .2s;
    font-size: 1.05rem;
    position: absolute;
    right: 18px;
    color: var(--vz-vertical-menu-title-color)
}

.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
    color: var(--vz-vertical-menu-item-active-color)
}

.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual {
    color: var(--vz-vertical-menu-item-hover-color);
    fill: rgba(75, 56, 179, .16)
}

.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    color: var(--vz-vertical-menu-item-active-color)
}

.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:before {
    opacity: 1;
    background-color: var(--vz-vertical-menu-item-active-color)
}

.navbar-menu .navbar-nav .nav-link.collapsed.active::after {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.navbar-menu .navbar-nav .nav-sm {
    padding-left: 1.75rem
}

.navbar-menu .navbar-nav .nav-sm .nav-link {
    padding: .55rem 1.5rem !important;
    color: var(--vz-vertical-menu-sub-item-color);
    white-space: none;
    position: relative;
    font-size: .8125rem;
    font-family: Inter, sans-serif
}

.navbar-menu .navbar-nav .nav-sm .nav-link:before {
    content: "";
    width: 6px;
    height: 1.5px;
    background-color: var(--vz-vertical-menu-sub-item-color);
    position: absolute;
    left: 2px;
    top: 16.5px;
    -webkit-transition: all .4s ease-in-out;
    transition: all .4s ease-in-out;
    opacity: .5
}

.navbar-menu .navbar-nav .nav-sm .nav-link:hover {
    color: var(--vz-vertical-menu-sub-item-hover-color)
}

.navbar-menu .navbar-nav .nav-sm .nav-link:hover:before {
    background-color: var(--vz-vertical-menu-sub-item-hover-color) !important;
    opacity: 1
}

.navbar-menu .navbar-nav .nav-sm .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color)
}

.navbar-menu .navbar-nav .nav-sm .nav-link.active:before {
    background-color: var(--vz-vertical-menu-item-active-color)
}

.navbar-menu .navbar-nav .nav-sm .nav-sm {
    padding-left: 15px
}

.navbar-menu .navbar-nav .nav-sm .nav-sm .nav-link:before {
    height: 5px;
    width: 5px;
    left: 5px;
    border-radius: 50%;
    background-color: transparent;
    border: 1px solid;
    top: 16px
}

.navbar-menu .btn-vertical-sm-hover {
    color: #878a99;
    display: none
}

.navbar-brand-box {
    padding: 0 1.3rem;
    text-align: center;
    -webkit-transition: all .1s ease-out;
    transition: all .1s ease-out
}

@media (max-width:767.98px) {
    .navbar-brand-box {
        display: none
    }
}

.hamburger-icon {
    width: 20px;
    height: 14px;
    position: relative;
    cursor: pointer;
    display: inline-block
}

.hamburger-icon span {
    background-color: #878a99;
    position: absolute;
    border-radius: 2px;
    -webkit-transition: .3s cubic-bezier(.8, .5, .2, 1.4);
    transition: .3s cubic-bezier(.8, .5, .2, 1.4);
    width: 100%;
    height: 2px;
    display: block;
    left: 0
}

.hamburger-icon span:nth-child(1) {
    top: 0;
    width: 80%
}

.hamburger-icon span:nth-child(2) {
    top: 6px
}

.hamburger-icon span:nth-child(3) {
    bottom: 0;
    width: 60%
}

.vertical-menu-btn:hover .hamburger-icon:not(.open) span:nth-child(1) {
    top: -1px
}

.vertical-menu-btn:hover .hamburger-icon:not(.open) span:nth-child(3) {
    bottom: -1px
}

.hamburger-icon.open {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg)
}

.hamburger-icon.open span:nth-child(1) {
    left: 1px;
    top: 5px;
    width: 20px;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transition-delay: 150ms;
    transition-delay: 150ms
}

.hamburger-icon.open span:nth-child(2) {
    left: 3px;
    top: 13px;
    width: 10px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transition-delay: 50ms;
    transition-delay: 50ms
}

.hamburger-icon.open span:nth-child(3) {
    left: 9px;
    top: 13px;
    width: 10px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transition-delay: .1s;
    transition-delay: .1s
}

.logo {
    line-height: 70px
}

.logo .logo-sm {
    display: none
}

.logo-light {
    display: none
}

[data-layout=vertical] .app-menu .row {
    margin: 0
}

[data-layout=vertical] .app-menu .row>* {
    width: 100%;
    padding: 0
}

@media (max-width:767.98px) {
    [data-layout=vertical] .app-menu {
        margin-left: -100%;
        padding: 10px 0 20px 0
    }
}

[data-layout=vertical] .navbar-menu .container-fluid {
    padding: 0
}

@media (max-width:767.98px) {
    [data-layout=vertical] .navbar-brand-box {
        display: none
    }
}

[data-layout=vertical] .horizontal-logo {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .main-content {
    margin-left: 70px
}

@media (max-width:767.98px) {
    [data-layout=vertical][data-sidebar-size=sm-hover] .main-content {
        margin-left: 0
    }
}

[data-layout=vertical][data-sidebar-size=sm-hover] .logo span.logo-lg {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .logo span.logo-sm {
    display: inline-block
}

[data-layout=vertical][data-sidebar-size=sm-hover] .btn-vertical-sm-hover {
    display: inline-block
}

@media (min-width:768px) {
    [data-layout=vertical][data-sidebar-size=sm-hover] .topnav-hamburger {
        display: none
    }
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu {
    width: 70px
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .btn-vertical-sm-hover {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .btn-vertical-sm-hover i.ri-record-circle-line:before {
    content: "\eb7d"
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .badge {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .menu-title {
    text-align: center
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .menu-title span {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .menu-title i {
    display: block;
    line-height: 36px;
    font-size: 1rem
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link span {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link i {
    font-size: 22px
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link i.lab,
[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link i.lar,
[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link i.las {
    font-size: 24px
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link svg {
    margin-right: 0
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link:after,
[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link:before {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .menu-dropdown {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover {
    width: 250px !important
}

@media (min-width:1024.99px) {
    [data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .btn-vertical-sm-hover {
        display: inline-block
    }

    [data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .navbar-brand-box {
        text-align: left
    }
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .navbar-nav .menu-dropdown.show {
    display: block
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link i {
    font-size: 18px
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link i.lab,
[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link i.lar,
[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link i.las {
    font-size: 20px
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link svg {
    margin-right: .665rem
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link span,
[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link:after,
[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link:before {
    display: inline-block
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .logo span.logo-lg {
    display: inline-block
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .logo span.logo-sm {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .menu-title {
    text-align: left
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .menu-title span {
    display: inline-block
}

[data-layout=vertical][data-sidebar-size=sm-hover] .navbar-menu:hover .menu-title i {
    display: none
}

@media (min-width:1025px) {

    [data-layout=vertical][data-sidebar-size=sm-hover-active] .navbar-header,
    [data-layout=vertical][data-sidebar-size=sm-hover] .navbar-header {
        padding-left: 1.5rem
    }
}

[data-layout=vertical][data-sidebar-size=sm-hover-active] .navbar-brand-box {
    text-align: left
}

[data-layout=vertical][data-sidebar-size=sm-hover-active] .topnav-hamburger {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm-hover-active] .btn-vertical-sm-hover {
    display: inline-block
}

@media (min-width:768px) {
    [data-layout=vertical][data-sidebar-size=sm] {
        min-height: 1400px
    }

    [data-layout=vertical][data-sidebar-size=sm] .main-content {
        margin-left: 70px
    }
}

[data-layout=vertical][data-sidebar-size=sm] #page-topbar {
    z-index: calc(1002 + 1)
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-brand-box {
    position: fixed;
    padding: 0;
    width: 70px;
    z-index: 1;
    top: 0;
    background: var(--vz-vertical-menu-bg)
}

[data-layout=vertical][data-sidebar-size=sm] .logo span.logo-lg {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm] .logo span.logo-sm {
    display: inline-block
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu {
    position: absolute;
    width: 70px !important;
    padding-top: 70px
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .simplebar-content-wrapper,
[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .simplebar-mask {
    overflow: visible !important
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .simplebar-scrollbar,
[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .vertical-menu-btn {
    display: none !important
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .simplebar-offset {
    bottom: 0 !important
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .badge {
    display: none !important
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .menu-title {
    text-align: center;
    font-size: 1rem
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .menu-title span {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .menu-title i {
    display: block;
    line-height: 36px
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link span {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link i {
    font-size: 22px
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link svg {
    margin-right: 0
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link:after,
[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link:before {
    display: none
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .menu-dropdown {
    display: none;
    height: auto !important
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item {
    position: relative
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>a.menu-link {
    position: relative;
    width: calc(200px + 70px);
    color: #fff;
    background-color: var(--vz-vertical-menu-bg-dark);
    -webkit-transition: none;
    transition: none
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>a.menu-link .icon-dual {
    color: var(--vz-vertical-menu-item-hover-color-dark);
    fill: rgba(255, 255, 255, .16)
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>a.menu-link span {
    display: inline-block;
    padding-left: 25px
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>a.menu-link:after {
    display: block;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    color: #fff
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>.menu-dropdown {
    display: block;
    left: 70px;
    position: absolute;
    width: 200px;
    background: var(--vz-vertical-menu-bg);
    height: auto !important;
    padding: .5rem 0;
    border-radius: 0 0 3px 3px;
    -webkit-box-shadow: 0 2px 4px rgba(15, 34, 58, .12);
    box-shadow: 0 2px 4px rgba(15, 34, 58, .12)
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm {
    padding: 0
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link {
    color: var(--vz-vertical-menu-item-hover-color)
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link:after {
    color: inherit
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-link:after {
    display: block !important;
    -webkit-transform: rotate(0) !important;
    transform: rotate(0) !important
}

[data-layout=vertical][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .menu-dropdown {
    left: 100% !important;
    top: 0;
    border-radius: 3px !important
}

@media (min-width:768px) {
    [data-layout=vertical][data-sidebar-size=md] .main-content {
        margin-left: 180px
    }
}

[data-layout=vertical][data-sidebar-size=md] .navbar-brand-box {
    width: 180px
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu {
    width: 180px !important
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link {
    display: block;
    text-align: center;
    padding: .55rem .525rem
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link i {
    display: block
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link svg {
    display: block;
    margin-left: auto;
    margin-right: auto
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link:before {
    display: none !important
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse]:after {
    position: relative;
    display: inline-block;
    right: 0;
    top: 3px
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link.menu-link[data-bs-toggle=collapse]:after {
    display: none
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .navbar-nav .badge {
    display: none !important
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .navbar-nav .nav-sm {
    padding-left: 0
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .menu-title {
    text-align: center
}

[data-layout=vertical][data-sidebar-size=md] .navbar-menu .menu-title span {
    text-decoration: underline
}

[data-layout=vertical][data-sidebar=dark] .navbar-menu {
    background: var(--vz-vertical-menu-bg-dark);
    border-right: 1px solid var(--vz-vertical-menu-bg-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-menu .hamburger-icon span {
    background-color: var(--vz-vertical-menu-item-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-menu .btn-vertical-sm-hover {
    color: var(--vz-vertical-menu-item-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link {
    color: var(--vz-vertical-menu-item-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link[data-bs-toggle=collapse]:after {
    color: inherit
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after {
    color: inherit
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual {
    color: var(--vz-vertical-menu-item-active-color-dark);
    fill: rgba(255, 255, 255, .1)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link svg {
    color: var(--vz-vertical-menu-item-color-dark);
    fill: rgba(255, 255, 255, .1)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link:hover {
    color: var(--vz-vertical-menu-item-hover-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-link:hover .icon-dual {
    color: var(--vz-vertical-menu-item-hover-color-dark);
    fill: rgba(255, 255, 255, .16)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav>.nav-item .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav>.nav-item .nav-link.active .icon-dual {
    color: var(--vz-vertical-menu-item-hover-color-dark);
    fill: rgba(255, 255, 255, .16)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-link {
    color: var(--vz-vertical-menu-sub-item-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-link:before {
    background-color: var(--vz-vertical-menu-sub-item-color-dark) !important
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-link:hover {
    color: var(--vz-vertical-menu-item-hover-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-link:hover:before {
    background-color: var(--vz-vertical-menu-item-hover-color-dark) !important
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-sm .nav-link:before {
    background-color: transparent !important
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-sm .nav-link:hover:before {
    background-color: var(--vz-vertical-menu-item-hover-color-dark) !important
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-sm .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .navbar-nav .nav-sm .nav-sm .nav-link.active:before {
    background-color: var(--vz-vertical-menu-item-active-color-dark) !important
}

[data-layout=vertical][data-sidebar=dark][data-sidebar-size=sm] .navbar-brand-box {
    background: var(--vz-vertical-menu-bg-dark)
}

[data-layout=vertical][data-sidebar=dark][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>.menu-dropdown {
    background: var(--vz-vertical-menu-bg-dark)
}

[data-layout=vertical][data-sidebar=dark][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm {
    padding: 0
}

[data-layout=vertical][data-sidebar=dark][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-link:after {
    display: block !important;
    -webkit-transform: rotate(0) !important;
    transform: rotate(0) !important
}

[data-layout=vertical][data-sidebar=dark][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-layout=vertical][data-sidebar=dark][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link:after {
    color: inherit
}

[data-layout=vertical][data-sidebar=dark][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .menu-dropdown {
    left: 100% !important;
    top: 0;
    border-radius: 3px !important
}

[data-layout=vertical][data-sidebar=dark] .menu-title {
    color: var(--vz-vertical-menu-title-color-dark)
}

[data-layout=vertical][data-sidebar=dark] .logo-dark {
    display: none
}

[data-layout=vertical][data-sidebar=dark] .logo-light {
    display: inline-block
}

[data-layout=vertical][data-sidebar=light] .logo-dark {
    display: inline-block
}

[data-layout=vertical][data-sidebar=light] .logo-light {
    display: none
}

[data-layout=vertical][data-layout-style=detached] #layout-wrapper,
[data-layout=vertical][data-layout-style=detached] .main-content {
    min-height: 100vh
}

@media (min-width:1024.1px) {
    [data-layout=vertical][data-layout-style=detached] .main-content {
        position: relative
    }

    [data-layout=vertical][data-layout-style=detached] #layout-wrapper {
        max-width: 95%;
        margin: 0 auto;
        padding-left: 1.5rem
    }

    [data-layout=vertical][data-layout-style=detached] .navbar-header {
        padding-left: 1.5rem
    }

    [data-layout=vertical][data-layout-style=detached] .navbar-menu {
        top: calc(70px + 1.5rem);
        bottom: 1.5rem;
        padding: 0;
        border-right: var(--vz-vertical-menu-bg);
        border-radius: 5px;
        padding: 10px 0;
        z-index: 1
    }

    [data-layout=vertical][data-layout-style=detached] .navbar-menu .navbar-brand-box {
        display: none
    }

    [data-layout=vertical][data-layout-style=detached][data-sidebar=dark] .logo-dark,
    [data-layout=vertical][data-layout-style=detached][data-sidebar=gradient-2] .logo-dark,
    [data-layout=vertical][data-layout-style=detached][data-sidebar=gradient-3] .logo-dark,
    [data-layout=vertical][data-layout-style=detached][data-sidebar=gradient-4] .logo-dark,
    [data-layout=vertical][data-layout-style=detached][data-sidebar=gradient] .logo-dark {
        display: inline-block
    }

    [data-layout=vertical][data-layout-style=detached][data-sidebar=dark] .logo-light,
    [data-layout=vertical][data-layout-style=detached][data-sidebar=gradient-2] .logo-light,
    [data-layout=vertical][data-layout-style=detached][data-sidebar=gradient-3] .logo-light,
    [data-layout=vertical][data-layout-style=detached][data-sidebar=gradient-4] .logo-light,
    [data-layout=vertical][data-layout-style=detached][data-sidebar=gradient] .logo-light {
        display: none
    }
}

[data-layout=vertical][data-layout-style=detached] .footer {
    border-top: 1px dashed var(--vz-border-color)
}

[data-layout=vertical][data-layout-style=detached] .auth-page-wrapper .footer {
    border-top: none
}

@media (min-width:768px) {

    [data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] #layout-wrapper,
    [data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] .main-content {
        min-height: 1400px
    }
}

.menu-title {
    letter-spacing: .05em;
    cursor: default;
    font-size: 11px;
    text-transform: uppercase;
    color: var(--vz-vertical-menu-title-color);
    font-weight: 600
}

.menu-title span {
    padding: 12px 20px;
    display: inline-block
}

.menu-title i {
    display: none
}

.vertical-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(33, 37, 41, .35);
    z-index: 1003;
    display: none
}

.vertical-sidebar-enable .vertical-overlay {
    display: block
}

.vertical-sidebar-enable .app-menu {
    margin-left: 0 !important;
    z-index: 1004
}

[dir=rtl] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse]:after {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

[dir=rtl] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after {
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg)
}

[data-sidebar=gradient-2] .navbar-menu,
[data-sidebar=gradient-3] .navbar-menu,
[data-sidebar=gradient-4] .navbar-menu,
[data-sidebar=gradient] .navbar-menu {
    background: var(--vz-vertical-menu-bg-gradient);
    border-right: 1px solid var(--vz-vertical-menu-border-gradient)
}

[data-sidebar=gradient-2] .navbar-menu .hamburger-icon span,
[data-sidebar=gradient-3] .navbar-menu .hamburger-icon span,
[data-sidebar=gradient-4] .navbar-menu .hamburger-icon span,
[data-sidebar=gradient] .navbar-menu .hamburger-icon span {
    background-color: rgba(255, 255, 255, .5)
}

[data-sidebar=gradient-2] .navbar-menu .btn-vertical-sm-hover,
[data-sidebar=gradient-3] .navbar-menu .btn-vertical-sm-hover,
[data-sidebar=gradient-4] .navbar-menu .btn-vertical-sm-hover,
[data-sidebar=gradient] .navbar-menu .btn-vertical-sm-hover {
    color: rgba(255, 255, 255, .5)
}

[data-sidebar=gradient-2] .navbar-nav .nav-link,
[data-sidebar=gradient-3] .navbar-nav .nav-link,
[data-sidebar=gradient-4] .navbar-nav .nav-link,
[data-sidebar=gradient] .navbar-nav .nav-link {
    color: rgba(255, 255, 255, .5)
}

[data-sidebar=gradient-2] .navbar-nav .nav-link.active,
[data-sidebar=gradient-3] .navbar-nav .nav-link.active,
[data-sidebar=gradient-4] .navbar-nav .nav-link.active,
[data-sidebar=gradient] .navbar-nav .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-sidebar=gradient-2] .navbar-nav .nav-link[data-bs-toggle=collapse]:after,
[data-sidebar=gradient-3] .navbar-nav .nav-link[data-bs-toggle=collapse]:after,
[data-sidebar=gradient-4] .navbar-nav .nav-link[data-bs-toggle=collapse]:after,
[data-sidebar=gradient] .navbar-nav .nav-link[data-bs-toggle=collapse]:after {
    color: inherit
}

[data-sidebar=gradient-2] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true],
[data-sidebar=gradient-3] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true],
[data-sidebar=gradient-4] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true],
[data-sidebar=gradient] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-sidebar=gradient-2] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after,
[data-sidebar=gradient-3] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after,
[data-sidebar=gradient-4] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after,
[data-sidebar=gradient] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after {
    color: inherit
}

[data-sidebar=gradient-2] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual,
[data-sidebar=gradient-3] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual,
[data-sidebar=gradient-4] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual,
[data-sidebar=gradient] .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual {
    color: var(--vz-vertical-menu-item-active-color-dark);
    fill: rgba(255, 255, 255, .1)
}

[data-sidebar=gradient-2] .navbar-nav .nav-link svg,
[data-sidebar=gradient-3] .navbar-nav .nav-link svg,
[data-sidebar=gradient-4] .navbar-nav .nav-link svg,
[data-sidebar=gradient] .navbar-nav .nav-link svg {
    color: rgba(255, 255, 255, .5);
    fill: rgba(255, 255, 255, .1)
}

[data-sidebar=gradient-2] .navbar-nav .nav-link:hover,
[data-sidebar=gradient-3] .navbar-nav .nav-link:hover,
[data-sidebar=gradient-4] .navbar-nav .nav-link:hover,
[data-sidebar=gradient] .navbar-nav .nav-link:hover {
    color: var(--vz-vertical-menu-item-hover-color-dark)
}

[data-sidebar=gradient-2] .navbar-nav .nav-link:hover .icon-dual,
[data-sidebar=gradient-3] .navbar-nav .nav-link:hover .icon-dual,
[data-sidebar=gradient-4] .navbar-nav .nav-link:hover .icon-dual,
[data-sidebar=gradient] .navbar-nav .nav-link:hover .icon-dual {
    color: var(--vz-vertical-menu-item-hover-color-dark);
    fill: rgba(255, 255, 255, .16)
}

[data-sidebar=gradient-2] .navbar-nav>.nav-item .nav-link.active,
[data-sidebar=gradient-3] .navbar-nav>.nav-item .nav-link.active,
[data-sidebar=gradient-4] .navbar-nav>.nav-item .nav-link.active,
[data-sidebar=gradient] .navbar-nav>.nav-item .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-sidebar=gradient-2] .navbar-nav>.nav-item .nav-link.active .icon-dual,
[data-sidebar=gradient-3] .navbar-nav>.nav-item .nav-link.active .icon-dual,
[data-sidebar=gradient-4] .navbar-nav>.nav-item .nav-link.active .icon-dual,
[data-sidebar=gradient] .navbar-nav>.nav-item .nav-link.active .icon-dual {
    color: var(--vz-vertical-menu-item-hover-color-dark);
    fill: rgba(255, 255, 255, .16)
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-link,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-link,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-link,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-link {
    color: rgba(255, 255, 255, .5)
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-link:before,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-link:before,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-link:before,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-link:before {
    background-color: rgba(255, 255, 255, .5) !important
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-link:hover,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-link:hover,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-link:hover,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-link:hover {
    color: var(--vz-vertical-menu-item-hover-color-dark)
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-link:hover:before,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-link:hover:before,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-link:hover:before,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-link:hover:before {
    background-color: var(--vz-vertical-menu-item-hover-color-dark) !important
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-link.active,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-link.active,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-link.active,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-sm .nav-link:before,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-sm .nav-link:before,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-sm .nav-link:before,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-sm .nav-link:before {
    background-color: transparent !important
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-sm .nav-link:hover:before,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-sm .nav-link:hover:before,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-sm .nav-link:hover:before,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-sm .nav-link:hover:before {
    background-color: var(--vz-vertical-menu-item-hover-color-dark) !important
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-sm .nav-link.active,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-sm .nav-link.active,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-sm .nav-link.active,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-sm .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-sidebar=gradient-2] .navbar-nav .nav-sm .nav-sm .nav-link.active:before,
[data-sidebar=gradient-3] .navbar-nav .nav-sm .nav-sm .nav-link.active:before,
[data-sidebar=gradient-4] .navbar-nav .nav-sm .nav-sm .nav-link.active:before,
[data-sidebar=gradient] .navbar-nav .nav-sm .nav-sm .nav-link.active:before {
    background-color: var(--vz-vertical-menu-item-active-color-dark) !important
}

[data-sidebar=gradient-2][data-sidebar-size=sm] .navbar-brand-box,
[data-sidebar=gradient-3][data-sidebar-size=sm] .navbar-brand-box,
[data-sidebar=gradient-4][data-sidebar-size=sm] .navbar-brand-box,
[data-sidebar=gradient][data-sidebar-size=sm] .navbar-brand-box {
    background: var(--vz-vertical-menu-bg-gradient)
}

[data-sidebar=gradient-2][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>.menu-dropdown,
[data-sidebar=gradient-3][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>.menu-dropdown,
[data-sidebar=gradient-4][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>.menu-dropdown,
[data-sidebar=gradient][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover>.menu-dropdown {
    background: var(--vz-vertical-menu-bg-dark)
}

[data-sidebar=gradient-2][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm,
[data-sidebar=gradient-3][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm,
[data-sidebar=gradient-4][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm,
[data-sidebar=gradient][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm {
    padding: 0
}

[data-sidebar=gradient-2][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-link:after,
[data-sidebar=gradient-3][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-link:after,
[data-sidebar=gradient-4][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-link:after,
[data-sidebar=gradient][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-link:after {
    display: block !important;
    -webkit-transform: rotate(0) !important;
    transform: rotate(0) !important
}

[data-sidebar=gradient-2][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link,
[data-sidebar=gradient-3][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link,
[data-sidebar=gradient-4][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link,
[data-sidebar=gradient][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link {
    color: var(--vz-vertical-menu-item-active-color-dark)
}

[data-sidebar=gradient-2][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link:after,
[data-sidebar=gradient-3][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link:after,
[data-sidebar=gradient-4][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link:after,
[data-sidebar=gradient][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover>.nav-link:after {
    color: inherit
}

[data-sidebar=gradient-2][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .menu-dropdown,
[data-sidebar=gradient-3][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .menu-dropdown,
[data-sidebar=gradient-4][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .menu-dropdown,
[data-sidebar=gradient][data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .menu-dropdown {
    left: 100% !important;
    top: 0;
    border-radius: 3px !important
}

[data-sidebar=gradient-2][data-sidebar-size=sm][data-layout=vertical] .navbar-menu .navbar-nav .nav-item:hover>a.menu-link,
[data-sidebar=gradient-3][data-sidebar-size=sm][data-layout=vertical] .navbar-menu .navbar-nav .nav-item:hover>a.menu-link,
[data-sidebar=gradient-4][data-sidebar-size=sm][data-layout=vertical] .navbar-menu .navbar-nav .nav-item:hover>a.menu-link,
[data-sidebar=gradient][data-sidebar-size=sm][data-layout=vertical] .navbar-menu .navbar-nav .nav-item:hover>a.menu-link {
    background: var(--vz-vertical-menu-bg-gradient)
}

[data-sidebar=gradient-2] .menu-title,
[data-sidebar=gradient-3] .menu-title,
[data-sidebar=gradient-4] .menu-title,
[data-sidebar=gradient] .menu-title {
    color: rgba(255, 255, 255, .5)
}

[data-sidebar=gradient-2] .logo-dark,
[data-sidebar=gradient-3] .logo-dark,
[data-sidebar=gradient-4] .logo-dark,
[data-sidebar=gradient] .logo-dark {
    display: none
}

[data-sidebar=gradient-2] .logo-light,
[data-sidebar=gradient-3] .logo-light,
[data-sidebar=gradient-4] .logo-light,
[data-sidebar=gradient] .logo-light {
    display: inline-block
}

.bg-vertical-gradient {
    background: -webkit-gradient(linear, left top, right top, from(var(--vz-primary)), to(var(--vz-success)));
    background: linear-gradient(to right, var(--vz-primary), var(--vz-success))
}

.bg-vertical-gradient-2 {
    background: -webkit-gradient(linear, left top, right top, from(var(--vz-info)), to(var(--vz-secondary)));
    background: linear-gradient(to right, var(--vz-info), var(--vz-secondary))
}

.bg-vertical-gradient-3 {
    background: -webkit-gradient(linear, left top, right top, from(var(--vz-info)), to(var(--vz-success)));
    background: linear-gradient(to right, var(--vz-info), var(--vz-success))
}

.bg-vertical-gradient-4 {
    background: -webkit-gradient(linear, left top, right top, from(var(--vz-dark)), to(var(--vz-primary)));
    background: linear-gradient(to right, var(--vz-dark), var(--vz-primary))
}

.sidebar-background {
    position: absolute;
    z-index: -1;
    height: 100%;
    width: 100%;
    display: block;
    top: 0;
    left: 0;
    background-size: cover;
    background-position: 50%;
    opacity: .07
}

[data-sidebar-image=img-1] .sidebar-background {
    background-image: url(../images/sidebar/img-1.jpg)
}

[data-sidebar-image=img-2] .sidebar-background {
    background-image: url(../images/sidebar/img-2.jpg)
}

[data-sidebar-image=img-3] .sidebar-background {
    background-image: url(../images/sidebar/img-3.jpg)
}

[data-sidebar-image=img-4] .sidebar-background {
    background-image: url(../images/sidebar/img-4.jpg)
}

[data-layout=horizontal] .main-content {
    margin-left: 0
}

@media (min-width:1024.1px) {

    [data-layout=horizontal] .container-fluid,
    [data-layout=horizontal] .layout-width {
        max-width: 90%;
        margin: 0 auto
    }

    [data-layout=horizontal] .topnav-hamburger {
        visibility: hidden
    }
}

[data-layout=horizontal] .horizontal-logo {
    padding-left: calc(1.5rem / 2)
}

@media (max-width:1024.98px) {
    [data-layout=horizontal] .horizontal-logo {
        padding-left: 1.5rem
    }
}

[data-layout=horizontal] .navbar-menu {
    background: var(--vz-topnav-bg);
    padding: 0 calc(1.5rem / 2);
    -webkit-box-shadow: 0 2px 4px rgba(15, 34, 58, .12);
    box-shadow: 0 2px 4px rgba(15, 34, 58, .12);
    margin-top: 70px;
    position: fixed;
    left: 0;
    right: 0;
    z-index: 100;
    width: 100%;
    bottom: auto
}

@media (max-width:575.98px) {
    [data-layout=horizontal] .navbar-menu .container-fluid {
        padding: 0
    }
}

[data-layout=horizontal] .navbar-menu .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-sm {
    padding-left: 0
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-sm .nav-link:before {
    opacity: 0 !important
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-sm .nav-link.active,
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-sm .nav-link:hover {
    color: var(--vz-topnav-item-color-active)
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link {
    color: var(--vz-topnav-item-color);
    padding: .75rem 1.5rem
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link i {
    line-height: 1
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link.active {
    color: var(--vz-topnav-item-color-active)
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link.active:after {
    color: var(--vz-topnav-item-color-active)
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link.active .icon-dual {
    color: var(--vz-topnav-item-color-active);
    fill: rgba(75, 56, 179, .1)
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
    color: var(--vz-topnav-item-color-active)
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual {
    color: var(--vz-topnav-item-color-active);
    fill: rgba(75, 56, 179, .1)
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after {
    color: var(--vz-topnav-item-color-active)
}

[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link:hover .icon-dual {
    color: var(--vz-topnav-item-color-active);
    fill: rgba(75, 56, 179, .1)
}

[data-layout=horizontal] .navbar-menu .navbar-nav>.nav-item>.nav-link[data-bs-toggle=collapse]:after {
    right: 0;
    -webkit-transform: rotate(90deg) !important;
    transform: rotate(90deg) !important
}

[data-layout=horizontal] .navbar-menu .navbar-nav>li:nth-of-type(2)>.nav-link.menu-link {
    padding-left: 0
}

[data-layout=horizontal] .navbar-menu .navbar-brand-box {
    display: none
}

[data-layout=horizontal] .navbar-nav .nav-item {
    position: relative
}

[data-layout=horizontal] .navbar-nav .nav-item .nav-link[data-bs-toggle=collapse]:after {
    right: 10px;
    -webkit-transform: rotate(0) !important;
    transform: rotate(0) !important
}

[data-layout=horizontal] .navbar-nav .nav-item>.nav-link>.badge {
    display: none
}

[data-layout=horizontal] .navbar-nav .nav-item:hover>.nav-link {
    color: var(--vz-topnav-item-color-active)
}

[data-layout=horizontal] .navbar-nav .nav-item:hover>.nav-link .icon-dual {
    color: var(--vz-topnav-item-color-active);
    fill: rgba(75, 56, 179, .1)
}

[data-layout=horizontal] .navbar-nav .nav-item:hover>.nav-link:after {
    color: var(--vz-topnav-item-color-active)
}

@media (min-width:1024.1px) {
    [data-layout=horizontal] .navbar-nav .nav-item:hover>.menu-dropdown {
        display: block;
        height: auto !important
    }
}

[data-layout=horizontal] .navbar-nav .nav-item.active {
    color: var(--vz-topnav-item-color-active)
}

[data-layout=horizontal] .menu-dropdown {
    position: absolute;
    min-width: 12rem;
    padding: .5rem 0;
    -webkit-box-shadow: 0 0 5px rgba(15, 34, 58, .15);
    box-shadow: 0 0 5px rgba(15, 34, 58, .15);
    -webkit-animation-name: DropDownSlide;
    animation-name: DropDownSlide;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    margin: 0;
    z-index: 1000;
    background-color: var(--vz-dropdown-bg);
    background-clip: padding-box;
    border: var(--vz-dropdown-border-width) solid var(--vz-border-color);
    border-radius: .3rem;
    display: none
}

[data-layout=horizontal] .menu-dropdown .menu-dropdown {
    top: 0;
    left: 100%
}

[data-layout=horizontal] .mega-dropdown-menu {
    width: 40rem
}

[data-layout=horizontal] .menu-title {
    display: none
}

[data-layout=horizontal] .dropdown-custom-right {
    left: -100% !important;
    right: 100%
}

@media (max-width:1024px) {
    [data-layout=horizontal] .navbar-menu {
        display: none
    }
}

@media (max-width:1024px) {
    [data-layout=horizontal] .menu .navbar-menu {
        display: block;
        max-height: 360px;
        overflow-y: auto;
        padding-left: 0
    }

    [data-layout=horizontal] .menu .navbar-menu .navbar-nav {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column
    }

    [data-layout=horizontal] .menu .navbar-menu .navbar-nav>li:nth-of-type(2)>.nav-link.menu-link {
        padding-left: 1.5rem
    }

    [data-layout=horizontal] .menu .navbar-menu .navbar-nav .nav-sm .nav-link:before {
        opacity: 1 !important
    }

    [data-layout=horizontal] .menu .menu-dropdown {
        position: relative;
        min-width: 100%;
        -webkit-box-shadow: none;
        box-shadow: none;
        padding-left: 28px;
        left: 0;
        -webkit-animation: none;
        animation: none;
        padding-top: 0
    }

    [data-layout=horizontal] .menu .menu-dropdown.show {
        display: block
    }

    [data-layout=horizontal] .menu .dropdown-custom-right {
        left: 0 !important
    }

    [data-layout=horizontal] .menu .nav-item .nav-link[data-bs-toggle=collapse]:after {
        right: 0
    }

    [data-layout=horizontal] .menu .mega-dropdown-menu {
        width: 100%
    }
}

[data-layout-mode=dark][data-topbar=light] .navbar-header .horizontal-logo .logo-dark {
    display: none
}

[data-layout-mode=dark][data-topbar=light] .navbar-header .horizontal-logo .logo-light {
    display: block
}

[dir=rtl][data-layout=horizontal] .navbar-menu .navbar-nav>.nav-item>.nav-link[data-bs-toggle=collapse]:after {
    -webkit-transform: rotate(-90deg) !important;
    transform: rotate(-90deg) !important
}

[dir=rtl][data-layout=horizontal] .navbar-nav .nav-item .nav-link[data-bs-toggle=collapse]:after {
    -webkit-transform: rotate(-180deg) !important;
    transform: rotate(-180deg) !important
}

[data-layout=twocolumn] .app-menu {
    padding-bottom: 0;
    width: 220px;
    left: 70px
}

[data-layout=twocolumn] .app-menu .menu-link {
    letter-spacing: .05em;
    cursor: default;
    font-size: 11px;
    text-transform: uppercase;
    color: var(--vz-vertical-menu-title-color) !important;
    font-weight: 600
}

[data-layout=twocolumn] .app-menu .menu-link:after {
    display: none !important
}

[data-layout=twocolumn] .app-menu .menu-link i {
    display: none
}

[data-layout=twocolumn] .app-menu .navbar-nav {
    height: calc(100vh - 70px)
}

@media (max-width:767.98px) {
    [data-layout=twocolumn] .app-menu .navbar-nav {
        padding-top: 16px
    }
}

[data-layout=twocolumn] .app-menu .navbar-nav>li:not(.twocolumn-item-show) {
    display: none
}

[data-layout=twocolumn] .app-menu .navbar-nav .twocolumn-item-show>div {
    display: block !important;
    height: auto !important
}

[data-layout=twocolumn] .app-menu .navbar-nav>.nav-item>.menu-dropdown {
    display: block !important;
    height: auto !important
}

[data-layout=twocolumn] .app-menu .navbar-nav .nav-item .menu-dropdown .row {
    margin: 0
}

[data-layout=twocolumn] .app-menu .navbar-nav .nav-item .menu-dropdown .row .col-lg-4 {
    width: 100%;
    padding: 0
}

[data-layout=twocolumn] .app-menu .navbar-nav .nav-sm .nav-link {
    color: var(--vz-vertical-menu-item-color)
}

[data-layout=twocolumn] .app-menu .navbar-nav .nav-sm .nav-link.active {
    color: var(--vz-vertical-menu-item-active-color)
}

[data-layout=twocolumn] .app-menu .container-fluid {
    padding: 0
}

[data-layout=twocolumn] .main-content {
    margin-left: calc(220px + 70px)
}

@media (max-width:767.98px) {
    [data-layout=twocolumn] .main-content {
        margin-left: 70px
    }
}

[data-layout=twocolumn] .twocolumn-iconview {
    width: 70px;
    background-color: var(--vz-twocolumn-menu-iconview-bg);
    height: 100%;
    left: -70px;
    -webkit-box-shadow: 0 2px 4px rgba(15, 34, 58, .12);
    box-shadow: 0 2px 4px rgba(15, 34, 58, .12);
    top: 0;
    position: absolute;
    padding: 0;
    text-align: center
}

[data-layout=twocolumn] .twocolumn-iconview li {
    position: relative
}

[data-layout=twocolumn] .twocolumn-iconview li .nav-icon::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

[data-layout=twocolumn] .twocolumn-iconview .nav-icon {
    width: 42px;
    height: 42px;
    line-height: 42px;
    color: var(--vz-vertical-menu-item-color);
    z-index: 1;
    font-size: 22px;
    text-align: center;
    border-radius: 3px;
    margin: 5px 0;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

[data-layout=twocolumn] .twocolumn-iconview .nav-icon .icon-dual {
    width: 18px;
    color: var(--vz-vertical-menu-item-color)
}

[data-layout=twocolumn] .twocolumn-iconview .nav-icon.active {
    background-color: rgba(75, 56, 179, .15);
    color: #4b38b3
}

[data-layout=twocolumn] .twocolumn-iconview .nav-icon.active .icon-dual {
    color: #4b38b3;
    fill: rgba(75, 56, 179, .1)
}

[data-layout=twocolumn] .menu-title {
    display: none
}

@media (max-width:991.98px) {
    [data-layout=twocolumn] .logo span.logo-lg {
        display: block
    }

    [data-layout=twocolumn] .logo span.logo-sm {
        display: none
    }
}

[data-layout=twocolumn][data-sidebar=light] .app-menu .navbar-brand-box .logo-light {
    display: none
}

[data-layout=twocolumn][data-sidebar=light] .app-menu .navbar-brand-box .logo-dark {
    display: block
}

[data-layout=twocolumn][data-sidebar=dark] .twocolumn-iconview {
    background-color: var(--vz-twocolumn-menu-iconview-bg-dark)
}

[data-layout=twocolumn][data-sidebar=dark] .twocolumn-iconview .nav-icon {
    color: var(--vz-twocolumn-menu-item-color-dark)
}

[data-layout=twocolumn][data-sidebar=dark] .twocolumn-iconview .nav-icon.active {
    color: var(--vz-twocolumn-menu-item-active-color-dark);
    background-color: var(--vz-twocolumn-menu-item-active-bg-dark)
}

[data-layout=twocolumn][data-sidebar=dark] .app-menu {
    background-color: var(--vz-twocolumn-menu-bg-dark);
    border-right-color: var(--vz-twocolumn-menu-bg-dark)
}

[data-layout=twocolumn][data-sidebar=dark] .app-menu .navbar-brand-box .logo-light {
    display: block
}

[data-layout=twocolumn][data-sidebar=dark] .app-menu .navbar-brand-box .logo-dark {
    display: none
}

[data-layout=twocolumn][data-sidebar=dark] .app-menu .navbar-nav .nav-sm .nav-link {
    color: var(--vz-twocolumn-menu-item-color-dark)
}

[data-layout=twocolumn][data-sidebar=dark] .navbar-menu .navbar-nav .nav-sm .nav-link.active,
[data-layout=twocolumn][data-sidebar=dark] .navbar-menu .navbar-nav .nav-sm .nav-link:hover {
    color: var(--vz-twocolumn-menu-item-active-color-dark)
}

[data-layout=twocolumn][data-sidebar=dark] .navbar-menu .navbar-nav .nav-sm .nav-link.active::before,
[data-layout=twocolumn][data-sidebar=dark] .navbar-menu .navbar-nav .nav-sm .nav-link:hover::before {
    background-color: var(--vz-twocolumn-menu-item-active-color-dark) !important
}

[data-layout=twocolumn][data-sidebar=dark] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
    color: var(--vz-twocolumn-menu-item-active-color-dark)
}

[data-layout=twocolumn][data-sidebar=dark] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:before {
    background-color: var(--vz-twocolumn-menu-item-active-color-dark)
}

[data-layout=twocolumn][data-sidebar=dark] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]::after {
    color: var(--vz-twocolumn-menu-item-active-color-dark) !important
}

[data-layout=twocolumn][data-sidebar=gradient-2] .twocolumn-iconview,
[data-layout=twocolumn][data-sidebar=gradient-3] .twocolumn-iconview,
[data-layout=twocolumn][data-sidebar=gradient-4] .twocolumn-iconview,
[data-layout=twocolumn][data-sidebar=gradient] .twocolumn-iconview {
    background-color: var(--vz-twocolumn-menu-iconview-bg-dark)
}

[data-layout=twocolumn][data-sidebar=gradient-2] .twocolumn-iconview .nav-icon,
[data-layout=twocolumn][data-sidebar=gradient-3] .twocolumn-iconview .nav-icon,
[data-layout=twocolumn][data-sidebar=gradient-4] .twocolumn-iconview .nav-icon,
[data-layout=twocolumn][data-sidebar=gradient] .twocolumn-iconview .nav-icon {
    color: rgba(255, 255, 255, .6)
}

[data-layout=twocolumn][data-sidebar=gradient-2] .twocolumn-iconview .nav-icon.active,
[data-layout=twocolumn][data-sidebar=gradient-3] .twocolumn-iconview .nav-icon.active,
[data-layout=twocolumn][data-sidebar=gradient-4] .twocolumn-iconview .nav-icon.active,
[data-layout=twocolumn][data-sidebar=gradient] .twocolumn-iconview .nav-icon.active {
    color: var(--vz-twocolumn-menu-item-active-color-dark);
    background-color: var(--vz-twocolumn-menu-item-active-bg-dark)
}

[data-layout=twocolumn][data-sidebar=gradient-2] .app-menu,
[data-layout=twocolumn][data-sidebar=gradient-3] .app-menu,
[data-layout=twocolumn][data-sidebar=gradient-4] .app-menu,
[data-layout=twocolumn][data-sidebar=gradient] .app-menu {
    background-color: var(--vz-twocolumn-menu-bg-dark);
    border-right-color: var(--vz-twocolumn-menu-bg-dark)
}

[data-layout=twocolumn][data-sidebar=gradient-2] .app-menu .navbar-brand-box .logo-light,
[data-layout=twocolumn][data-sidebar=gradient-3] .app-menu .navbar-brand-box .logo-light,
[data-layout=twocolumn][data-sidebar=gradient-4] .app-menu .navbar-brand-box .logo-light,
[data-layout=twocolumn][data-sidebar=gradient] .app-menu .navbar-brand-box .logo-light {
    display: block
}

[data-layout=twocolumn][data-sidebar=gradient-2] .app-menu .navbar-brand-box .logo-dark,
[data-layout=twocolumn][data-sidebar=gradient-3] .app-menu .navbar-brand-box .logo-dark,
[data-layout=twocolumn][data-sidebar=gradient-4] .app-menu .navbar-brand-box .logo-dark,
[data-layout=twocolumn][data-sidebar=gradient] .app-menu .navbar-brand-box .logo-dark {
    display: none
}

[data-layout=twocolumn][data-sidebar=gradient-2] .app-menu .navbar-nav .nav-sm .nav-link,
[data-layout=twocolumn][data-sidebar=gradient-3] .app-menu .navbar-nav .nav-sm .nav-link,
[data-layout=twocolumn][data-sidebar=gradient-4] .app-menu .navbar-nav .nav-sm .nav-link,
[data-layout=twocolumn][data-sidebar=gradient] .app-menu .navbar-nav .nav-sm .nav-link {
    color: rgba(255, 255, 255, .6)
}

[data-layout=twocolumn][data-sidebar=gradient-2] .navbar-menu .navbar-nav .nav-sm .nav-link.active,
[data-layout=twocolumn][data-sidebar=gradient-2] .navbar-menu .navbar-nav .nav-sm .nav-link:hover,
[data-layout=twocolumn][data-sidebar=gradient-3] .navbar-menu .navbar-nav .nav-sm .nav-link.active,
[data-layout=twocolumn][data-sidebar=gradient-3] .navbar-menu .navbar-nav .nav-sm .nav-link:hover,
[data-layout=twocolumn][data-sidebar=gradient-4] .navbar-menu .navbar-nav .nav-sm .nav-link.active,
[data-layout=twocolumn][data-sidebar=gradient-4] .navbar-menu .navbar-nav .nav-sm .nav-link:hover,
[data-layout=twocolumn][data-sidebar=gradient] .navbar-menu .navbar-nav .nav-sm .nav-link.active,
[data-layout=twocolumn][data-sidebar=gradient] .navbar-menu .navbar-nav .nav-sm .nav-link:hover {
    color: var(--vz-twocolumn-menu-item-active-color-dark)
}

[data-layout=twocolumn][data-sidebar=gradient-2] .navbar-menu .navbar-nav .nav-sm .nav-link.active::before,
[data-layout=twocolumn][data-sidebar=gradient-2] .navbar-menu .navbar-nav .nav-sm .nav-link:hover::before,
[data-layout=twocolumn][data-sidebar=gradient-3] .navbar-menu .navbar-nav .nav-sm .nav-link.active::before,
[data-layout=twocolumn][data-sidebar=gradient-3] .navbar-menu .navbar-nav .nav-sm .nav-link:hover::before,
[data-layout=twocolumn][data-sidebar=gradient-4] .navbar-menu .navbar-nav .nav-sm .nav-link.active::before,
[data-layout=twocolumn][data-sidebar=gradient-4] .navbar-menu .navbar-nav .nav-sm .nav-link:hover::before,
[data-layout=twocolumn][data-sidebar=gradient] .navbar-menu .navbar-nav .nav-sm .nav-link.active::before,
[data-layout=twocolumn][data-sidebar=gradient] .navbar-menu .navbar-nav .nav-sm .nav-link:hover::before {
    background-color: var(--vz-twocolumn-menu-item-active-color-dark) !important
}

[data-layout=twocolumn][data-sidebar=gradient-2] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true],
[data-layout=twocolumn][data-sidebar=gradient-3] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true],
[data-layout=twocolumn][data-sidebar=gradient-4] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true],
[data-layout=twocolumn][data-sidebar=gradient] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
    color: var(--vz-twocolumn-menu-item-active-color-dark)
}

[data-layout=twocolumn][data-sidebar=gradient-2] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:before,
[data-layout=twocolumn][data-sidebar=gradient-3] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:before,
[data-layout=twocolumn][data-sidebar=gradient-4] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:before,
[data-layout=twocolumn][data-sidebar=gradient] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:before {
    background-color: var(--vz-twocolumn-menu-item-active-color-dark)
}

[data-layout=twocolumn][data-sidebar=gradient-2] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]::after,
[data-layout=twocolumn][data-sidebar=gradient-3] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]::after,
[data-layout=twocolumn][data-sidebar=gradient-4] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]::after,
[data-layout=twocolumn][data-sidebar=gradient] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]::after {
    color: var(--vz-twocolumn-menu-item-active-color-dark) !important
}

[data-layout=twocolumn] .twocolumn-panel .app-menu {
    width: 0
}

@media (max-width:575.98px) {
    [data-layout=twocolumn] .twocolumn-panel .app-menu {
        display: none
    }
}

[data-layout=twocolumn] .twocolumn-panel .navbar-brand-box,
[data-layout=twocolumn] .twocolumn-panel .navbar-nav {
    display: none
}

[data-layout=twocolumn] .twocolumn-panel .main-content {
    margin-left: 70px
}

[data-layout=twocolumn] .twocolumn-panel #page-topbar,
[data-layout=twocolumn] .twocolumn-panel .footer {
    left: 70px
}

@media (max-width:575.98px) {
    [data-layout=twocolumn] .twocolumn-panel .main-content {
        margin-left: 0
    }

    [data-layout=twocolumn] .twocolumn-panel #page-topbar,
    [data-layout=twocolumn] .twocolumn-panel .footer {
        left: 0
    }
}

@media (max-width:767.98px) {

    [data-layout=twocolumn] #page-topbar,
    [data-layout=twocolumn] .footer {
        left: 70px
    }
}

@media (max-width:575.98px) {
    [data-layout=twocolumn] .main-content {
        margin-left: 0
    }

    [data-layout=twocolumn] #page-topbar,
    [data-layout=twocolumn] .footer {
        left: 0
    }
}

[data-layout-width=boxed] body {
    background-color: var(--vz-boxed-body-bg)
}

[data-layout-width=boxed] #layout-wrapper {
    max-width: 1300px;
    margin: 0 auto;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow);
    background-color: var(--vz-body-bg)
}

[data-layout-width=boxed][data-layout=vertical] #layout-wrapper {
    min-height: 100vh
}

[data-layout-width=boxed] #page-topbar,
[data-layout-width=boxed] .footer {
    max-width: 1300px;
    margin: 0 auto;
    left: 0 !important
}

@media (min-width:768px) {

    [data-layout-width=boxed][data-sidebar-size=sm-hover][data-layout=vertical] #layout-wrapper,
    [data-layout-width=boxed][data-sidebar-size=sm][data-layout=vertical] #layout-wrapper {
        min-height: 1400px
    }
}

@media (max-width:767.98px) {

    [data-layout-width=boxed][data-sidebar-size=sm-hover][data-layout=vertical] .main-content,
    [data-layout-width=boxed][data-sidebar-size=sm][data-layout=vertical] .main-content {
        margin-left: 0
    }
}

[data-layout-width=boxed][data-sidebar-size=sm-hover] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=sm-hover] .footer,
[data-layout-width=boxed][data-sidebar-size=sm] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=sm] .footer {
    left: 0 !important;
    max-width: calc(1300px - 70px)
}

@media (min-width:768px) {

    [data-layout-width=boxed][data-sidebar-size=sm-hover] #page-topbar,
    [data-layout-width=boxed][data-sidebar-size=sm-hover] .footer,
    [data-layout-width=boxed][data-sidebar-size=sm] #page-topbar,
    [data-layout-width=boxed][data-sidebar-size=sm] .footer {
        left: 70px !important
    }
}

[data-layout-width=boxed][data-sidebar-size=lg] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=lg] .footer,
[data-layout-width=boxed][data-sidebar-size=sm-hover-active] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=sm-hover-active] .footer {
    max-width: calc(1300px - 250px)
}

@media (min-width:768px) {

    [data-layout-width=boxed][data-sidebar-size=lg] #page-topbar,
    [data-layout-width=boxed][data-sidebar-size=lg] .footer,
    [data-layout-width=boxed][data-sidebar-size=sm-hover-active] #page-topbar,
    [data-layout-width=boxed][data-sidebar-size=sm-hover-active] .footer {
        left: 250px !important
    }
}

[data-layout-width=boxed][data-sidebar-size=md] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=md] .footer {
    max-width: calc(1300px - 180px)
}

@media (min-width:768px) {

    [data-layout-width=boxed][data-sidebar-size=md] #page-topbar,
    [data-layout-width=boxed][data-sidebar-size=md] .footer {
        left: 180px !important
    }
}

[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached] body {
    background-color: var(--vz-body-bg)
}

@media (min-width:1024.1px) {
    [data-layout-width=boxed][data-layout=vertical][data-layout-style=detached] #layout-wrapper {
        max-width: 1300px;
        -webkit-box-shadow: none;
        box-shadow: none
    }

    [data-layout-width=boxed][data-layout=vertical][data-layout-style=detached] .layout-width {
        max-width: 1300px
    }
}

[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=lg] #page-topbar,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=lg] .footer,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=md] #page-topbar,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=md] .footer,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm-hover] #page-topbar,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm-hover] .footer,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] #page-topbar,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] .footer {
    max-width: 100%;
    left: 0 !important
}

[data-layout=horizontal][data-layout-width=boxed] #layout-wrapper,
[data-layout=horizontal][data-layout-width=boxed] #page-topbar,
[data-layout=horizontal][data-layout-width=boxed] .footer {
    max-width: 100%
}

[data-layout=horizontal][data-layout-width=boxed] .container-fluid,
[data-layout=horizontal][data-layout-width=boxed] .navbar-header {
    max-width: 1300px
}

[data-layout=horizontal][data-layout-width=boxed] .navbar-header {
    padding: 0 calc(1.5rem / 2) 0 0
}

[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=lg] #page-topbar,
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=lg] .footer,
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm-hover] #page-topbar,
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm-hover] .footer,
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm] [data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm-hover-active] #page-topbar,
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm] [data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm-hover-active] .footer {
    left: 0 !important
}

@media (min-width:992px) {

    [data-layout-position=scrollable] #page-topbar,
    [data-layout-position=scrollable] .navbar-menu {
        position: absolute
    }
}

@media (min-width:992px) {

    [data-layout-position=scrollable][data-layout=horizontal] #page-topbar,
    [data-layout-position=scrollable][data-layout=horizontal] .topnav {
        position: absolute
    }
}

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
    position: relative;
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent
}

.waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    opacity: 0;
    background: rgba(0, 0, 0, .2);
    background: radial-gradient(rgba(0, 0, 0, .2) 0, rgba(0, 0, 0, .3) 40%, rgba(0, 0, 0, .4) 50%, rgba(0, 0, 0, .5) 60%, rgba(255, 255, 255, 0) 70%);
    -webkit-transition: all .5s ease-out;
    transition: all .5s ease-out;
    -webkit-transition-property: -webkit-transform, opacity;
    -webkit-transition-property: opacity, -webkit-transform;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
    -webkit-transform: scale(0) translate(0, 0);
    transform: scale(0) translate(0, 0);
    pointer-events: none
}

.waves-effect.waves-light .waves-ripple {
    background: rgba(255, 255, 255, .4);
    background: radial-gradient(rgba(255, 255, 255, .2) 0, rgba(255, 255, 255, .3) 40%, rgba(255, 255, 255, .4) 50%, rgba(255, 255, 255, .5) 60%, rgba(255, 255, 255, 0) 70%)
}

.waves-effect.waves-classic .waves-ripple {
    background: rgba(0, 0, 0, .2)
}

.waves-effect.waves-classic.waves-light .waves-ripple {
    background: rgba(255, 255, 255, .4)
}

.waves-notransition {
    -webkit-transition: none !important;
    transition: none !important
}

.waves-button,
.waves-circle {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-mask-image: -webkit-radial-gradient(circle, #fff 100%, #000 100%)
}

.waves-button,
.waves-button-input,
.waves-button:hover,
.waves-button:visited {
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: none;
    outline: 0;
    color: inherit;
    background-color: rgba(0, 0, 0, 0);
    font-size: 1em;
    line-height: 1em;
    text-align: center;
    text-decoration: none;
    z-index: 1
}

.waves-button {
    padding: .85em 1.1em;
    border-radius: .2em
}

.waves-button-input {
    margin: 0;
    padding: .85em 1.1em
}

.waves-input-wrapper {
    border-radius: .2em;
    vertical-align: bottom
}

.waves-input-wrapper.waves-button {
    padding: 0
}

.waves-input-wrapper .waves-button-input {
    position: relative;
    top: 0;
    left: 0;
    z-index: 1
}

.waves-circle {
    text-align: center;
    width: 2.5em;
    height: 2.5em;
    line-height: 2.5em;
    border-radius: 50%
}

.waves-float {
    -webkit-mask-image: none;
    -webkit-box-shadow: 0 1px 1.5px 1px rgba(0, 0, 0, .12);
    box-shadow: 0 1px 1.5px 1px rgba(0, 0, 0, .12);
    -webkit-transition: all .3s;
    transition: all .3s
}

.waves-float:active {
    -webkit-box-shadow: 0 8px 20px 1px rgba(0, 0, 0, .3);
    box-shadow: 0 8px 20px 1px rgba(0, 0, 0, .3)
}

.waves-block {
    display: block
}

.waves-effect.waves-light .waves-ripple {
    background-color: rgba(255, 255, 255, .4)
}

.waves-effect.waves-primary .waves-ripple {
    background-color: rgba(75, 56, 179, .4)
}

.waves-effect.waves-success .waves-ripple {
    background-color: rgba(69, 203, 133, .4)
}

.waves-effect.waves-info .waves-ripple {
    background-color: rgba(41, 156, 219, .4)
}

.waves-effect.waves-warning .waves-ripple {
    background-color: rgba(255, 190, 11, .4)
}

.waves-effect.waves-danger .waves-ripple {
    background-color: rgba(240, 101, 72, .4)
}

.avatar-xxs {
    height: 1.5rem;
    width: 1.5rem
}

.avatar-xs {
    height: 2rem;
    width: 2rem
}

.avatar-sm {
    height: 3rem;
    width: 3rem
}

.avatar-md {
    height: 4.5rem;
    width: 4.5rem
}

.avatar-lg {
    height: 6rem;
    width: 6rem
}

.avatar-xl {
    height: 7.5rem;
    width: 7.5rem
}

.avatar-title {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #4b38b3;
    color: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-weight: 500;
    height: 100%;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%
}

.avatar-group {
    padding-left: 12px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.avatar-group .avatar-group-item {
    margin-left: -12px;
    border: 2px solid var(--vz-card-bg);
    border-radius: 50%;
    -webkit-transition: all .2s;
    transition: all .2s
}

.avatar-group .avatar-group-item:hover {
    position: relative;
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
    z-index: 1
}

.accordion .accordion-button {
    font-weight: 500
}

.accordion .accordion-body {
    color: #878a99
}

.accordion.accordion-icon-none .accordion-button::after {
    content: "";
    background-image: none !important
}

.accordion.accordion-icon-none .accordion-button:not(.collapsed)::after {
    content: ""
}

.custom-accordionwithicon .accordion-button::after {
    background-image: none !important;
    font-family: "Material Design Icons";
    content: "\f0142";
    font-size: 1.1rem;
    vertical-align: middle;
    line-height: .8
}

.custom-accordionwithicon .accordion-button:not(.collapsed)::after {
    background-image: none !important;
    content: "\f0140";
    margin-right: -3px
}

.custom-accordionwithicon-plus .accordion-button::after {
    background-image: none !important;
    font-family: "Material Design Icons";
    content: "\f0415";
    font-size: 1.1rem;
    vertical-align: middle;
    line-height: .8
}

.custom-accordionwithicon-plus .accordion-button:not(.collapsed)::after {
    background-image: none !important;
    content: "\f0374";
    margin-right: -3px
}

.lefticon-accordion .accordion-button {
    padding-left: 2.75rem
}

.lefticon-accordion .accordion-button::after {
    position: absolute;
    left: 1.25rem;
    top: 14px
}

.lefticon-accordion .accordion-button:not(.collapsed)::after {
    top: 20px
}

.accordion-border-box .accordion-item {
    border-top: 1px solid var(--vz-border-color);
    border-radius: .25rem
}

.accordion-border-box .accordion-item:not(:first-of-type) {
    margin-top: 8px
}

.accordion-border-box .accordion-item .accordion-button {
    border-radius: .25rem
}

.accordion-border-box .accordion-item .accordion-button:not(.collapsed) {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.custom-accordion-border .accordion-item {
    border-left: 3px solid var(--vz-border-color)
}

.accordion-primary .accordion-item {
    border-color: rgba(75, 56, 179, .6)
}

.accordion-primary .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-primary .accordion-item .accordion-button:not(.collapsed) {
    color: #4b38b3;
    background-color: rgba(75, 56, 179, .1) !important
}

.accordion-primary .accordion-item .accordion-button::after {
    color: #4b38b3
}

.accordion-fill-primary .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-fill-primary .accordion-item .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #4b38b3 !important
}

.accordion-secondary .accordion-item {
    border-color: rgba(53, 119, 241, .6)
}

.accordion-secondary .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-secondary .accordion-item .accordion-button:not(.collapsed) {
    color: #3577f1;
    background-color: rgba(53, 119, 241, .1) !important
}

.accordion-secondary .accordion-item .accordion-button::after {
    color: #3577f1
}

.accordion-fill-secondary .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-fill-secondary .accordion-item .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #3577f1 !important
}

.accordion-success .accordion-item {
    border-color: rgba(69, 203, 133, .6)
}

.accordion-success .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-success .accordion-item .accordion-button:not(.collapsed) {
    color: #45cb85;
    background-color: rgba(69, 203, 133, .1) !important
}

.accordion-success .accordion-item .accordion-button::after {
    color: #45cb85
}

.accordion-fill-success .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-fill-success .accordion-item .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #45cb85 !important
}

.accordion-info .accordion-item {
    border-color: rgba(41, 156, 219, .6)
}

.accordion-info .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-info .accordion-item .accordion-button:not(.collapsed) {
    color: #299cdb;
    background-color: rgba(41, 156, 219, .1) !important
}

.accordion-info .accordion-item .accordion-button::after {
    color: #299cdb
}

.accordion-fill-info .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-fill-info .accordion-item .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #299cdb !important
}

.accordion-warning .accordion-item {
    border-color: rgba(255, 190, 11, .6)
}

.accordion-warning .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-warning .accordion-item .accordion-button:not(.collapsed) {
    color: #ffbe0b;
    background-color: rgba(255, 190, 11, .1) !important
}

.accordion-warning .accordion-item .accordion-button::after {
    color: #ffbe0b
}

.accordion-fill-warning .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-fill-warning .accordion-item .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #ffbe0b !important
}

.accordion-danger .accordion-item {
    border-color: rgba(240, 101, 72, .6)
}

.accordion-danger .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-danger .accordion-item .accordion-button:not(.collapsed) {
    color: #f06548;
    background-color: rgba(240, 101, 72, .1) !important
}

.accordion-danger .accordion-item .accordion-button::after {
    color: #f06548
}

.accordion-fill-danger .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-fill-danger .accordion-item .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #f06548 !important
}

.accordion-light .accordion-item {
    border-color: rgba(243, 246, 249, .6)
}

.accordion-light .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-light .accordion-item .accordion-button:not(.collapsed) {
    color: #f3f6f9;
    background-color: rgba(243, 246, 249, .1) !important
}

.accordion-light .accordion-item .accordion-button::after {
    color: #f3f6f9
}

.accordion-fill-light .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-fill-light .accordion-item .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #f3f6f9 !important
}

.accordion-dark .accordion-item {
    border-color: rgba(33, 37, 41, .6)
}

.accordion-dark .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-dark .accordion-item .accordion-button:not(.collapsed) {
    color: #212529;
    background-color: rgba(33, 37, 41, .1) !important
}

.accordion-dark .accordion-item .accordion-button::after {
    color: #212529
}

.accordion-fill-dark .accordion-item .accordion-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.accordion-fill-dark .accordion-item .accordion-button:not(.collapsed) {
    color: #fff;
    background-color: #212529 !important
}

[data-layout-mode=dark] .accordion-button:not(.collapsed) {
    color: var(--vz-accordion-button-active-color)
}

[data-layout-mode=dark] .accordion-button:not(.collapsed):after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e")
}

[data-layout-mode=dark] .accordion-button:after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23adb5bd'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e")
}

[dir=rtl] .custom-accordionwithicon .accordion-button::after {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.fs-10 {
    font-size: 10px !important
}

.fs-11 {
    font-size: 11px !important
}

.fs-12 {
    font-size: 12px !important
}

.fs-13 {
    font-size: 13px !important
}

.fs-14 {
    font-size: 14px !important
}

.fs-15 {
    font-size: 15px !important
}

.fs-16 {
    font-size: 16px !important
}

.fs-17 {
    font-size: 17px !important
}

.fs-18 {
    font-size: 18px !important
}

.fs-19 {
    font-size: 19px !important
}

.fs-20 {
    font-size: 20px !important
}

.fs-21 {
    font-size: 21px !important
}

.fs-22 {
    font-size: 22px !important
}

.fs-23 {
    font-size: 23px !important
}

.fs-24 {
    font-size: 24px !important
}

.fs-36 {
    font-size: 36px !important
}

.fs-48 {
    font-size: 48px !important
}

.border-dark {
    border-color: var(--vz-dark) !important
}

.border-light {
    border-color: var(--vz-light) !important
}

.border-double {
    border-style: double !important
}

.border-top-double {
    border-top-style: double !important
}

.border-bottom-double {
    border-bottom-style: double !important
}

.border-end-double {
    border-right-style: double !important
}

.border-start-double {
    border-left-style: double !important
}

.list-group-flush.border-double {
    border: none !important
}

.list-group-flush.border-double .list-group-item {
    border-style: double !important
}

.border-dashed {
    border-style: dashed !important
}

.border-top-dashed {
    border-top-style: dashed !important
}

.border-bottom-dashed {
    border-bottom-style: dashed !important
}

.border-end-dashed {
    border-right-style: dashed !important
}

.border-start-dashed {
    border-left-style: dashed !important
}

.list-group-flush.border-dashed {
    border: none !important
}

.list-group-flush.border-dashed .list-group-item {
    border-style: dashed !important
}

.border-groove {
    border-style: groove !important
}

.border-top-groove {
    border-top-style: groove !important
}

.border-bottom-groove {
    border-bottom-style: groove !important
}

.border-end-groove {
    border-right-style: groove !important
}

.border-start-groove {
    border-left-style: groove !important
}

.list-group-flush.border-groove {
    border: none !important
}

.list-group-flush.border-groove .list-group-item {
    border-style: groove !important
}

.border-outset {
    border-style: outset !important
}

.border-top-outset {
    border-top-style: outset !important
}

.border-bottom-outset {
    border-bottom-style: outset !important
}

.border-end-outset {
    border-right-style: outset !important
}

.border-start-outset {
    border-left-style: outset !important
}

.list-group-flush.border-outset {
    border: none !important
}

.list-group-flush.border-outset .list-group-item {
    border-style: outset !important
}

.border-ridge {
    border-style: ridge !important
}

.border-top-ridge {
    border-top-style: ridge !important
}

.border-bottom-ridge {
    border-bottom-style: ridge !important
}

.border-end-ridge {
    border-right-style: ridge !important
}

.border-start-ridge {
    border-left-style: ridge !important
}

.list-group-flush.border-ridge {
    border: none !important
}

.list-group-flush.border-ridge .list-group-item {
    border-style: ridge !important
}

.border-dotted {
    border-style: dotted !important
}

.border-top-dotted {
    border-top-style: dotted !important
}

.border-bottom-dotted {
    border-bottom-style: dotted !important
}

.border-end-dotted {
    border-right-style: dotted !important
}

.border-start-dotted {
    border-left-style: dotted !important
}

.list-group-flush.border-dotted {
    border: none !important
}

.list-group-flush.border-dotted .list-group-item {
    border-style: dotted !important
}

.border-inset {
    border-style: inset !important
}

.border-top-inset {
    border-top-style: inset !important
}

.border-bottom-inset {
    border-bottom-style: inset !important
}

.border-end-inset {
    border-right-style: inset !important
}

.border-start-inset {
    border-left-style: inset !important
}

.list-group-flush.border-inset {
    border: none !important
}

.list-group-flush.border-inset .list-group-item {
    border-style: inset !important
}

[data-layout-mode=dark] .link-dark,
[data-layout-mode=dark] .link-light {
    color: var(--vz-dark)
}

[data-layout-mode=dark] .link-dark:focus,
[data-layout-mode=dark] .link-dark:hover,
[data-layout-mode=dark] .link-light:focus,
[data-layout-mode=dark] .link-light:hover {
    color: rgba(var(--vz-dark-rgb), .75)
}

.ff-base {
    font-family: var(--vz-font-sans-serif)
}

.ff-secondary {
    font-family: Inter, sans-serif
}

.fw-medium {
    font-weight: 500
}

.fw-semibold {
    font-weight: 600 !important
}

.flex-1 {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.social-list-item {
    height: 2rem;
    width: 2rem;
    line-height: calc(2rem - 4px);
    display: block;
    border: 2px solid #adb5bd;
    border-radius: 50%;
    color: #adb5bd;
    text-align: center;
    -webkit-transition: all .4s;
    transition: all .4s
}

.social-list-item:hover {
    color: #878a99;
    background-color: #eff2f7
}

.bg-pattern {
    /* background: url(../images/modal-bg.png) var(--vz-modal-bg) */
}

.w-xs {
    min-width: 80px
}

.w-sm {
    min-width: 95px
}

.w-md {
    min-width: 110px
}

.w-lg {
    min-width: 140px
}

.w-xl {
    min-width: 160px
}

.icon-xs {
    height: 16px;
    width: 16px
}

.icon-sm {
    height: 18px;
    width: 18px
}

.icon-md {
    height: 22px;
    width: 22px
}

.icon-lg {
    height: 24px;
    width: 24px
}

.icon-xl {
    height: 28px;
    width: 28px
}

.icon-xxl {
    height: 32px;
    width: 32px
}

.icon-dual {
    color: #adb5bd;
    fill: rgba(173, 181, 189, .16)
}

.icon-dual-primary {
    color: #4b38b3;
    fill: rgba(75, 56, 179, .16)
}

.icon-dual-secondary {
    color: #3577f1;
    fill: rgba(53, 119, 241, .16)
}

.icon-dual-success {
    color: #45cb85;
    fill: rgba(69, 203, 133, .16)
}

.icon-dual-info {
    color: #299cdb;
    fill: rgba(41, 156, 219, .16)
}

.icon-dual-warning {
    color: #ffbe0b;
    fill: rgba(255, 190, 11, .16)
}

.icon-dual-danger {
    color: #f06548;
    fill: rgba(240, 101, 72, .16)
}

.icon-dual-light {
    color: #f3f6f9;
    fill: rgba(243, 246, 249, .16)
}

.icon-dual-dark {
    color: #212529;
    fill: rgba(33, 37, 41, .16)
}

.search-box {
    position: relative
}

.search-box .form-control {
    padding-left: 40px
}

.search-box .search-icon {
    font-size: 14px;
    position: absolute;
    left: 13px;
    top: 0;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #878a99
}

.bg-overlay {
    position: absolute;
    height: 100%;
    width: 100%;
    right: 0;
    bottom: 0;
    left: 0;
    top: 0;
    opacity: .7;
    background-color: #000
}

.customizer-setting {
    position: fixed;
    bottom: 40px;
    right: 20px;
    z-index: 1000
}

code {
    -webkit-user-select: all;
    -moz-user-select: all;
    user-select: all
}

.layout-rightside {
    width: 280px;
    margin-right: -1.5rem;
    margin-top: calc(1px - 1.5rem);
    height: calc(100% + 1.5rem)
}

@media (max-width:1699.98px) {
    .layout-rightside-col {
        display: none;
        position: fixed !important;
        height: 100vh;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: 1004
    }

    .layout-rightside-col .overlay {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background-color: rgba(33, 37, 41, .2)
    }

    .layout-rightside-col .layout-rightside {
        margin-top: 0;
        height: 100%;
        margin-left: auto
    }

    .layout-rightside-col .card-body {
        overflow-y: auto;
        padding-bottom: 1rem !important
    }
}

@media (min-width:1700px) {

    [data-layout-style=detached] .layout-rightside,
    [data-layout=horizontal] .layout-rightside {
        margin-top: calc(28px - 1.5rem);
        margin-right: 0;
        height: calc(100% - (1.5rem / 5))
    }
}

.object-cover {
    -o-object-fit: cover;
    object-fit: cover
}

.text-truncate-two-lines {
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    white-space: normal
}

.favourite-btn .ri-star-fill {
    color: #878a99
}

.favourite-btn.active .ri-star-fill {
    color: #ffbe0b
}

.favourite-btn.active .ri-star-fill:before {
    content: "\f186"
}

.card-logo-light {
    display: var(--vz-card-logo-light)
}

.card-logo-dark {
    display: var(--vz-card-logo-dark)
}

[data-layout-mode=dark] .btn-close {
    -webkit-filter: invert(1) grayscale(100%) brightness(200%);
    filter: invert(1) grayscale(100%) brightness(200%)
}

#back-to-top {
    position: fixed;
    bottom: 100px;
    right: 28px;
    -webkit-transition: all .5s ease;
    transition: all .5s ease;
    display: none;
    z-index: 1000
}

#back-to-top:hover {
    -webkit-animation: fade-up 1.5s infinite linear;
    animation: fade-up 1.5s infinite linear
}

@-webkit-keyframes fade-up {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }

    75% {
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
        opacity: 0
    }
}

@keyframes fade-up {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1
    }

    75% {
        -webkit-transform: translateY(-20px);
        transform: translateY(-20px);
        opacity: 0
    }
}

.cursor-pointer {
    cursor: pointer
}

#preloader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--vz-card-bg);
    z-index: 9999
}

#status {
    width: 40px;
    height: 40px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -20px 0 0 -20px
}

.spinner-chase {
    margin: 0 auto;
    width: 40px;
    height: 40px;
    position: relative;
    -webkit-animation: spinner-chase 2.5s infinite linear both;
    animation: spinner-chase 2.5s infinite linear both
}

.chase-dot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    -webkit-animation: chase-dot 2s infinite ease-in-out both;
    animation: chase-dot 2s infinite ease-in-out both
}

.chase-dot:before {
    content: "";
    display: block;
    width: 25%;
    height: 25%;
    background-color: #4b38b3;
    border-radius: 100%;
    -webkit-animation: chase-dot-before 2s infinite ease-in-out both;
    animation: chase-dot-before 2s infinite ease-in-out both
}

.chase-dot:nth-child(1) {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s
}

.chase-dot:nth-child(1):before {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s
}

.chase-dot:nth-child(2) {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

.chase-dot:nth-child(2):before {
    -webkit-animation-delay: -1s;
    animation-delay: -1s
}

.chase-dot:nth-child(3) {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}

.chase-dot:nth-child(3):before {
    -webkit-animation-delay: -.9s;
    animation-delay: -.9s
}

.chase-dot:nth-child(4) {
    -webkit-animation-delay: -.8s;
    animation-delay: -.8s
}

.chase-dot:nth-child(4):before {
    -webkit-animation-delay: -.8s;
    animation-delay: -.8s
}

.chase-dot:nth-child(5) {
    -webkit-animation-delay: -.7s;
    animation-delay: -.7s
}

.chase-dot:nth-child(5):before {
    -webkit-animation-delay: -.7s;
    animation-delay: -.7s
}

.chase-dot:nth-child(6) {
    -webkit-animation-delay: -.6s;
    animation-delay: -.6s
}

.chase-dot:nth-child(6):before {
    -webkit-animation-delay: -.6s;
    animation-delay: -.6s
}

@-webkit-keyframes spinner-chase {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes spinner-chase {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-webkit-keyframes chase-dot {

    100%,
    80% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes chase-dot {

    100%,
    80% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@-webkit-keyframes chase-dot-before {
    50% {
        -webkit-transform: scale(.4);
        transform: scale(.4)
    }

    0%,
    100% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes chase-dot-before {
    50% {
        -webkit-transform: scale(.4);
        transform: scale(.4)
    }

    0%,
    100% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

[type=email]::-webkit-input-placeholder,
[type=number]::-webkit-input-placeholder,
[type=tel]::-webkit-input-placeholder,
[type=url]::-webkit-input-placeholder {
    text-align: left
}

[type=email]::-moz-placeholder,
[type=number]::-moz-placeholder,
[type=tel]::-moz-placeholder,
[type=url]::-moz-placeholder {
    text-align: left
}

[type=email]:-ms-input-placeholder,
[type=number]:-ms-input-placeholder,
[type=tel]:-ms-input-placeholder,
[type=url]:-ms-input-placeholder {
    text-align: left
}

[type=email]::-ms-input-placeholder,
[type=number]::-ms-input-placeholder,
[type=tel]::-ms-input-placeholder,
[type=url]::-ms-input-placeholder {
    text-align: left
}

[type=email]::placeholder,
[type=number]::placeholder,
[type=tel]::placeholder,
[type=url]::placeholder {
    text-align: left
}

.main-chart .chart-border-left {
    border-left: 1.4px solid var(--vz-border-color);
    padding: 2px 20px
}

.main-chart .chart-border-left:last-child {
    margin-right: 0
}

.activity-feed {
    list-style: none
}

.activity-feed .feed-item {
    position: relative;
    padding-bottom: 27px;
    padding-left: 16px;
    border-left: 2px solid #f3f6f9
}

.activity-feed .feed-item:after {
    content: "";
    display: block;
    position: absolute;
    top: 4px;
    left: -6px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #4b38b3;
    background-color: var(--vz-card-bg)
}

.activity-feed .feed-item:last-child {
    border-color: transparent
}

.mini-stats-wid {
    position: relative
}

.mini-stats-wid .mini-stat-icon {
    overflow: hidden;
    position: relative
}

.mini-stats-wid .mini-stat-icon:after,
.mini-stats-wid .mini-stat-icon:before {
    content: "";
    position: absolute;
    width: 8px;
    height: 69px;
    background-color: rgba(69, 203, 133, .1);
    left: 3px;
    -webkit-transform: rotate(32deg);
    transform: rotate(32deg);
    top: -8px;
    -webkit-transition: all .4s;
    transition: all .4s
}

.mini-stats-wid .mini-stat-icon::after {
    left: 27px;
    width: 8px;
    -webkit-transition: all .2s;
    transition: all .2s
}

.mini-stats-wid:hover .mini-stat-icon::after {
    left: 60px
}

.mini-stats-wid:hover .mini-stat-icon::before {
    left: 50px
}

.button-items {
    margin-left: -8px;
    margin-bottom: -12px
}

.button-items .btn {
    margin-bottom: 12px;
    margin-left: 8px
}

.bs-example-modal {
    position: relative;
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
    z-index: 1;
    display: block
}

[dir=rtl] .modal-open {
    padding-left: 0 !important
}

.icon-demo-content {
    color: var(--vz-gray-500)
}

.icon-demo-content i {
    font-size: 24px;
    margin-right: 10px;
    color: var(--vz-gray-600);
    -webkit-transition: all .4s;
    transition: all .4s;
    vertical-align: middle
}

.icon-demo-content svg {
    margin-right: 10px;
    -webkit-transition: all .4s;
    transition: all .4s;
    height: 20px
}

.icon-demo-content .col-lg-4 {
    margin-top: 24px
}

.icon-demo-content .col-lg-4:hover i,
.icon-demo-content .col-lg-4:hover svg {
    color: #4b38b3;
    -webkit-transform: scale(1.5);
    transform: scale(1.5)
}

.grid-structure .grid-container {
    background-color: #f3f6f9;
    margin-top: 10px;
    font-size: .8rem;
    font-weight: 500;
    padding: 10px 20px
}

.img-switch .card-radio .form-check-input {
    display: none
}

.img-switch .card-radio .form-check-input:checked+.form-check-label::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(75, 56, 179, .5)
}

.img-switch .card-radio .form-check-input:checked+.form-check-label::after {
    content: "\eb80";
    font-family: remixicon;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translateY(-50%) translateX(-50%);
    transform: translateY(-50%) translateX(-50%);
    font-size: 18px;
    color: #fff
}

[data-bs-target="#collapseBgGradient"].active {
    border-color: #4b38b3 !important
}

[data-bs-target="#collapseBgGradient"].active::before {
    content: "\eb80";
    font-family: remixicon;
    position: absolute;
    top: 2px;
    right: 6px;
    font-size: 16px;
    color: #4b38b3
}

[data-layout-mode=dark] .colorscheme-cardradio .form-check-label {
    background-color: var(--vz-dark)
}

[data-layout-mode=dark] .colorscheme-cardradio .bg-light {
    background-color: rgba(var(--vz-light-rgb), .1) !important
}

[data-layout-mode=dark] .colorscheme-cardradio .bg-soft-light {
    background-color: rgba(var(--vz-dark-rgb), .1) !important
}

[data-layout-mode=dark] .colorscheme-cardradio .dark .bg-dark {
    background-color: var(--vz-card-bg) !important
}

@media print {

    #back-to-top,
    .app-menu,
    .footer,
    .navbar-header,
    .page-title-box,
    .right-bar,
    .vertical-menu {
        display: none !important
    }

    .card-body,
    .main-content,
    .page-content,
    .right-bar,
    body {
        padding: 0;
        margin: 0
    }

    .card {
        border: 0;
        -webkit-box-shadow: none !important;
        box-shadow: none !important
    }

    .invoice-details .d-sm-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }

    .address.col-sm-6 {
        -webkit-box-flex: 0 !important;
        -ms-flex: 0 0 auto !important;
        flex: 0 0 auto !important;
        width: 50% !important;
        max-width: 100% !important
    }
}

.ribbon-box {
    position: relative
}

.ribbon-box .ribbon {
    padding: 5px 12px;
    -webkit-box-shadow: 2px 5px 10px rgba(33, 37, 41, .15);
    box-shadow: 2px 5px 10px rgba(33, 37, 41, .15);
    color: #fff;
    font-size: .8125rem;
    font-weight: 600;
    position: absolute;
    left: -1px;
    top: 5px
}

.ribbon-box .ribbon.round-shape {
    border-radius: 0 30px 30px 0
}

.ribbon-box .ribbon.ribbon-shape {
    display: inline-block
}

.ribbon-box .ribbon.ribbon-shape::before {
    content: "";
    position: absolute;
    right: -17px;
    top: 0;
    border: 14px solid transparent
}

.ribbon-box .ribbon.ribbon-shape::after {
    content: "";
    position: absolute;
    right: -17px;
    bottom: 0;
    border: 14px solid transparent
}

.ribbon-box.ribbon-circle .ribbon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    left: 20px;
    top: 20px
}

.ribbon-box.ribbon-fill {
    overflow: hidden
}

.ribbon-box.ribbon-fill .ribbon {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    width: 93px;
    height: 52px;
    left: -36px;
    top: -16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
}

.ribbon-box.ribbon-fill.ribbon-sm .ribbon {
    padding: 2px 12px;
    width: 78px;
    height: 42px;
    font-size: 12px;
    -webkit-box-shadow: none;
    box-shadow: none
}

.ribbon-box.right .ribbon {
    position: absolute;
    left: auto;
    right: 0
}

.ribbon-box.right .ribbon.round-shape {
    border-radius: 30px 0 0 30px
}

.ribbon-box.right .ribbon.ribbon-shape {
    text-align: right
}

.ribbon-box.right .ribbon.ribbon-shape::after,
.ribbon-box.right .ribbon.ribbon-shape::before {
    right: auto;
    left: -17px;
    border-left-color: transparent
}

.ribbon-box.right.ribbon-circle .ribbon {
    left: auto;
    right: 20px
}

.ribbon-box.right .icon-ribbon {
    right: 24px;
    left: auto
}

.ribbon-box.right.ribbon-fill .ribbon {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    right: -38px;
    left: auto
}

.ribbon-box.right.ribbon-box .ribbon-two {
    left: auto;
    right: -5px
}

.ribbon-box.right.ribbon-box .ribbon-two span {
    left: auto;
    right: -21px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.ribbon-box .ribbon-content {
    clear: both
}

.ribbon-box .ribbon-primary {
    background: #4b38b3
}

.ribbon-box .ribbon-primary:before {
    border-color: #3b2c8c transparent transparent
}

.ribbon-box .ribbon-primary.ribbon-shape::before {
    border-left-color: #4b38b3;
    border-top-color: #4b38b3
}

.ribbon-box .ribbon-primary.ribbon-shape::after {
    border-left-color: #4b38b3;
    border-bottom-color: #4b38b3
}

.ribbon-box.right .ribbon-primary {
    background: #4b38b3
}

.ribbon-box.right .ribbon-primary.ribbon-shape::before {
    border-right-color: #4b38b3;
    border-top-color: #4b38b3
}

.ribbon-box.right .ribbon-primary.ribbon-shape::after {
    border-right-color: #4b38b3;
    border-bottom-color: #4b38b3
}

.ribbon-box .ribbon-secondary {
    background: #3577f1
}

.ribbon-box .ribbon-secondary:before {
    border-color: #105ae3 transparent transparent
}

.ribbon-box .ribbon-secondary.ribbon-shape::before {
    border-left-color: #3577f1;
    border-top-color: #3577f1
}

.ribbon-box .ribbon-secondary.ribbon-shape::after {
    border-left-color: #3577f1;
    border-bottom-color: #3577f1
}

.ribbon-box.right .ribbon-secondary {
    background: #3577f1
}

.ribbon-box.right .ribbon-secondary.ribbon-shape::before {
    border-right-color: #3577f1;
    border-top-color: #3577f1
}

.ribbon-box.right .ribbon-secondary.ribbon-shape::after {
    border-right-color: #3577f1;
    border-bottom-color: #3577f1
}

.ribbon-box .ribbon-success {
    background: #45cb85
}

.ribbon-box .ribbon-success:before {
    border-color: #30ad6c transparent transparent
}

.ribbon-box .ribbon-success.ribbon-shape::before {
    border-left-color: #45cb85;
    border-top-color: #45cb85
}

.ribbon-box .ribbon-success.ribbon-shape::after {
    border-left-color: #45cb85;
    border-bottom-color: #45cb85
}

.ribbon-box.right .ribbon-success {
    background: #45cb85
}

.ribbon-box.right .ribbon-success.ribbon-shape::before {
    border-right-color: #45cb85;
    border-top-color: #45cb85
}

.ribbon-box.right .ribbon-success.ribbon-shape::after {
    border-right-color: #45cb85;
    border-bottom-color: #45cb85
}

.ribbon-box .ribbon-info {
    background: #299cdb
}

.ribbon-box .ribbon-info:before {
    border-color: #1e7eb3 transparent transparent
}

.ribbon-box .ribbon-info.ribbon-shape::before {
    border-left-color: #299cdb;
    border-top-color: #299cdb
}

.ribbon-box .ribbon-info.ribbon-shape::after {
    border-left-color: #299cdb;
    border-bottom-color: #299cdb
}

.ribbon-box.right .ribbon-info {
    background: #299cdb
}

.ribbon-box.right .ribbon-info.ribbon-shape::before {
    border-right-color: #299cdb;
    border-top-color: #299cdb
}

.ribbon-box.right .ribbon-info.ribbon-shape::after {
    border-right-color: #299cdb;
    border-bottom-color: #299cdb
}

.ribbon-box .ribbon-warning {
    background: #ffbe0b
}

.ribbon-box .ribbon-warning:before {
    border-color: #d79e00 transparent transparent
}

.ribbon-box .ribbon-warning.ribbon-shape::before {
    border-left-color: #ffbe0b;
    border-top-color: #ffbe0b
}

.ribbon-box .ribbon-warning.ribbon-shape::after {
    border-left-color: #ffbe0b;
    border-bottom-color: #ffbe0b
}

.ribbon-box.right .ribbon-warning {
    background: #ffbe0b
}

.ribbon-box.right .ribbon-warning.ribbon-shape::before {
    border-right-color: #ffbe0b;
    border-top-color: #ffbe0b
}

.ribbon-box.right .ribbon-warning.ribbon-shape::after {
    border-right-color: #ffbe0b;
    border-bottom-color: #ffbe0b
}

.ribbon-box .ribbon-danger {
    background: #f06548
}

.ribbon-box .ribbon-danger:before {
    border-color: #ec3d19 transparent transparent
}

.ribbon-box .ribbon-danger.ribbon-shape::before {
    border-left-color: #f06548;
    border-top-color: #f06548
}

.ribbon-box .ribbon-danger.ribbon-shape::after {
    border-left-color: #f06548;
    border-bottom-color: #f06548
}

.ribbon-box.right .ribbon-danger {
    background: #f06548
}

.ribbon-box.right .ribbon-danger.ribbon-shape::before {
    border-right-color: #f06548;
    border-top-color: #f06548
}

.ribbon-box.right .ribbon-danger.ribbon-shape::after {
    border-right-color: #f06548;
    border-bottom-color: #f06548
}

.ribbon-box .ribbon-light {
    background: #f3f6f9
}

.ribbon-box .ribbon-light:before {
    border-color: #d1dde8 transparent transparent
}

.ribbon-box .ribbon-light.ribbon-shape::before {
    border-left-color: #f3f6f9;
    border-top-color: #f3f6f9
}

.ribbon-box .ribbon-light.ribbon-shape::after {
    border-left-color: #f3f6f9;
    border-bottom-color: #f3f6f9
}

.ribbon-box.right .ribbon-light {
    background: #f3f6f9
}

.ribbon-box.right .ribbon-light.ribbon-shape::before {
    border-right-color: #f3f6f9;
    border-top-color: #f3f6f9
}

.ribbon-box.right .ribbon-light.ribbon-shape::after {
    border-right-color: #f3f6f9;
    border-bottom-color: #f3f6f9
}

.ribbon-box .ribbon-dark {
    background: #212529
}

.ribbon-box .ribbon-dark:before {
    border-color: #0a0c0d transparent transparent
}

.ribbon-box .ribbon-dark.ribbon-shape::before {
    border-left-color: #212529;
    border-top-color: #212529
}

.ribbon-box .ribbon-dark.ribbon-shape::after {
    border-left-color: #212529;
    border-bottom-color: #212529
}

.ribbon-box.right .ribbon-dark {
    background: #212529
}

.ribbon-box.right .ribbon-dark.ribbon-shape::before {
    border-right-color: #212529;
    border-top-color: #212529
}

.ribbon-box.right .ribbon-dark.ribbon-shape::after {
    border-right-color: #212529;
    border-bottom-color: #212529
}

.ribbon-box .icon-ribbon {
    -webkit-box-shadow: none;
    box-shadow: none;
    left: 24px;
    top: -12px;
    font-size: 40px;
    padding: 0
}

.ribbon-box .ribbon-two {
    position: absolute;
    left: -5px;
    top: -5px;
    z-index: 1;
    overflow: hidden;
    width: 75px;
    height: 75px;
    text-align: right
}

.ribbon-box .ribbon-two span {
    font-size: 13px;
    color: #fff;
    text-align: center;
    line-height: 20px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    width: 100px;
    display: block;
    -webkit-box-shadow: 0 0 8px 0 rgba(0, 0, 0, .06), 0 1px 0 0 rgba(0, 0, 0, .02);
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, .06), 0 1px 0 0 rgba(0, 0, 0, .02);
    position: absolute;
    top: 19px;
    left: -21px;
    font-weight: 600
}

.ribbon-box .ribbon-two span:before {
    content: "";
    position: absolute;
    left: 0;
    top: 100%;
    z-index: -1;
    border-right: 3px solid transparent;
    border-bottom: 3px solid transparent
}

.ribbon-box .ribbon-two span:after {
    content: "";
    position: absolute;
    right: 0;
    top: 100%;
    z-index: -1;
    border-left: 3px solid transparent;
    border-bottom: 3px solid transparent
}

.ribbon-box .ribbon-two-primary span {
    background: #4b38b3
}

.ribbon-box .ribbon-two-primary span:before {
    border-left: 3px solid #332679;
    border-top: 3px solid #332679
}

.ribbon-box .ribbon-two-primary span:after {
    border-right: 3px solid #332679;
    border-top: 3px solid #332679
}

.ribbon-box .ribbon-two-secondary span {
    background: #3577f1
}

.ribbon-box .ribbon-two-secondary span:before {
    border-left: 3px solid #0e51cb;
    border-top: 3px solid #0e51cb
}

.ribbon-box .ribbon-two-secondary span:after {
    border-right: 3px solid #0e51cb;
    border-top: 3px solid #0e51cb
}

.ribbon-box .ribbon-two-success span {
    background: #45cb85
}

.ribbon-box .ribbon-two-success span:before {
    border-left: 3px solid #2b995f;
    border-top: 3px solid #2b995f
}

.ribbon-box .ribbon-two-success span:after {
    border-right: 3px solid #2b995f;
    border-top: 3px solid #2b995f
}

.ribbon-box .ribbon-two-info span {
    background: #299cdb
}

.ribbon-box .ribbon-two-info span:before {
    border-left: 3px solid #1a6f9d;
    border-top: 3px solid #1a6f9d
}

.ribbon-box .ribbon-two-info span:after {
    border-right: 3px solid #1a6f9d;
    border-top: 3px solid #1a6f9d
}

.ribbon-box .ribbon-two-warning span {
    background: #ffbe0b
}

.ribbon-box .ribbon-two-warning span:before {
    border-left: 3px solid #be8b00;
    border-top: 3px solid #be8b00
}

.ribbon-box .ribbon-two-warning span:after {
    border-right: 3px solid #be8b00;
    border-top: 3px solid #be8b00
}

.ribbon-box .ribbon-two-danger span {
    background: #f06548
}

.ribbon-box .ribbon-two-danger span:before {
    border-left: 3px solid #da3412;
    border-top: 3px solid #da3412
}

.ribbon-box .ribbon-two-danger span:after {
    border-right: 3px solid #da3412;
    border-top: 3px solid #da3412
}

.ribbon-box .ribbon-two-light span {
    background: #f3f6f9
}

.ribbon-box .ribbon-two-light span:before {
    border-left: 3px solid #c0d0e0;
    border-top: 3px solid #c0d0e0
}

.ribbon-box .ribbon-two-light span:after {
    border-right: 3px solid #c0d0e0;
    border-top: 3px solid #c0d0e0
}

.ribbon-box .ribbon-two-dark span {
    background: #212529
}

.ribbon-box .ribbon-two-dark span:before {
    border-left: 3px solid #000;
    border-top: 3px solid #000
}

.ribbon-box .ribbon-two-dark span:after {
    border-right: 3px solid #000;
    border-top: 3px solid #000
}

.ribbon-box.right .ribbon-three {
    position: absolute;
    top: -6.1px;
    right: 10px;
    left: auto
}

.ribbon-three {
    position: absolute;
    top: -6.1px;
    left: 10px
}

.ribbon-three span {
    position: relative;
    display: block;
    text-align: center;
    color: #fff;
    font-size: 14px;
    line-height: 1;
    padding: 12px 8px 10px;
    border-top-right-radius: 8px;
    width: 90px
}

.ribbon-three span::after,
.ribbon-three span::before {
    position: absolute;
    content: ""
}

.ribbon-three span::before {
    height: 6px;
    width: 6px;
    left: -6px;
    top: 0
}

.ribbon-three span::after {
    height: 6px;
    width: 8px;
    left: -8px;
    top: 0;
    border-radius: 8px 8px 0 0
}

.ribbon-three::after {
    position: absolute;
    content: "";
    width: 0;
    height: 0;
    border-left: 44px solid transparent;
    border-right: 44px solid transparent;
    border-top: 10px solid
}

.ribbon-three-primary span {
    background: #4b38b3
}

.ribbon-three-primary span:before {
    background: #4b38b3
}

.ribbon-three-primary span:after {
    background: #3b2c8c
}

.ribbon-three-primary::after {
    border-top-color: #4b38b3
}

.ribbon-three-secondary span {
    background: #3577f1
}

.ribbon-three-secondary span:before {
    background: #3577f1
}

.ribbon-three-secondary span:after {
    background: #105ae3
}

.ribbon-three-secondary::after {
    border-top-color: #3577f1
}

.ribbon-three-success span {
    background: #45cb85
}

.ribbon-three-success span:before {
    background: #45cb85
}

.ribbon-three-success span:after {
    background: #30ad6c
}

.ribbon-three-success::after {
    border-top-color: #45cb85
}

.ribbon-three-info span {
    background: #299cdb
}

.ribbon-three-info span:before {
    background: #299cdb
}

.ribbon-three-info span:after {
    background: #1e7eb3
}

.ribbon-three-info::after {
    border-top-color: #299cdb
}

.ribbon-three-warning span {
    background: #ffbe0b
}

.ribbon-three-warning span:before {
    background: #ffbe0b
}

.ribbon-three-warning span:after {
    background: #d79e00
}

.ribbon-three-warning::after {
    border-top-color: #ffbe0b
}

.ribbon-three-danger span {
    background: #f06548
}

.ribbon-three-danger span:before {
    background: #f06548
}

.ribbon-three-danger span:after {
    background: #ec3d19
}

.ribbon-three-danger::after {
    border-top-color: #f06548
}

.ribbon-three-light span {
    background: #f3f6f9
}

.ribbon-three-light span:before {
    background: #f3f6f9
}

.ribbon-three-light span:after {
    background: #d1dde8
}

.ribbon-three-light::after {
    border-top-color: #f3f6f9
}

.ribbon-three-dark span {
    background: #212529
}

.ribbon-three-dark span:before {
    background: #212529
}

.ribbon-three-dark span:after {
    background: #0a0c0d
}

.ribbon-three-dark::after {
    border-top-color: #212529
}

.ribbon-box .trending-ribbon {
    -webkit-transform: translateX(-50px);
    transform: translateX(-50px);
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

.ribbon-box .trending-ribbon .trending-ribbon-text {
    -webkit-transition: all .5s ease;
    transition: all .5s ease;
    opacity: 0
}

.ribbon-box:hover .trending-ribbon {
    -webkit-transform: translateX(0);
    transform: translateX(0)
}

.ribbon-box:hover .trending-ribbon .trending-ribbon-text {
    opacity: 1
}

.ribbon-box.right .trending-ribbon {
    -webkit-transform: translateX(50px);
    transform: translateX(50px);
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

.ribbon-box.right .trending-ribbon .trending-ribbon-text {
    -webkit-transition: all .5s ease;
    transition: all .5s ease;
    opacity: 0
}

.ribbon-box.right:hover .trending-ribbon {
    -webkit-transform: translateX(0);
    transform: translateX(0)
}

.ribbon-box.right:hover .trending-ribbon .trending-ribbon-text {
    opacity: 1
}

.toast:not(:last-child) {
    margin-bottom: .75rem
}

.toast-border-primary .toast-body {
    color: #4b38b3;
    border-bottom: 3px solid #4b38b3
}

.toast-border-secondary .toast-body {
    color: #3577f1;
    border-bottom: 3px solid #3577f1
}

.toast-border-success .toast-body {
    color: #45cb85;
    border-bottom: 3px solid #45cb85
}

.toast-border-info .toast-body {
    color: #299cdb;
    border-bottom: 3px solid #299cdb
}

.toast-border-warning .toast-body {
    color: #ffbe0b;
    border-bottom: 3px solid #ffbe0b
}

.toast-border-danger .toast-body {
    color: #f06548;
    border-bottom: 3px solid #f06548
}

.toast-border-light .toast-body {
    color: #f3f6f9;
    border-bottom: 3px solid #f3f6f9
}

.toast-border-dark .toast-body {
    color: #212529;
    border-bottom: 3px solid #212529
}

.scrollspy-example {
    position: relative;
    height: 200px;
    margin-top: .5rem;
    overflow: auto
}

.scrollspy-example-2 {
    position: relative;
    height: 370px;
    overflow: auto
}

html {
    position: relative;
    min-height: 100%
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    color: var(--vz-heading-color);
    font-family: Inter, sans-serif
}

a {
    text-decoration: none !important
}

label {
    font-weight: 500;
    margin-bottom: .5rem
}

b,
strong {
    font-weight: 600
}

.blockquote {
    padding: 10px 20px;
    border-left: 4px solid #e9ebec
}

.blockquote-reverse {
    border-left: 0;
    border-right: 4px solid #e9ebec;
    text-align: right
}

@media (min-width:1200px) {

    .container,
    .container-lg,
    .container-md,
    .container-sm,
    .container-xl,
    .container-xxl {
        max-width: 1140px
    }
}

.row>* {
    position: relative
}

.alert-solid .alert-link {
    color: #fff
}

.alert-outline {
    background-color: var(--vz-card-bg);
    border-width: 2px
}

.alert-borderless {
    border-width: 0
}

.alert-label-icon {
    position: relative;
    padding-left: 60px;
    border: 0
}

.alert-label-icon .label-icon {
    position: absolute;
    width: 45px;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(255, 255, 255, .15);
    font-size: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.alert-label-icon.label-arrow {
    overflow: hidden
}

.alert-label-icon.label-arrow .label-icon:after {
    content: "";
    position: absolute;
    border: 6px solid transparent;
    border-left-color: #4b38b3;
    right: -12px
}

.alert-border-left {
    border-left: 3px solid
}

.alert-top-border {
    background-color: var(--vz-card-bg);
    border-color: var(--vz-border-color);
    border-top: 2px solid;
    color: var(--vz-body-color)
}

.alert-additional {
    padding: 0
}

.alert-additional .alert-body {
    padding: .8rem 1rem
}

.alert-additional .alert-content {
    padding: .8rem 1rem;
    border-bottom-left-radius: .25rem;
    border-bottom-right-radius: .25rem;
    margin: 0 -1px -1px -1px
}

.rounded-label .label-icon {
    width: 45px;
    height: 26px;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    border-radius: 0 30px 30px 0
}

.alert-solid.alert-primary {
    background-color: #4b38b3;
    border-color: #4b38b3;
    color: #fff
}

.alert-outline.alert-primary {
    color: #4b38b3
}

.alert-border-left.alert-primary {
    border-left-color: #4b38b3
}

.alert-top-border.alert-primary {
    border-top-color: #4b38b3
}

.rounded-label.alert-primary .label-icon {
    background-color: #4b38b3;
    color: #fff
}

.label-arrow.alert-primary .label-icon {
    color: #fff;
    background-color: #4b38b3
}

.label-arrow.alert-primary .label-icon:after {
    border-left-color: #4b38b3 !important
}

.alert.alert-primary .btn-close::after {
    color: #4b38b3
}

.alert-additional.alert-primary .alert-content {
    background-color: #4b38b3;
    color: #fff
}

.alert-solid.alert-secondary {
    background-color: #3577f1;
    border-color: #3577f1;
    color: #fff
}

.alert-outline.alert-secondary {
    color: #3577f1
}

.alert-border-left.alert-secondary {
    border-left-color: #3577f1
}

.alert-top-border.alert-secondary {
    border-top-color: #3577f1
}

.rounded-label.alert-secondary .label-icon {
    background-color: #3577f1;
    color: #fff
}

.label-arrow.alert-secondary .label-icon {
    color: #fff;
    background-color: #3577f1
}

.label-arrow.alert-secondary .label-icon:after {
    border-left-color: #3577f1 !important
}

.alert.alert-secondary .btn-close::after {
    color: #3577f1
}

.alert-additional.alert-secondary .alert-content {
    background-color: #3577f1;
    color: #fff
}

.alert-solid.alert-success {
    background-color: #45cb85;
    border-color: #45cb85;
    color: #fff
}

.alert-outline.alert-success {
    color: #45cb85
}

.alert-border-left.alert-success {
    border-left-color: #45cb85
}

.alert-top-border.alert-success {
    border-top-color: #45cb85
}

.rounded-label.alert-success .label-icon {
    background-color: #45cb85;
    color: #fff
}

.label-arrow.alert-success .label-icon {
    color: #fff;
    background-color: #45cb85
}

.label-arrow.alert-success .label-icon:after {
    border-left-color: #45cb85 !important
}

.alert.alert-success .btn-close::after {
    color: #45cb85
}

.alert-additional.alert-success .alert-content {
    background-color: #45cb85;
    color: #fff
}

.alert-solid.alert-info {
    background-color: #299cdb;
    border-color: #299cdb;
    color: #fff
}

.alert-outline.alert-info {
    color: #299cdb
}

.alert-border-left.alert-info {
    border-left-color: #299cdb
}

.alert-top-border.alert-info {
    border-top-color: #299cdb
}

.rounded-label.alert-info .label-icon {
    background-color: #299cdb;
    color: #fff
}

.label-arrow.alert-info .label-icon {
    color: #fff;
    background-color: #299cdb
}

.label-arrow.alert-info .label-icon:after {
    border-left-color: #299cdb !important
}

.alert.alert-info .btn-close::after {
    color: #299cdb
}

.alert-additional.alert-info .alert-content {
    background-color: #299cdb;
    color: #fff
}

.alert-solid.alert-warning {
    background-color: #ffbe0b;
    border-color: #ffbe0b;
    color: #fff
}

.alert-outline.alert-warning {
    color: #ffbe0b
}

.alert-border-left.alert-warning {
    border-left-color: #ffbe0b
}

.alert-top-border.alert-warning {
    border-top-color: #ffbe0b
}

.rounded-label.alert-warning .label-icon {
    background-color: #ffbe0b;
    color: #fff
}

.label-arrow.alert-warning .label-icon {
    color: #fff;
    background-color: #ffbe0b
}

.label-arrow.alert-warning .label-icon:after {
    border-left-color: #ffbe0b !important
}

.alert.alert-warning .btn-close::after {
    color: #ffbe0b
}

.alert-additional.alert-warning .alert-content {
    background-color: #ffbe0b;
    color: #fff
}

.alert-solid.alert-danger {
    background-color: #f06548;
    border-color: #f06548;
    color: #fff
}

.alert-outline.alert-danger {
    color: #f06548
}

.alert-border-left.alert-danger {
    border-left-color: #f06548
}

.alert-top-border.alert-danger {
    border-top-color: #f06548
}

.rounded-label.alert-danger .label-icon {
    background-color: #f06548;
    color: #fff
}

.label-arrow.alert-danger .label-icon {
    color: #fff;
    background-color: #f06548
}

.label-arrow.alert-danger .label-icon:after {
    border-left-color: #f06548 !important
}

.alert.alert-danger .btn-close::after {
    color: #f06548
}

.alert-additional.alert-danger .alert-content {
    background-color: #f06548;
    color: #fff
}

.alert-solid.alert-light {
    background-color: #f3f6f9;
    border-color: #f3f6f9;
    color: #000
}

.alert-outline.alert-light {
    color: #f3f6f9
}

.alert-border-left.alert-light {
    border-left-color: #f3f6f9
}

.alert-top-border.alert-light {
    border-top-color: #f3f6f9
}

.rounded-label.alert-light .label-icon {
    background-color: #f3f6f9;
    color: #000
}

.label-arrow.alert-light .label-icon {
    color: #000;
    background-color: #f3f6f9
}

.label-arrow.alert-light .label-icon:after {
    border-left-color: #f3f6f9 !important
}

.alert.alert-light .btn-close::after {
    color: #f3f6f9
}

.alert-additional.alert-light .alert-content {
    background-color: #f3f6f9;
    color: #000
}

.alert-solid.alert-dark {
    background-color: #212529;
    border-color: #212529;
    color: #fff
}

.alert-outline.alert-dark {
    color: #212529
}

.alert-border-left.alert-dark {
    border-left-color: #212529
}

.alert-top-border.alert-dark {
    border-top-color: #212529
}

.rounded-label.alert-dark .label-icon {
    background-color: #212529;
    color: #fff
}

.label-arrow.alert-dark .label-icon {
    color: #fff;
    background-color: #212529
}

.label-arrow.alert-dark .label-icon:after {
    border-left-color: #212529 !important
}

.alert.alert-dark .btn-close::after {
    color: #212529
}

.alert-additional.alert-dark .alert-content {
    background-color: #212529;
    color: #fff
}

.alert-dismissible .btn-close {
    background: 0 0 !important
}

.alert-dismissible .btn-close::after {
    background: 0 0 !important;
    content: "\f0156" !important;
    font-size: 18px;
    line-height: 15px;
    font-family: "Material Design Icons" !important
}

.alert-outline.alert-dark {
    color: var(--vz-dark)
}

.bg-soft-primary {
    background-color: rgba(75, 56, 179, .18) !important
}

.bg-soft-secondary {
    background-color: rgba(53, 119, 241, .18) !important
}

.bg-soft-success {
    background-color: rgba(69, 203, 133, .18) !important
}

.bg-soft-info {
    background-color: rgba(41, 156, 219, .18) !important
}

.bg-soft-warning {
    background-color: rgba(255, 190, 11, .18) !important
}

.bg-soft-danger {
    background-color: rgba(240, 101, 72, .18) !important
}

.bg-soft-light {
    background-color: rgba(243, 246, 249, .18) !important
}

.bg-soft-dark {
    background-color: rgba(33, 37, 41, .18) !important
}

.bg-soft-dark {
    background-color: rgba(var(--vz-dark-rgb), .18) !important
}

.bg-soft-light {
    background-color: rgba(var(--vz-light-rgb), .18) !important
}

.badge-outline-primary {
    color: #4b38b3;
    border: 1px solid #4b38b3;
    background-color: transparent
}

.badge-outline-secondary {
    color: #3577f1;
    border: 1px solid #3577f1;
    background-color: transparent
}

.badge-outline-success {
    color: #45cb85;
    border: 1px solid #45cb85;
    background-color: transparent
}

.badge-outline-info {
    color: #299cdb;
    border: 1px solid #299cdb;
    background-color: transparent
}

.badge-outline-warning {
    color: #ffbe0b;
    border: 1px solid #ffbe0b;
    background-color: transparent
}

.badge-outline-danger {
    color: #f06548;
    border: 1px solid #f06548;
    background-color: transparent
}

.badge-outline-light {
    color: #f3f6f9;
    border: 1px solid #f3f6f9;
    background-color: transparent
}

.badge-outline-dark {
    color: #212529;
    border: 1px solid #212529;
    background-color: transparent
}

.badge-outline {
    background-color: var(--vz-card-bg);
    border: 1px solid
}

.badge-soft-primary {
    color: #4b38b3;
    background-color: rgba(75, 56, 179, .1)
}

.badge-outline.badge-primary {
    color: #4b38b3;
    border: 1px solid #4b38b3;
    background-color: transparent
}

.badge-label.bg-primary:before {
    border-right-color: #4b38b3
}

.badge-soft-secondary {
    color: #3577f1;
    background-color: rgba(53, 119, 241, .1)
}

.badge-outline.badge-secondary {
    color: #3577f1;
    border: 1px solid #3577f1;
    background-color: transparent
}

.badge-label.bg-secondary:before {
    border-right-color: #3577f1
}

.badge-soft-success {
    color: #45cb85;
    background-color: rgba(69, 203, 133, .1)
}

.badge-outline.badge-success {
    color: #45cb85;
    border: 1px solid #45cb85;
    background-color: transparent
}

.badge-label.bg-success:before {
    border-right-color: #45cb85
}

.badge-soft-info {
    color: #299cdb;
    background-color: rgba(41, 156, 219, .1)
}

.badge-outline.badge-info {
    color: #299cdb;
    border: 1px solid #299cdb;
    background-color: transparent
}

.badge-label.bg-info:before {
    border-right-color: #299cdb
}

.badge-soft-warning {
    color: #ffbe0b;
    background-color: rgba(255, 190, 11, .1)
}

.badge-outline.badge-warning {
    color: #ffbe0b;
    border: 1px solid #ffbe0b;
    background-color: transparent
}

.badge-label.bg-warning:before {
    border-right-color: #ffbe0b
}

.badge-soft-danger {
    color: #f06548;
    background-color: rgba(240, 101, 72, .1)
}

.badge-outline.badge-danger {
    color: #f06548;
    border: 1px solid #f06548;
    background-color: transparent
}

.badge-label.bg-danger:before {
    border-right-color: #f06548
}

.badge-soft-light {
    color: #f3f6f9;
    background-color: rgba(243, 246, 249, .1)
}

.badge-outline.badge-light {
    color: #f3f6f9;
    border: 1px solid #f3f6f9;
    background-color: transparent
}

.badge-label.bg-light:before {
    border-right-color: #f3f6f9
}

.badge-soft-dark {
    color: #212529;
    background-color: rgba(33, 37, 41, .1)
}

.badge-outline.badge-dark {
    color: #212529;
    border: 1px solid #212529;
    background-color: transparent
}

.badge-label.bg-dark:before {
    border-right-color: #212529
}

.badge-border {
    border-left: 2px solid
}

.badge-label {
    margin-left: 8px;
    position: relative
}

.badge-label:before {
    content: "";
    position: absolute;
    border: 8px solid transparent;
    border-right-color: #4b38b3;
    left: -14px;
    top: 0
}

[data-layout-mode=dark] .badge.bg-dark {
    background-color: var(--vz-light) !important
}

[data-layout-mode=dark] .badge.bg-light {
    background-color: var(--vz-dark) !important;
    color: var(--vz-light) !important
}

.badge-gradient-primary {
    background: linear-gradient(135deg, #4b38b3 0, #45cb85 100%)
}

.badge-gradient-secondary {
    background: linear-gradient(135deg, #3577f1 0, #299cdb 100%)
}

.badge-gradient-success {
    background: linear-gradient(135deg, #45cb85 0, #ffbe0b 100%)
}

.badge-gradient-danger {
    background: linear-gradient(135deg, #f06548 0, #3577f1 100%)
}

.badge-gradient-warning {
    background: linear-gradient(135deg, #ffbe0b 0, #be8b00 100%)
}

.badge-gradient-info {
    background: linear-gradient(135deg, #299cdb 0, #45cb85 100%)
}

.badge-gradient-dark {
    background: linear-gradient(135deg, #212529 0, #4b38b3 100%)
}

a,
button {
    outline: 0 !important;
    position: relative
}

.btn {
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.btn-rounded {
    border-radius: 30px
}

.btn-icon {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: calc(1rem + 1.5em + 2px);
    width: calc(1rem + 1.5em + 2px);
    padding: 0
}

.btn-icon i,
.btn-icon img,
.btn-icon svg {
    vertical-align: middle
}

.btn-icon.btn-sm {
    height: calc(.5rem + 1.5em + 2px);
    width: calc(.5rem + 1.5em + 2px)
}

.btn-icon.btn-lg {
    height: calc(1.4rem + 1.5em + 2px);
    width: calc(1.4rem + 1.5em + 2px)
}

.btn-soft-primary {
    color: #4b38b3;
    background-color: rgba(75, 56, 179, .1);
    border-color: transparent
}

.btn-soft-primary:active,
.btn-soft-primary:focus,
.btn-soft-primary:hover {
    color: #fff;
    background-color: #4b38b3
}

.btn-soft-secondary {
    color: #3577f1;
    background-color: rgba(53, 119, 241, .1);
    border-color: transparent
}

.btn-soft-secondary:active,
.btn-soft-secondary:focus,
.btn-soft-secondary:hover {
    color: #fff;
    background-color: #3577f1
}

.btn-soft-success {
    color: #45cb85;
    background-color: rgba(69, 203, 133, .1);
    border-color: transparent
}

.btn-soft-success:active,
.btn-soft-success:focus,
.btn-soft-success:hover {
    color: #fff;
    background-color: #45cb85
}

.btn-soft-info {
    color: #299cdb;
    background-color: rgba(41, 156, 219, .1);
    border-color: transparent
}

.btn-soft-info:active,
.btn-soft-info:focus,
.btn-soft-info:hover {
    color: #fff;
    background-color: #299cdb
}

.btn-soft-warning {
    color: #ffbe0b;
    background-color: rgba(255, 190, 11, .1);
    border-color: transparent
}

.btn-soft-warning:active,
.btn-soft-warning:focus,
.btn-soft-warning:hover {
    color: #fff;
    background-color: #ffbe0b
}

.btn-soft-danger {
    color: #f06548;
    background-color: rgba(240, 101, 72, .1);
    border-color: transparent
}

.btn-soft-danger:active,
.btn-soft-danger:focus,
.btn-soft-danger:hover {
    color: #fff;
    background-color: #f06548
}

.btn-soft-light {
    color: #f3f6f9;
    background-color: rgba(243, 246, 249, .1);
    border-color: transparent
}

.btn-soft-light:active,
.btn-soft-light:focus,
.btn-soft-light:hover {
    color: #fff;
    background-color: #f3f6f9
}

.btn-soft-dark {
    color: #212529;
    background-color: rgba(33, 37, 41, .1);
    border-color: transparent
}

.btn-soft-dark:active,
.btn-soft-dark:focus,
.btn-soft-dark:hover {
    color: #fff;
    background-color: #212529
}

.btn-ghost-primary {
    color: #4b38b3;
    border-color: transparent
}

.btn-ghost-primary:active,
.btn-ghost-primary:focus,
.btn-ghost-primary:hover {
    color: #4b38b3;
    background-color: rgba(75, 56, 179, .1)
}

.btn-ghost-secondary {
    color: #3577f1;
    border-color: transparent
}

.btn-ghost-secondary:active,
.btn-ghost-secondary:focus,
.btn-ghost-secondary:hover {
    color: #3577f1;
    background-color: rgba(53, 119, 241, .1)
}

.btn-ghost-success {
    color: #45cb85;
    border-color: transparent
}

.btn-ghost-success:active,
.btn-ghost-success:focus,
.btn-ghost-success:hover {
    color: #45cb85;
    background-color: rgba(69, 203, 133, .1)
}

.btn-ghost-info {
    color: #299cdb;
    border-color: transparent
}

.btn-ghost-info:active,
.btn-ghost-info:focus,
.btn-ghost-info:hover {
    color: #299cdb;
    background-color: rgba(41, 156, 219, .1)
}

.btn-ghost-warning {
    color: #ffbe0b;
    border-color: transparent
}

.btn-ghost-warning:active,
.btn-ghost-warning:focus,
.btn-ghost-warning:hover {
    color: #ffbe0b;
    background-color: rgba(255, 190, 11, .1)
}

.btn-ghost-danger {
    color: #f06548;
    border-color: transparent
}

.btn-ghost-danger:active,
.btn-ghost-danger:focus,
.btn-ghost-danger:hover {
    color: #f06548;
    background-color: rgba(240, 101, 72, .1)
}

.btn-ghost-light {
    color: #f3f6f9;
    border-color: transparent
}

.btn-ghost-light:active,
.btn-ghost-light:focus,
.btn-ghost-light:hover {
    color: #f3f6f9;
    background-color: rgba(243, 246, 249, .1)
}

.btn-ghost-dark {
    color: #212529;
    border-color: transparent
}

.btn-ghost-dark:active,
.btn-ghost-dark:focus,
.btn-ghost-dark:hover {
    color: #212529;
    background-color: rgba(33, 37, 41, .1)
}

.btn-ghost-secondary {
    color: var(--vz-gray-700)
}

.btn-label {
    position: relative;
    padding-left: 44px
}

.btn-label .label-icon {
    position: absolute;
    width: 35.5px;
    left: -1px;
    top: -1px;
    bottom: -1px;
    background-color: rgba(255, 255, 255, .1);
    font-size: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.btn-label.btn-light .label-icon {
    background-color: rgba(33, 37, 41, .05)
}

.btn-label.right {
    padding-left: .9rem;
    padding-right: 44px
}

.btn-label.right .label-icon {
    right: -1px;
    left: auto
}

.btn-animation {
    overflow: hidden;
    -webkit-transition: border-color .3s, background-color .3s;
    transition: border-color .3s, background-color .3s;
    -webkit-transition-timing-function: cubic-bezier(.2, 1, .3, 1);
    transition-timing-function: cubic-bezier(.2, 1, .3, 1)
}

.btn-animation::after {
    content: attr(data-text);
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    -webkit-transform: translate3d(0, 25%, 0);
    transform: translate3d(0, 25%, 0);
    padding: .5rem .9rem;
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, -webkit-transform .3s;
    transition: transform .3s, opacity .3s;
    transition: transform .3s, opacity .3s, -webkit-transform .3s;
    -webkit-transition-timing-function: cubic-bezier(.2, 1, .3, 1);
    transition-timing-function: cubic-bezier(.2, 1, .3, 1)
}

.btn-animation>span {
    display: block;
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, -webkit-transform .3s;
    transition: transform .3s, opacity .3s;
    transition: transform .3s, opacity .3s, -webkit-transform .3s;
    -webkit-transition-timing-function: cubic-bezier(.2, 1, .3, 1);
    transition-timing-function: cubic-bezier(.2, 1, .3, 1)
}

.btn-animation:hover::after {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.btn-animation:hover>span {
    opacity: 0;
    -webkit-transform: translate3d(0, -25%, 0);
    transform: translate3d(0, -25%, 0)
}

.btn-primary.btn-animation {
    background-color: #4b38b3;
    border-color: #4b38b3
}

.btn-primary.btn-animation:active,
.btn-primary.btn-animation:focus,
.btn-primary.btn-animation:hover {
    color: #4b38b3;
    background-color: rgba(75, 56, 179, .1)
}

.btn-secondary.btn-animation {
    background-color: #3577f1;
    border-color: #3577f1
}

.btn-secondary.btn-animation:active,
.btn-secondary.btn-animation:focus,
.btn-secondary.btn-animation:hover {
    color: #3577f1;
    background-color: rgba(53, 119, 241, .1)
}

.btn-success.btn-animation {
    background-color: #45cb85;
    border-color: #45cb85
}

.btn-success.btn-animation:active,
.btn-success.btn-animation:focus,
.btn-success.btn-animation:hover {
    color: #45cb85;
    background-color: rgba(69, 203, 133, .1)
}

.btn-info.btn-animation {
    background-color: #299cdb;
    border-color: #299cdb
}

.btn-info.btn-animation:active,
.btn-info.btn-animation:focus,
.btn-info.btn-animation:hover {
    color: #299cdb;
    background-color: rgba(41, 156, 219, .1)
}

.btn-warning.btn-animation {
    background-color: #ffbe0b;
    border-color: #ffbe0b
}

.btn-warning.btn-animation:active,
.btn-warning.btn-animation:focus,
.btn-warning.btn-animation:hover {
    color: #ffbe0b;
    background-color: rgba(255, 190, 11, .1)
}

.btn-danger.btn-animation {
    background-color: #f06548;
    border-color: #f06548
}

.btn-danger.btn-animation:active,
.btn-danger.btn-animation:focus,
.btn-danger.btn-animation:hover {
    color: #f06548;
    background-color: rgba(240, 101, 72, .1)
}

.btn-light.btn-animation {
    background-color: #f3f6f9;
    border-color: #f3f6f9
}

.btn-light.btn-animation:active,
.btn-light.btn-animation:focus,
.btn-light.btn-animation:hover {
    color: #f3f6f9;
    background-color: rgba(243, 246, 249, .1)
}

.btn-dark.btn-animation {
    background-color: #212529;
    border-color: #212529
}

.btn-dark.btn-animation:active,
.btn-dark.btn-animation:focus,
.btn-dark.btn-animation:hover {
    color: #212529;
    background-color: rgba(33, 37, 41, .1)
}

.btn-group-vertical label {
    margin-bottom: 0
}

.btn-group.radio .btn {
    border: none
}

.btn-group.radio .btn-check:active+.btn-light,
.btn-group.radio .btn-check:checked+.btn-light,
.btn-group.radio .btn-light.active,
.btn-group.radio .btn-light.dropdown-toggle.show,
.btn-group.radio .btn-light:active {
    background-color: rgba(41, 156, 219, .2);
    color: #299cdb
}

[data-layout-mode=dark] .btn-light {
    color: var(--vz-dark);
    background-color: var(--vz-light);
    border-color: var(--vz-light)
}

[data-layout-mode=dark] .btn-light:hover {
    color: var(--vz-dark);
    background-color: rgba(var(--vz-light-rgb), .75);
    border-color: rgba(var(--vz-light-rgb), .75)
}

[data-layout-mode=dark] .btn-check:focus+.btn-light,
[data-layout-mode=dark] .btn-check:focus+.btn-outline-light,
[data-layout-mode=dark] .btn-check:focus+.btn-soft-light,
[data-layout-mode=dark] .btn-light:focus,
[data-layout-mode=dark] .btn-outline-light:focus,
[data-layout-mode=dark] .btn-soft-light:focus {
    color: var(--vz-dark);
    background-color: rgba(var(--vz-light-rgb), .75);
    border-color: rgba(var(--vz-light-rgb), .75)
}

[data-layout-mode=dark] .btn-check:active+.btn-light,
[data-layout-mode=dark] .btn-check:active+.btn-outline-light,
[data-layout-mode=dark] .btn-check:active+.btn-soft-light,
[data-layout-mode=dark] .btn-check:checked+.btn-light,
[data-layout-mode=dark] .btn-check:checked+.btn-outline-light,
[data-layout-mode=dark] .btn-check:checked+.btn-soft-light,
[data-layout-mode=dark] .btn-light.active,
[data-layout-mode=dark] .btn-light:active,
[data-layout-mode=dark] .btn-outline-light.active,
[data-layout-mode=dark] .btn-outline-light:active,
[data-layout-mode=dark] .btn-soft-light.active,
[data-layout-mode=dark] .btn-soft-light:active,
[data-layout-mode=dark] .show>.btn-light.dropdown-toggle,
[data-layout-mode=dark] .show>.btn-outline-light.dropdown-toggle,
[data-layout-mode=dark] .show>.btn-soft-light.dropdown-toggle {
    color: var(--vz-dark);
    background-color: rgba(var(--vz-light-rgb), .75);
    border-color: rgba(var(--vz-light-rgb), .75)
}

[data-layout-mode=dark] .btn-dark {
    color: var(--vz-light);
    background-color: var(--vz-dark);
    border-color: var(--vz-dark)
}

[data-layout-mode=dark] .btn-dark:hover {
    color: var(--vz-light);
    background-color: rgba(var(--vz-dark-rgb), .75);
    border-color: rgba(var(--vz-dark-rgb), .75)
}

[data-layout-mode=dark] .btn-check:focus+.btn-dark,
[data-layout-mode=dark] .btn-check:focus+.btn-outline-dark,
[data-layout-mode=dark] .btn-check:focus+.btn-soft-dark,
[data-layout-mode=dark] .btn-dark:focus,
[data-layout-mode=dark] .btn-outline-dark:focus,
[data-layout-mode=dark] .btn-soft-dark:focus {
    color: var(--vz-light);
    background-color: rgba(var(--vz-dark-rgb), .75);
    border-color: rgba(var(--vz-dark-rgb), .75)
}

[data-layout-mode=dark] .btn-check:active+.btn-dark,
[data-layout-mode=dark] .btn-check:active+.btn-outline-dark,
[data-layout-mode=dark] .btn-check:active+.btn-soft-dark,
[data-layout-mode=dark] .btn-check:checked+.btn-dark,
[data-layout-mode=dark] .btn-check:checked+.btn-outline-dark,
[data-layout-mode=dark] .btn-check:checked+.btn-soft-dark,
[data-layout-mode=dark] .btn-dark.active,
[data-layout-mode=dark] .btn-dark:active,
[data-layout-mode=dark] .btn-outline-dark.active,
[data-layout-mode=dark] .btn-outline-dark:active,
[data-layout-mode=dark] .btn-soft-dark.active,
[data-layout-mode=dark] .btn-soft-dark:active,
[data-layout-mode=dark] .show>.btn-dark.dropdown-toggle,
[data-layout-mode=dark] .show>.btn-outline-dark.dropdown-toggle,
[data-layout-mode=dark] .show>.btn-soft-dark.dropdown-toggle {
    color: var(--vz-light);
    background-color: rgba(var(--vz-dark-rgb), .75);
    border-color: rgba(var(--vz-dark-rgb), .75)
}

[data-layout-mode=dark] .btn-outline-light {
    color: var(--vz-dark);
    border-color: var(--vz-light)
}

[data-layout-mode=dark] .btn-outline-light:hover {
    color: var(--vz-dark);
    background-color: rgba(var(--vz-light-rgb), .75);
    border-color: rgba(var(--vz-light-rgb), .75)
}

[data-layout-mode=dark] .btn-outline-dark {
    color: var(--vz-dark);
    border-color: var(--vz-dark)
}

[data-layout-mode=dark] .btn-outline-dark:hover {
    color: var(--vz-light);
    background-color: rgba(var(--vz-dark-rgb), .75);
    border-color: rgba(var(--vz-dark-rgb), .75)
}

[data-layout-mode=dark] .btn-soft-light {
    color: var(--vz-dark);
    background-color: rgba(var(--vz-light-rgb), .1)
}

[data-layout-mode=dark] .btn-soft-light:hover {
    color: var(--vz-dark);
    background-color: var(--vz-light)
}

[data-layout-mode=dark] .btn-soft-dark {
    color: var(--vz-dark);
    background-color: rgba(var(--vz-dark-rgb), .1)
}

[data-layout-mode=dark] .btn-soft-dark:hover {
    color: var(--vz-light);
    background-color: var(--vz-dark)
}

[data-layout-mode=dark] .btn-ghost-light {
    color: var(--vz-dark)
}

[data-layout-mode=dark] .btn-ghost-light:active,
[data-layout-mode=dark] .btn-ghost-light:focus,
[data-layout-mode=dark] .btn-ghost-light:hover {
    color: var(--vz-dark);
    background-color: rgba(var(--vz-light-rgb), .5)
}

[data-layout-mode=dark] .btn-ghost-dark {
    color: var(--vz-dark)
}

[data-layout-mode=dark] .btn-ghost-dark:active,
[data-layout-mode=dark] .btn-ghost-dark:focus,
[data-layout-mode=dark] .btn-ghost-dark:hover {
    color: var(--vz-dark);
    background-color: rgba(var(--vz-dark-rgb), .1)
}

.btn-load .spinner-border,
.btn-load .spinner-grow {
    height: 19px;
    width: 19px
}

.btn-outline-primary.btn-border,
.btn-primary.btn-border,
.btn-soft-primary.btn-border {
    border-bottom: 2px solid #3e2e94
}

.btn-outline-secondary.btn-border,
.btn-secondary.btn-border,
.btn-soft-secondary.btn-border {
    border-bottom: 2px solid #105eed
}

.btn-outline-success.btn-border,
.btn-soft-success.btn-border,
.btn-success.btn-border {
    border-bottom: 2px solid #33b571
}

.btn-info.btn-border,
.btn-outline-info.btn-border,
.btn-soft-info.btn-border {
    border-bottom: 2px solid #2084bc
}

.btn-outline-warning.btn-border,
.btn-soft-warning.btn-border,
.btn-warning.btn-border {
    border-bottom: 2px solid #e1a500
}

.btn-danger.btn-border,
.btn-outline-danger.btn-border,
.btn-soft-danger.btn-border {
    border-bottom: 2px solid #ed4522
}

.btn-light.btn-border,
.btn-outline-light.btn-border,
.btn-soft-light.btn-border {
    border-bottom: 2px solid #d8e2eb
}

.btn-dark.btn-border,
.btn-outline-dark.btn-border,
.btn-soft-dark.btn-border {
    border-bottom: 2px solid #0f1112
}

.breadcrumb-item>a {
    color: var(--vz-gray-700)
}

.breadcrumb-item+.breadcrumb-item::before {
    font-family: "Material Design Icons";
    font-size: 15px;
    line-height: 20px
}

.card {
    margin-bottom: 1.5rem;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.card-header {
    border-bottom: 1px solid var(--vz-border-color)
}

.card-header-dropdown .dropdown-btn {
    padding: 1rem 0
}

.card-footer {
    border-top: 1px solid var(--vz-border-color)
}

.card-drop {
    color: #212529
}

.card-title {
    font-size: 16px;
    margin: 0 0 7px 0
}

.card-title-desc {
    margin-bottom: 24px
}

.card-height-100 {
    height: calc(100% - 1.5rem)
}

.card-animate {
    -webkit-transition: all .4s;
    transition: all .4s
}

.card-animate:hover {
    -webkit-transform: translateY(calc(-1.5rem / 5));
    transform: translateY(calc(-1.5rem / 5));
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12)
}

.card-primary {
    color: #fff;
    background-color: #4b38b3
}

.card-primary .card-footer,
.card-primary .card-header {
    background-color: rgba(255, 255, 255, .1);
    color: #fff;
    border-color: transparent
}

.card-primary .card-title {
    color: #fff
}

.card-secondary {
    color: #fff;
    background-color: #3577f1
}

.card-secondary .card-footer,
.card-secondary .card-header {
    background-color: rgba(255, 255, 255, .1);
    color: #fff;
    border-color: transparent
}

.card-secondary .card-title {
    color: #fff
}

.card-success {
    color: #fff;
    background-color: #45cb85
}

.card-success .card-footer,
.card-success .card-header {
    background-color: rgba(255, 255, 255, .1);
    color: #fff;
    border-color: transparent
}

.card-success .card-title {
    color: #fff
}

.card-info {
    color: #fff;
    background-color: #299cdb
}

.card-info .card-footer,
.card-info .card-header {
    background-color: rgba(255, 255, 255, .1);
    color: #fff;
    border-color: transparent
}

.card-info .card-title {
    color: #fff
}

.card-warning {
    color: #fff;
    background-color: #ffbe0b
}

.card-warning .card-footer,
.card-warning .card-header {
    background-color: rgba(255, 255, 255, .1);
    color: #fff;
    border-color: transparent
}

.card-warning .card-title {
    color: #fff
}

.card-danger {
    color: #fff;
    background-color: #f06548
}

.card-danger .card-footer,
.card-danger .card-header {
    background-color: rgba(255, 255, 255, .1);
    color: #fff;
    border-color: transparent
}

.card-danger .card-title {
    color: #fff
}

.card-light {
    color: #fff;
    background-color: #f3f6f9
}

.card-light .card-footer,
.card-light .card-header {
    background-color: rgba(255, 255, 255, .1);
    color: #fff;
    border-color: transparent
}

.card-light .card-title {
    color: #fff
}

.card-dark {
    color: #fff;
    background-color: #212529
}

.card-dark .card-footer,
.card-dark .card-header {
    background-color: rgba(255, 255, 255, .1);
    color: #fff;
    border-color: transparent
}

.card-dark .card-title {
    color: #fff
}

.card-border-primary {
    border-color: #4b38b3 !important
}

.card-border-primary .card-footer,
.card-border-primary .card-header {
    border-color: #4b38b3
}

.card-border-secondary {
    border-color: #3577f1 !important
}

.card-border-secondary .card-footer,
.card-border-secondary .card-header {
    border-color: #3577f1
}

.card-border-success {
    border-color: #45cb85 !important
}

.card-border-success .card-footer,
.card-border-success .card-header {
    border-color: #45cb85
}

.card-border-info {
    border-color: #299cdb !important
}

.card-border-info .card-footer,
.card-border-info .card-header {
    border-color: #299cdb
}

.card-border-warning {
    border-color: #ffbe0b !important
}

.card-border-warning .card-footer,
.card-border-warning .card-header {
    border-color: #ffbe0b
}

.card-border-danger {
    border-color: #f06548 !important
}

.card-border-danger .card-footer,
.card-border-danger .card-header {
    border-color: #f06548
}

.card-border-light {
    border-color: #f3f6f9 !important
}

.card-border-light .card-footer,
.card-border-light .card-header {
    border-color: #f3f6f9
}

.card-border-dark {
    border-color: #212529 !important
}

.card-border-dark .card-footer,
.card-border-dark .card-header {
    border-color: #212529
}

.card-light {
    background-color: var(--vz-light)
}

.card-light .card-footer,
.card-light .card-header {
    color: var(--vz-body-color) !important;
    background-color: rgba(var(--vz-dark-rgb), .1)
}

.card-light .card-text,
.card-light .card-title {
    color: var(--vz-body-color) !important
}

.stretched-link {
    position: static
}

.card-preloader {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(var(--vz-light-rgb), .6);
    z-index: 9999
}

.card-status {
    width: 40px;
    height: 40px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%) !important;
    transform: translate(-50%, -50%) !important
}

.custom-loader {
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite
}

@-webkit-keyframes spin {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes spin {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

.card-overlay {
    position: relative;
    overflow: hidden
}

.card-overlay:before {
    content: "";
    background-color: rgba(75, 56, 179, .2);
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0
}

.card-overlay .card-footer,
.card-overlay .card-header {
    border-color: rgba(255, 255, 255, .15) !important
}

.card-toolbar-menu {
    line-height: .8
}

.card-toolbar-menu a {
    font-size: 17px
}

.card-toolbar-menu .minimize-card .plus {
    display: none
}

.card-toolbar-menu .minimize-card .minus {
    display: block
}

.card-toolbar-menu .minimize-card.collapsed .plus {
    display: block
}

.card-toolbar-menu .minimize-card.collapsed .minus {
    display: none
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: .255em;
    font-size: 15px;
    line-height: 15px;
    content: "\f0140";
    font-family: "Material Design Icons"
}

.dropdown-menu {
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    -webkit-animation-name: DropDownSlide;
    animation-name: DropDownSlide;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    position: absolute;
    z-index: 1000
}

.dropdown-menu.dropdown-megamenu {
    padding: 20px;
    left: 0 !important;
    right: 0 !important
}

.dropdown-menu[data-popper-placement=top-start] {
    -webkit-animation-name: DropDownSlideDown;
    animation-name: DropDownSlideDown
}

@-webkit-keyframes DropDownSlide {
    100% {
        margin-top: -1px
    }

    0% {
        margin-top: 8px
    }
}

@keyframes DropDownSlide {
    100% {
        margin-top: -1px
    }

    0% {
        margin-top: 8px
    }
}

@-webkit-keyframes DropDownSlideDown {
    100% {
        margin-bottom: 0
    }

    0% {
        margin-bottom: 8px
    }
}

@keyframes DropDownSlideDown {
    100% {
        margin-bottom: 0
    }

    0% {
        margin-bottom: 8px
    }
}

@media (min-width:600px) {
    .dropdown-menu-xl {
        width: 420px
    }

    .dropdown-menu-lg {
        width: 320px
    }

    .dropdown-menu-md {
        width: 240px
    }
}

.dropdown-toggle-split {
    border-left: none
}

.dropdown-toggle-split::after {
    margin-left: 0
}

.dropdown-toggle-split:before {
    content: "";
    position: absolute;
    background-color: rgba(255, 255, 255, .12);
    top: -1px;
    bottom: -1px;
    right: -1px;
    left: 0;
    border-radius: 0 .25rem .25rem 0
}

.dropdown-mega {
    position: static !important
}

.dropdown-mega-menu-xl {
    width: 38rem
}

.dropdown-mega-menu-lg {
    width: 26rem
}

[dir=ltr] .dropdown-menu-start {
    --bs-position: end
}

[dir=ltr] .dropdown-menu-end {
    --bs-position: start
}

.dropdown-head .nav-tabs-custom {
    border: 0
}

.dropdown-head .nav-tabs-custom .nav-link {
    color: rgba(255, 255, 255, .6)
}

.dropdown-head .nav-tabs-custom .nav-link.active {
    background-color: var(--vz-dropdown-bg)
}

.dropdown-head .nav-tabs-custom .nav-link:hover {
    color: #fff
}

.dropdownmenu-primary .dropdown-item.active,
.dropdownmenu-primary .dropdown-item:focus,
.dropdownmenu-primary .dropdown-item:hover {
    background-color: rgba(75, 56, 179, .07);
    color: #4b38b3
}

.dropdownmenu-secondary .dropdown-item.active,
.dropdownmenu-secondary .dropdown-item:focus,
.dropdownmenu-secondary .dropdown-item:hover {
    background-color: rgba(53, 119, 241, .07);
    color: #3577f1
}

.dropdownmenu-success .dropdown-item.active,
.dropdownmenu-success .dropdown-item:focus,
.dropdownmenu-success .dropdown-item:hover {
    background-color: rgba(69, 203, 133, .07);
    color: #45cb85
}

.dropdownmenu-info .dropdown-item.active,
.dropdownmenu-info .dropdown-item:focus,
.dropdownmenu-info .dropdown-item:hover {
    background-color: rgba(41, 156, 219, .07);
    color: #299cdb
}

.dropdownmenu-warning .dropdown-item.active,
.dropdownmenu-warning .dropdown-item:focus,
.dropdownmenu-warning .dropdown-item:hover {
    background-color: rgba(255, 190, 11, .07);
    color: #ffbe0b
}

.dropdownmenu-danger .dropdown-item.active,
.dropdownmenu-danger .dropdown-item:focus,
.dropdownmenu-danger .dropdown-item:hover {
    background-color: rgba(240, 101, 72, .07);
    color: #f06548
}

.dropdownmenu-light .dropdown-item.active,
.dropdownmenu-light .dropdown-item:focus,
.dropdownmenu-light .dropdown-item:hover {
    background-color: rgba(243, 246, 249, .07);
    color: #f3f6f9
}

.dropdownmenu-dark .dropdown-item.active,
.dropdownmenu-dark .dropdown-item:focus,
.dropdownmenu-dark .dropdown-item:hover {
    background-color: rgba(33, 37, 41, .07);
    color: #212529
}

.nav-pills>li>a,
.nav-tabs>li>a {
    color: var(--vz-gray-700);
    font-weight: 500
}

.nav-pills>a {
    color: var(--vz-gray-700);
    font-weight: 500
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.nav-tabs-custom {
    border-bottom: 1px solid var(--vz-gray-300)
}

.nav-tabs-custom .nav-item {
    position: relative
}

.nav-tabs-custom .nav-item .nav-link {
    border: none;
    font-weight: 500
}

.nav-tabs-custom .nav-item .nav-link::after {
    content: "";
    background: #4b38b3;
    height: 1px;
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    -webkit-transition: all 250ms ease 0s;
    transition: all 250ms ease 0s;
    -webkit-transform: scale(0);
    transform: scale(0)
}

.nav-tabs-custom .nav-item .nav-link.active {
    color: #4b38b3
}

.nav-tabs-custom .nav-item .nav-link.active:after {
    -webkit-transform: scale(1);
    transform: scale(1)
}

.nav-tabs-custom.card-header-tabs {
    margin-top: -1rem
}

.nav-tabs-custom.card-header-tabs .nav-link {
    padding: 1rem 1rem
}

.vertical-nav .nav .nav-link {
    padding: 24px 16px;
    text-align: center;
    margin-bottom: 8px
}

.vertical-nav .nav .nav-link .nav-icon {
    font-size: 24px
}

.navtab-bg li>a {
    background-color: #e9ebec;
    margin: 0 5px
}

.arrow-navtabs .nav-item .nav-link {
    position: relative;
    text-align: center
}

.arrow-navtabs .nav-item .nav-link:before {
    content: "";
    position: absolute;
    border: 6px solid transparent;
    bottom: -12px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    -webkit-transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .arrow-navtabs .nav-item .nav-link:before {
        -webkit-transition: none;
        transition: none
    }
}

.arrow-navtabs .nav-item .nav-link.active:before {
    border-top-color: #4b38b3
}

.custom-hover-nav-tabs .nav-item {
    text-align: center;
    overflow: hidden
}

.custom-hover-nav-tabs .nav-item .nav-link {
    width: 120px;
    height: 45px;
    position: relative;
    border-radius: 0
}

.custom-hover-nav-tabs .nav-item .nav-link .nav-icon {
    font-size: 22px
}

.custom-hover-nav-tabs .nav-item .nav-link .nav-titl {
    font-size: 14px
}

.custom-hover-nav-tabs .nav-item .nav-link.active .nav-tab-position {
    color: #fff
}

.custom-hover-nav-tabs .nav-item .nav-link .nav-tab-position {
    position: absolute;
    left: 0;
    right: 0;
    -webkit-transition: all .4s;
    transition: all .4s
}

.custom-hover-nav-tabs .nav-item .nav-link .nav-tab-position.nav-icon {
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.custom-hover-nav-tabs .nav-item .nav-link .nav-tab-position.nav-titl {
    bottom: -20px
}

.custom-hover-nav-tabs .nav-item .nav-link:hover .nav-titl {
    bottom: 50%;
    -webkit-transform: translateY(50%);
    transform: translateY(50%)
}

.custom-hover-nav-tabs .nav-item .nav-link:hover .nav-icon {
    top: -20px
}

.custom-verti-nav-pills .nav-link {
    background-color: var(--vz-light);
    margin-top: 7px;
    position: relative
}

@media (min-width:992px) {
    .custom-verti-nav-pills .nav-link::before {
        content: "";
        position: absolute;
        right: -20px;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        border: 12px solid transparent;
        border-left-color: transparent;
        -webkit-transition: border-left-color .04 ease;
        transition: border-left-color .04 ease
    }

    .custom-verti-nav-pills .nav-link.active::before {
        border-left-color: #4b38b3
    }
}

.animation-nav li {
    position: relative
}

.animation-nav li a {
    color: var(--vz-body-color);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    -webkit-transition: all .4s;
    transition: all .4s;
    z-index: 1
}

.animation-nav li a span {
    position: relative
}

.animation-nav li a::before {
    content: "";
    position: absolute;
    top: 0;
    width: 0;
    right: 0;
    height: 100%;
    -webkit-transition: width .4s cubic-bezier(.51, .18, 0, .88) .1s;
    transition: width .4s cubic-bezier(.51, .18, 0, .88) .1s;
    background-color: #4b38b3;
    z-index: -1
}

.animation-nav li a.active,
.animation-nav li a:hover {
    color: #fff;
    background-color: transparent !important
}

.animation-nav li a.active::before,
.animation-nav li a:hover::before {
    width: 100%;
    left: 0
}

.nav-border-top .nav-link {
    border-top: 3px solid transparent
}

.nav-border-top .nav-link.active {
    border-top-color: #4b38b3
}

.nav-border-top-primary .nav-link.active {
    color: #4b38b3;
    border-top-color: #4b38b3
}

.nav-border-top-secondary .nav-link.active {
    color: #3577f1;
    border-top-color: #3577f1
}

.nav-border-top-success .nav-link.active {
    color: #45cb85;
    border-top-color: #45cb85
}

.nav-border-top-info .nav-link.active {
    color: #299cdb;
    border-top-color: #299cdb
}

.nav-border-top-warning .nav-link.active {
    color: #ffbe0b;
    border-top-color: #ffbe0b
}

.nav-border-top-danger .nav-link.active {
    color: #f06548;
    border-top-color: #f06548
}

.nav-border-top-light .nav-link.active {
    color: #f3f6f9;
    border-top-color: #f3f6f9
}

.nav-border-top-dark .nav-link.active {
    color: #212529;
    border-top-color: #212529
}

.nav-custom {
    background-color: #4b38b3;
    border-radius: .25rem
}

.nav-custom .nav-item .nav-link {
    color: rgba(255, 255, 255, .75)
}

.nav-custom .nav-item .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, .1)
}

.nav-custom.nav-custom-light .nav-item .nav-link {
    color: rgba(var(--vz-dark-rgb), .75)
}

.nav-custom.nav-custom-light .nav-item .nav-link.active {
    color: #f3f6f9;
    background-color: #4b38b3
}

.nav-custom-primary {
    background-color: #4b38b3
}

.nav-custom-secondary {
    background-color: #3577f1
}

.nav-custom-success {
    background-color: #45cb85
}

.nav-custom-info {
    background-color: #299cdb
}

.nav-custom-warning {
    background-color: #ffbe0b
}

.nav-custom-danger {
    background-color: #f06548
}

.nav-custom-light {
    background-color: #f3f6f9
}

.nav-custom-dark {
    background-color: #212529
}

.nav-custom-light {
    background-color: var(--vz-light)
}

.nav-primary .nav-link.active {
    color: #fff;
    background-color: #4b38b3
}

.nav-primary.nav-tabs .nav-link.active {
    color: #4b38b3;
    background-color: var(--vz-card-bg)
}

.nav-primary.nav-tabs-custom .nav-link.active {
    color: #4b38b3;
    background-color: var(--vz-card-bg)
}

.nav-primary.nav-tabs-custom .nav-link.active::after {
    background-color: #4b38b3
}

.nav-primary.arrow-navtabs .nav-link.active::before {
    border-top-color: #4b38b3
}

.nav-primary.custom-verti-nav-pills .nav-link.active::before {
    border-left-color: #4b38b3
}

.nav-secondary .nav-link.active {
    color: #fff;
    background-color: #3577f1
}

.nav-secondary.nav-tabs .nav-link.active {
    color: #3577f1;
    background-color: var(--vz-card-bg)
}

.nav-secondary.nav-tabs-custom .nav-link.active {
    color: #3577f1;
    background-color: var(--vz-card-bg)
}

.nav-secondary.nav-tabs-custom .nav-link.active::after {
    background-color: #3577f1
}

.nav-secondary.arrow-navtabs .nav-link.active::before {
    border-top-color: #3577f1
}

.nav-secondary.custom-verti-nav-pills .nav-link.active::before {
    border-left-color: #3577f1
}

.nav-success .nav-link.active {
    color: #fff;
    background-color: #45cb85
}

.nav-success.nav-tabs .nav-link.active {
    color: #45cb85;
    background-color: var(--vz-card-bg)
}

.nav-success.nav-tabs-custom .nav-link.active {
    color: #45cb85;
    background-color: var(--vz-card-bg)
}

.nav-success.nav-tabs-custom .nav-link.active::after {
    background-color: #45cb85
}

.nav-success.arrow-navtabs .nav-link.active::before {
    border-top-color: #45cb85
}

.nav-success.custom-verti-nav-pills .nav-link.active::before {
    border-left-color: #45cb85
}

.nav-info .nav-link.active {
    color: #fff;
    background-color: #299cdb
}

.nav-info.nav-tabs .nav-link.active {
    color: #299cdb;
    background-color: var(--vz-card-bg)
}

.nav-info.nav-tabs-custom .nav-link.active {
    color: #299cdb;
    background-color: var(--vz-card-bg)
}

.nav-info.nav-tabs-custom .nav-link.active::after {
    background-color: #299cdb
}

.nav-info.arrow-navtabs .nav-link.active::before {
    border-top-color: #299cdb
}

.nav-info.custom-verti-nav-pills .nav-link.active::before {
    border-left-color: #299cdb
}

.nav-warning .nav-link.active {
    color: #fff;
    background-color: #ffbe0b
}

.nav-warning.nav-tabs .nav-link.active {
    color: #ffbe0b;
    background-color: var(--vz-card-bg)
}

.nav-warning.nav-tabs-custom .nav-link.active {
    color: #ffbe0b;
    background-color: var(--vz-card-bg)
}

.nav-warning.nav-tabs-custom .nav-link.active::after {
    background-color: #ffbe0b
}

.nav-warning.arrow-navtabs .nav-link.active::before {
    border-top-color: #ffbe0b
}

.nav-warning.custom-verti-nav-pills .nav-link.active::before {
    border-left-color: #ffbe0b
}

.nav-danger .nav-link.active {
    color: #fff;
    background-color: #f06548
}

.nav-danger.nav-tabs .nav-link.active {
    color: #f06548;
    background-color: var(--vz-card-bg)
}

.nav-danger.nav-tabs-custom .nav-link.active {
    color: #f06548;
    background-color: var(--vz-card-bg)
}

.nav-danger.nav-tabs-custom .nav-link.active::after {
    background-color: #f06548
}

.nav-danger.arrow-navtabs .nav-link.active::before {
    border-top-color: #f06548
}

.nav-danger.custom-verti-nav-pills .nav-link.active::before {
    border-left-color: #f06548
}

.nav-light .nav-link.active {
    color: #fff;
    background-color: #f3f6f9
}

.nav-light.nav-tabs .nav-link.active {
    color: #f3f6f9;
    background-color: var(--vz-card-bg)
}

.nav-light.nav-tabs-custom .nav-link.active {
    color: #f3f6f9;
    background-color: var(--vz-card-bg)
}

.nav-light.nav-tabs-custom .nav-link.active::after {
    background-color: #f3f6f9
}

.nav-light.arrow-navtabs .nav-link.active::before {
    border-top-color: #f3f6f9
}

.nav-light.custom-verti-nav-pills .nav-link.active::before {
    border-left-color: #f3f6f9
}

.nav-dark .nav-link.active {
    color: #fff;
    background-color: #212529
}

.nav-dark.nav-tabs .nav-link.active {
    color: #212529;
    background-color: var(--vz-card-bg)
}

.nav-dark.nav-tabs-custom .nav-link.active {
    color: #212529;
    background-color: var(--vz-card-bg)
}

.nav-dark.nav-tabs-custom .nav-link.active::after {
    background-color: #212529
}

.nav-dark.arrow-navtabs .nav-link.active::before {
    border-top-color: #212529
}

.nav-dark.custom-verti-nav-pills .nav-link.active::before {
    border-left-color: #212529
}

.progress-nav {
    position: relative;
    margin-right: 1rem;
    margin-left: 1rem
}

.progress-nav .progress {
    position: absolute;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 100%
}

.progress-nav .nav {
    margin-right: -1rem;
    margin-left: -1rem;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.progress-nav .nav .nav-link {
    width: 2rem;
    height: 2rem;
    background-color: var(--vz-light);
    padding: 0;
    color: var(--vz-dark);
    font-weight: 500
}

.progress-nav .nav .nav-link.active,
.progress-nav .nav .nav-link.done {
    background-color: #4b38b3;
    color: #fff
}

.step-arrow-nav .nav {
    background-color: var(--vz-light)
}

.step-arrow-nav .nav .nav-link {
    border-radius: 0;
    position: relative;
    font-weight: 500;
    color: var(--vz-dark)
}

.step-arrow-nav .nav .nav-link::before {
    content: "";
    position: absolute;
    border: 7px solid transparent;
    right: -14px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.step-arrow-nav .nav .nav-link.done {
    background-color: rgba(75, 56, 179, .05);
    color: #4b38b3
}

.step-arrow-nav .nav .nav-link.done::before {
    border-left-color: transparent
}

.step-arrow-nav .nav .nav-link.active {
    background-color: rgba(75, 56, 179, .1);
    color: #4b38b3;
    -webkit-box-shadow: none;
    box-shadow: none
}

.step-arrow-nav .nav .nav-link.active::before {
    border-left-color: rgba(75, 56, 179, .1)
}

.step-arrow-nav .nav .nav-item:last-child .nav-link:before {
    display: none
}

.vertical-navs-step .nav {
    gap: 16px
}

.vertical-navs-step .nav .nav-link {
    text-align: left;
    background-color: rgba(var(--vz-light-rgb), .4);
    border: 1px solid var(--vz-border-color);
    color: var(--vz-dark)
}

.vertical-navs-step .nav .nav-link .step-title {
    font-weight: 600
}

.vertical-navs-step .nav .nav-link .step-icon {
    color: #f06548;
    vertical-align: middle;
    font-weight: 500;
    float: left
}

.vertical-navs-step .nav .nav-link.active .step-icon,
.vertical-navs-step .nav .nav-link.done .step-icon {
    color: #45cb85
}

.vertical-navs-step .nav .nav-link.active .step-icon:before,
.vertical-navs-step .nav .nav-link.done .step-icon:before {
    content: "\eb80"
}

.vertical-navs-step .nav .nav-link.active {
    border-color: #4b38b3
}

.vertical-navs-step .nav .nav-link.done {
    border-color: #45cb85
}

.nav-custom-outline.nav .nav-link {
    border: 1px solid transparent;
    border-bottom: 2px solid transparent
}

.nav-custom-outline.nav .nav-link.active {
    border-color: #4b38b3;
    background-color: transparent;
    color: #4b38b3
}

.nav-primary.nav-custom-outline .nav-link.active {
    color: #4b38b3;
    border-color: #4b38b3
}

.nav-secondary.nav-custom-outline .nav-link.active {
    color: #3577f1;
    border-color: #3577f1
}

.nav-success.nav-custom-outline .nav-link.active {
    color: #45cb85;
    border-color: #45cb85
}

.nav-info.nav-custom-outline .nav-link.active {
    color: #299cdb;
    border-color: #299cdb
}

.nav-warning.nav-custom-outline .nav-link.active {
    color: #ffbe0b;
    border-color: #ffbe0b
}

.nav-danger.nav-custom-outline .nav-link.active {
    color: #f06548;
    border-color: #f06548
}

.nav-light.nav-custom-outline .nav-link.active {
    color: #f3f6f9;
    border-color: #f3f6f9
}

.nav-dark.nav-custom-outline .nav-link.active {
    color: #212529;
    border-color: #212529
}

.nav-customs.nav {
    padding-left: 34px;
    overflow: hidden
}

.nav-customs.nav .nav-link {
    position: relative;
    display: block;
    float: right;
    background-color: var(--vz-body-bg);
    margin-right: 46px;
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

.nav-customs.nav .nav-link::after,
.nav-customs.nav .nav-link::before {
    display: block;
    content: " ";
    position: absolute;
    top: -1px;
    bottom: -1px;
    width: 37px;
    background-color: var(--vz-body-bg);
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

.nav-customs.nav .nav-link::before {
    border-radius: 0 8px 0 0;
    right: -24px;
    -webkit-transform: skew(30deg, 0deg);
    transform: skew(30deg, 0deg)
}

.nav-customs.nav .nav-link::after {
    border-radius: 8px 0 0 0;
    left: -24px;
    -webkit-transform: skew(-30deg, 0deg);
    transform: skew(-30deg, 0deg)
}

.nav-customs.nav .nav-link.active,
.nav-customs.nav .nav-link.active:after,
.nav-customs.nav .nav-link.active:before {
    background-color: #4b38b3;
    color: #fff
}

.nav-customs.nav .nav-link.active {
    z-index: 1
}

.table th {
    font-weight: 600
}

.table .table-light {
    color: #212529;
    border-color: var(--vz-border-color);
    background-color: var(--vz-light)
}

.table>thead {
    border-color: var(--vz-border-color)
}

.table>:not(:first-child) {
    border-top-width: 1px
}

.table-nowrap td,
.table-nowrap th {
    white-space: nowrap
}

.table-card {
    margin: -1rem -1rem
}

.table-card td:first-child,
.table-card th:first-child {
    padding-left: 16px
}

.table-card td:last-child,
.table-card th:last-child {
    padding-right: 16px
}

.table-card .table>:not(:first-child) {
    border-top-width: 1px
}

.border-primary.table>thead {
    border-color: #4b38b3 !important
}

.table-primary.table>thead {
    border-bottom-color: #6654ca !important
}

.border-secondary.table>thead {
    border-color: #3577f1 !important
}

.table-secondary.table>thead {
    border-bottom-color: #6597f4 !important
}

.border-success.table>thead {
    border-color: #45cb85 !important
}

.table-success.table>thead {
    border-bottom-color: #6dd69f !important
}

.border-info.table>thead {
    border-color: #299cdb !important
}

.table-info.table>thead {
    border-bottom-color: #55b0e2 !important
}

.border-warning.table>thead {
    border-color: #ffbe0b !important
}

.table-warning.table>thead {
    border-bottom-color: #ffcc3e !important
}

.border-danger.table>thead {
    border-color: #f06548 !important
}

.table-danger.table>thead {
    border-bottom-color: #f48d77 !important
}

.border-light.table>thead {
    border-color: #f3f6f9 !important
}

.table-light.table>thead {
    border-bottom-color: #fff !important
}

.border-dark.table>thead {
    border-color: #212529 !important
}

.table-dark.table>thead {
    border-bottom-color: #383f45 !important
}

.table .form-check {
    padding-left: 0;
    margin-bottom: 0
}

.table .form-check .form-check-input {
    margin-left: 0;
    margin-top: 0;
    float: none;
    vertical-align: middle
}

.table .sort {
    position: relative
}

.table .sort::before {
    content: "\f035d";
    position: absolute;
    right: .5rem;
    top: 18px;
    font-size: .8rem;
    font-family: "Material Design Icons"
}

.table .sort::after {
    position: absolute;
    right: .5rem;
    content: "\f0360";
    font-family: "Material Design Icons";
    font-size: .8rem;
    top: 12px
}

[data-layout-mode=dark] .table-light {
    background-color: var(--vz-light);
    color: var(--vz-body-color)
}

[data-layout-mode=dark] .table-light td,
[data-layout-mode=dark] .table-light th {
    background-color: var(--vz-light)
}

.modal-title {
    font-weight: 600
}

.modal-dialog:not(.modal-dialog-scrollable) .modal-header {
    padding-bottom: 0
}

.modal-dialog:not(.modal-dialog-scrollable) .modal-header .btn-close {
    margin-top: -1.25rem 1.25rem
}

.modal-dialog:not(.modal-dialog-scrollable) .modal-footer {
    padding-top: 0
}

.modal.fadeInRight .modal-dialog {
    opacity: 0;
    -webkit-transform: translateX(20%);
    transform: translateX(20%);
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.modal.fadeInRight.show .modal-dialog {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0)
}

.modal.fadeInLeft .modal-dialog {
    -webkit-animation: fadeInLeft .3s ease-in-out;
    animation: fadeInLeft .3s ease-in-out;
    -webkit-transform: translate(-50%, 0);
    transform: translate(-50%, 0)
}

.modal.fadeInLeft.show .modal-dialog {
    -webkit-transform: none;
    transform: none
}

.modal.fadeInUp .modal-dialog {
    -webkit-animation: fadeInUp .3s ease-in-out;
    animation: fadeInUp .3s ease-in-out;
    -webkit-transform: translate(0, 30%);
    transform: translate(0, 30%)
}

.modal.fadeInUp.show .modal-dialog {
    -webkit-transform: none;
    transform: none
}

.modal.flip {
    -webkit-perspective: 1300px;
    perspective: 1300px
}

.modal.flip .modal-dialog {
    opacity: 0;
    -webkit-transform: rotateY(-70deg);
    transform: rotateY(-70deg);
    -webkit-transition: all .3s;
    transition: all .3s
}

.modal.flip.show .modal-dialog {
    opacity: 1;
    -webkit-transform: rotateY(0);
    transform: rotateY(0)
}

.modal.zoomIn .modal-dialog {
    opacity: 0;
    -webkit-transform: scale(.7);
    transform: scale(.7);
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}

.modal.zoomIn.show .modal-dialog {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.modal-dialog-right {
    margin-right: 1.75rem
}

.modal-dialog-bottom {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    min-height: calc(100% - 1.75rem)
}

@media (min-width:576px) {
    .modal-dialog-bottom {
        min-height: calc(100% - 3.5rem)
    }
}

.modal-dialog-bottom-right {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    min-height: calc(100% - 1.75rem);
    margin-right: 1.75rem
}

@media (min-width:576px) {
    .modal-dialog-bottom-right {
        min-height: calc(100% - 3.5rem)
    }
}

@-webkit-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-30%, 0, 0);
        transform: translate3d(-30%, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-30%, 0, 0);
        transform: translate3d(-30%, 0, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 30%, 0);
        transform: translate3d(0, 30%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 30%, 0);
        transform: translate3d(0, 30%, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }

    50% {
        opacity: 1
    }
}

@keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }

    50% {
        opacity: 1
    }
}

.login-modal {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1016%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(64%2c 81%2c 137%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c650.704C122.328%2c648.746%2c159.175%2c473.043%2c255.674%2c397.837C339.724%2c332.333%2c461.529%2c324.924%2c526.449%2c240.421C598.428%2c146.73%2c655.546%2c24.847%2c631.015%2c-90.726C606.666%2c-205.444%2c482.926%2c-263.497%2c401.565%2c-347.958C325.215%2c-427.217%2c275.543%2c-549.012%2c167.826%2c-571.563C60.344%2c-594.065%2c-27.703%2c-482.932%2c-135.163%2c-460.325C-256.336%2c-434.833%2c-401.929%2c-509.651%2c-497.972%2c-431.495C-592.807%2c-354.321%2c-579.865%2c-206.886%2c-595.603%2c-85.635C-611.133%2c34.016%2c-656.761%2c169.183%2c-588.884%2c268.934C-520.854%2c368.909%2c-362.458%2c340.324%2c-260.989%2c406.106C-158.875%2c472.306%2c-121.679%2c652.651%2c0%2c650.704' fill='%2333416e'%3e%3c/path%3e%3cpath d='M1440 995.672C1519.728 984.741 1563.12 899.779 1626.466 850.1469999999999 1682.6390000000001 806.135 1756.261 782.602 1791.2939999999999 720.431 1827.571 656.052 1835.537 577.6610000000001 1820.814 505.247 1806.518 434.933 1753.2640000000001 383.16999999999996 1710.941 325.228 1664.475 261.614 1634.992 175.16000000000003 1560.657 149.07999999999998 1485.96 122.87299999999999 1402.146 155.543 1332.03 192.289 1269.541 225.038 1232.754 287.251 1189.969 343.347 1149.925 395.849 1115.781 448.9 1089.96 509.672 1056 589.599 988.9680000000001 671.1659999999999 1015.557 753.837 1041.91 835.774 1142.714 863.61 1217.498 906.22 1288.388 946.611 1359.167 1006.755 1440 995.672' fill='%234d61a4'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1016'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
    background-size: cover;
    background-position: center
}

.pagination-separated .page-item .page-link {
    margin-left: .35rem;
    border-radius: .25rem
}

.pagination-rounded .page-link {
    border-radius: 30px !important;
    margin: 0 3px !important;
    border: none;
    min-width: 32px;
    min-height: 32px;
    text-align: center
}

.pagination-rounded.pagination-sm .page-link {
    min-width: 25px;
    min-height: 25px
}

.page-item.active .page-link {
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.progress-sm {
    height: 5px
}

.progress-lg {
    height: 12px
}

.progress-xl {
    height: 16px
}

.custom-progess {
    position: relative
}

.custom-progess .progress-icon {
    position: absolute;
    top: -12px
}

.custom-progess .progress-icon .avatar-title {
    background: var(--vz-card-bg)
}

.animated-progress {
    position: relative
}

.animated-progress .progress-bar {
    position: relative;
    border-radius: 6px;
    -webkit-animation: animate-positive 2s;
    animation: animate-positive 2s
}

@-webkit-keyframes animate-positive {
    0% {
        width: 0
    }
}

@keyframes animate-positive {
    0% {
        width: 0
    }
}

.custom-progress {
    height: 15px;
    padding: 4px;
    border-radius: 30px
}

.custom-progress .progress-bar {
    position: relative;
    border-radius: 30px
}

.custom-progress .progress-bar::before {
    content: "";
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: #fff;
    border-radius: 7px;
    right: 2px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.progress-label {
    overflow: visible
}

.progress-label .progress-bar {
    position: relative;
    overflow: visible
}

.progress-label .progress-bar .label {
    position: absolute;
    top: -25px;
    right: -9px;
    background-color: #4b38b3;
    color: #fff;
    display: inline-block;
    line-height: 18px;
    padding: 0 4px;
    border-radius: 4px
}

.progress-label .progress-bar .label:after {
    content: "";
    position: absolute;
    border: 4px solid transparent;
    border-top-color: #4b38b3;
    bottom: -7px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.progress-step-arrow {
    height: 3.25rem
}

.progress-step-arrow .progress-bar {
    position: relative;
    overflow: initial;
    font-size: .875rem;
    color: #fff
}

.progress-step-arrow .progress-bar::after {
    content: "";
    position: absolute;
    border: 10px solid transparent;
    bottom: 15px;
    right: -20px;
    z-index: 1
}

.progress-primary .progress-bar {
    background-color: #4b38b3
}

.progress-primary .progress-bar::after {
    border-left-color: #4b38b3
}

.progress-primary .progress-bar:nth-child(2) {
    background-color: rgba(75, 56, 179, .1) !important;
    color: #4b38b3 !important
}

.progress-primary .progress-bar:nth-child(2)::after {
    border-left-color: rgba(75, 56, 179, .1)
}

.progress-secondary .progress-bar {
    background-color: #3577f1
}

.progress-secondary .progress-bar::after {
    border-left-color: #3577f1
}

.progress-secondary .progress-bar:nth-child(2) {
    background-color: rgba(53, 119, 241, .1) !important;
    color: #3577f1 !important
}

.progress-secondary .progress-bar:nth-child(2)::after {
    border-left-color: rgba(53, 119, 241, .1)
}

.progress-success .progress-bar {
    background-color: #45cb85
}

.progress-success .progress-bar::after {
    border-left-color: #45cb85
}

.progress-success .progress-bar:nth-child(2) {
    background-color: rgba(69, 203, 133, .1) !important;
    color: #45cb85 !important
}

.progress-success .progress-bar:nth-child(2)::after {
    border-left-color: rgba(69, 203, 133, .1)
}

.progress-info .progress-bar {
    background-color: #299cdb
}

.progress-info .progress-bar::after {
    border-left-color: #299cdb
}

.progress-info .progress-bar:nth-child(2) {
    background-color: rgba(41, 156, 219, .1) !important;
    color: #299cdb !important
}

.progress-info .progress-bar:nth-child(2)::after {
    border-left-color: rgba(41, 156, 219, .1)
}

.progress-warning .progress-bar {
    background-color: #ffbe0b
}

.progress-warning .progress-bar::after {
    border-left-color: #ffbe0b
}

.progress-warning .progress-bar:nth-child(2) {
    background-color: rgba(255, 190, 11, .1) !important;
    color: #ffbe0b !important
}

.progress-warning .progress-bar:nth-child(2)::after {
    border-left-color: rgba(255, 190, 11, .1)
}

.progress-danger .progress-bar {
    background-color: #f06548
}

.progress-danger .progress-bar::after {
    border-left-color: #f06548
}

.progress-danger .progress-bar:nth-child(2) {
    background-color: rgba(240, 101, 72, .1) !important;
    color: #f06548 !important
}

.progress-danger .progress-bar:nth-child(2)::after {
    border-left-color: rgba(240, 101, 72, .1)
}

.progress-light .progress-bar {
    background-color: #f3f6f9
}

.progress-light .progress-bar::after {
    border-left-color: #f3f6f9
}

.progress-light .progress-bar:nth-child(2) {
    background-color: rgba(243, 246, 249, .1) !important;
    color: #f3f6f9 !important
}

.progress-light .progress-bar:nth-child(2)::after {
    border-left-color: rgba(243, 246, 249, .1)
}

.progress-dark .progress-bar {
    background-color: #212529
}

.progress-dark .progress-bar::after {
    border-left-color: #212529
}

.progress-dark .progress-bar:nth-child(2) {
    background-color: rgba(33, 37, 41, .1) !important;
    color: #212529 !important
}

.progress-dark .progress-bar:nth-child(2)::after {
    border-left-color: rgba(33, 37, 41, .1)
}

.popover {
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12)
}

.text-white-75 {
    color: rgba(255, 255, 255, .75) !important
}

.custom-blockquote.blockquote {
    padding: 16px;
    border-left: 3px solid
}

.custom-blockquote.blockquote.blockquote-outline {
    background-color: var(--vz-card-bg) !important;
    border: 1px solid;
    border-left: 3px solid
}

.custom-blockquote.blockquote.blockquote-primary {
    color: #4b38b3;
    border-color: #4b38b3;
    background-color: rgba(75, 56, 179, .15)
}

.custom-blockquote.blockquote.blockquote-primary .blockquote-footer {
    color: #4b38b3
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-primary {
    border-color: #4b38b3
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-primary .blockquote-footer {
    color: #4b38b3
}

.custom-blockquote.blockquote.blockquote-secondary {
    color: #3577f1;
    border-color: #3577f1;
    background-color: rgba(53, 119, 241, .15)
}

.custom-blockquote.blockquote.blockquote-secondary .blockquote-footer {
    color: #3577f1
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-secondary {
    border-color: #3577f1
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-secondary .blockquote-footer {
    color: #3577f1
}

.custom-blockquote.blockquote.blockquote-success {
    color: #45cb85;
    border-color: #45cb85;
    background-color: rgba(69, 203, 133, .15)
}

.custom-blockquote.blockquote.blockquote-success .blockquote-footer {
    color: #45cb85
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-success {
    border-color: #45cb85
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-success .blockquote-footer {
    color: #45cb85
}

.custom-blockquote.blockquote.blockquote-info {
    color: #299cdb;
    border-color: #299cdb;
    background-color: rgba(41, 156, 219, .15)
}

.custom-blockquote.blockquote.blockquote-info .blockquote-footer {
    color: #299cdb
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-info {
    border-color: #299cdb
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-info .blockquote-footer {
    color: #299cdb
}

.custom-blockquote.blockquote.blockquote-warning {
    color: #ffbe0b;
    border-color: #ffbe0b;
    background-color: rgba(255, 190, 11, .15)
}

.custom-blockquote.blockquote.blockquote-warning .blockquote-footer {
    color: #ffbe0b
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-warning {
    border-color: #ffbe0b
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-warning .blockquote-footer {
    color: #ffbe0b
}

.custom-blockquote.blockquote.blockquote-danger {
    color: #f06548;
    border-color: #f06548;
    background-color: rgba(240, 101, 72, .15)
}

.custom-blockquote.blockquote.blockquote-danger .blockquote-footer {
    color: #f06548
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-danger {
    border-color: #f06548
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-danger .blockquote-footer {
    color: #f06548
}

.custom-blockquote.blockquote.blockquote-light {
    color: #f3f6f9;
    border-color: #f3f6f9;
    background-color: rgba(243, 246, 249, .15)
}

.custom-blockquote.blockquote.blockquote-light .blockquote-footer {
    color: #f3f6f9
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-light {
    border-color: #f3f6f9
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-light .blockquote-footer {
    color: #f3f6f9
}

.custom-blockquote.blockquote.blockquote-dark {
    color: #212529;
    border-color: #212529;
    background-color: rgba(33, 37, 41, .15)
}

.custom-blockquote.blockquote.blockquote-dark .blockquote-footer {
    color: #212529
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-dark {
    border-color: #212529
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-dark .blockquote-footer {
    color: #212529
}

.custom-blockquote.blockquote.blockquote-dark {
    color: var(--vz-dark);
    border-color: var(--vz-dark);
    background-color: rgba(var(--vz-dark-rgb), .15)
}

.custom-blockquote.blockquote.blockquote-dark .blockquote-footer {
    color: var(--vz-dark)
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-dark {
    border-color: var(--vz-dark)
}

.custom-blockquote.blockquote.blockquote-outline.blockquote-dark .blockquote-footer {
    color: var(--vz-dark)
}

.form-check {
    position: relative;
    text-align: left
}

.form-check .form-check-input {
    cursor: pointer
}

.form-check label {
    cursor: pointer
}

.form-check-primary .form-check-input:checked {
    background-color: #4b38b3;
    border-color: #4b38b3
}

.form-radio-primary .form-check-input:checked {
    border-color: #4b38b3;
    background-color: #4b38b3
}

.form-radio-primary .form-check-input:checked:after {
    background-color: #4b38b3
}

.form-check-secondary .form-check-input:checked {
    background-color: #3577f1;
    border-color: #3577f1
}

.form-radio-secondary .form-check-input:checked {
    border-color: #3577f1;
    background-color: #3577f1
}

.form-radio-secondary .form-check-input:checked:after {
    background-color: #3577f1
}

.form-check-success .form-check-input:checked {
    background-color: #45cb85;
    border-color: #45cb85
}

.form-radio-success .form-check-input:checked {
    border-color: #45cb85;
    background-color: #45cb85
}

.form-radio-success .form-check-input:checked:after {
    background-color: #45cb85
}

.form-check-info .form-check-input:checked {
    background-color: #299cdb;
    border-color: #299cdb
}

.form-radio-info .form-check-input:checked {
    border-color: #299cdb;
    background-color: #299cdb
}

.form-radio-info .form-check-input:checked:after {
    background-color: #299cdb
}

.form-check-warning .form-check-input:checked {
    background-color: #ffbe0b;
    border-color: #ffbe0b
}

.form-radio-warning .form-check-input:checked {
    border-color: #ffbe0b;
    background-color: #ffbe0b
}

.form-radio-warning .form-check-input:checked:after {
    background-color: #ffbe0b
}

.form-check-danger .form-check-input:checked {
    background-color: #f06548;
    border-color: #f06548
}

.form-radio-danger .form-check-input:checked {
    border-color: #f06548;
    background-color: #f06548
}

.form-radio-danger .form-check-input:checked:after {
    background-color: #f06548
}

.form-check-light .form-check-input:checked {
    background-color: #f3f6f9;
    border-color: #f3f6f9
}

.form-radio-light .form-check-input:checked {
    border-color: #f3f6f9;
    background-color: #f3f6f9
}

.form-radio-light .form-check-input:checked:after {
    background-color: #f3f6f9
}

.form-check-dark .form-check-input:checked {
    background-color: #212529;
    border-color: #212529
}

.form-radio-dark .form-check-input:checked {
    border-color: #212529;
    background-color: #212529
}

.form-radio-dark .form-check-input:checked:after {
    background-color: #212529
}

.form-check-label {
    cursor: pointer;
    margin-bottom: 0
}

.form-check-right {
    padding-left: 0;
    display: inline-block
}

.form-check-right .form-check-input {
    float: right;
    margin-left: 0;
    margin-right: -1.6em
}

.form-check-right .form-check-label {
    display: block
}

.form-check-right.form-switch .form-check-input {
    margin-right: -2.56em
}

.form-check-outline.form-check-primary .form-check-input:checked[type=checkbox] {
    color: #4b38b3;
    background-color: transparent;
    border-color: #4b38b3
}

.form-check-outline.form-check-secondary .form-check-input:checked[type=checkbox] {
    color: #3577f1;
    background-color: transparent;
    border-color: #3577f1
}

.form-check-outline.form-check-success .form-check-input:checked[type=checkbox] {
    color: #45cb85;
    background-color: transparent;
    border-color: #45cb85
}

.form-check-outline.form-check-info .form-check-input:checked[type=checkbox] {
    color: #299cdb;
    background-color: transparent;
    border-color: #299cdb
}

.form-check-outline.form-check-warning .form-check-input:checked[type=checkbox] {
    color: #ffbe0b;
    background-color: transparent;
    border-color: #ffbe0b
}

.form-check-outline.form-check-danger .form-check-input:checked[type=checkbox] {
    color: #f06548;
    background-color: transparent;
    border-color: #f06548
}

.form-check-outline.form-check-light .form-check-input:checked[type=checkbox] {
    color: #f3f6f9;
    background-color: transparent;
    border-color: #f3f6f9
}

.form-check-outline.form-check-dark .form-check-input:checked[type=checkbox] {
    color: #212529;
    background-color: transparent;
    border-color: #212529
}

.form-check-outline .form-check-input {
    position: relative
}

.form-check-outline .form-check-input:checked[type=checkbox] {
    background-image: none
}

.form-check-outline .form-check-input:checked[type=checkbox]::before {
    content: "\f012c";
    font-family: "Material Design Icons";
    top: -2px;
    position: absolute;
    font-weight: 700;
    font-size: 11px;
    left: 1px
}

.form-radio-outline.form-radio-primary .form-check-input:checked[type=radio] {
    color: #4b38b3;
    background-color: transparent;
    border-color: #4b38b3
}

.form-radio-outline.form-radio-secondary .form-check-input:checked[type=radio] {
    color: #3577f1;
    background-color: transparent;
    border-color: #3577f1
}

.form-radio-outline.form-radio-success .form-check-input:checked[type=radio] {
    color: #45cb85;
    background-color: transparent;
    border-color: #45cb85
}

.form-radio-outline.form-radio-info .form-check-input:checked[type=radio] {
    color: #299cdb;
    background-color: transparent;
    border-color: #299cdb
}

.form-radio-outline.form-radio-warning .form-check-input:checked[type=radio] {
    color: #ffbe0b;
    background-color: transparent;
    border-color: #ffbe0b
}

.form-radio-outline.form-radio-danger .form-check-input:checked[type=radio] {
    color: #f06548;
    background-color: transparent;
    border-color: #f06548
}

.form-radio-outline.form-radio-light .form-check-input:checked[type=radio] {
    color: #f3f6f9;
    background-color: transparent;
    border-color: #f3f6f9
}

.form-radio-outline.form-radio-dark .form-check-input:checked[type=radio] {
    color: #212529;
    background-color: transparent;
    border-color: #212529
}

.form-radio-outline .form-check-input {
    position: relative
}

.form-radio-outline .form-check-input:checked[type=radio] {
    background-image: none
}

.form-radio-outline .form-check-input:checked[type=radio]::before {
    content: "\f0765";
    font-family: "Material Design Icons";
    top: 0;
    position: absolute;
    font-size: 8px;
    left: 2.2px
}

.form-switch-md {
    padding-left: 2.5rem;
    min-height: 22px;
    line-height: 22px
}

.form-switch-md .form-check-input {
    width: 40px;
    height: 20px;
    left: -.5rem;
    position: relative
}

.form-switch-md .form-check-label {
    vertical-align: middle
}

.form-switch-lg {
    padding-left: 2.75rem;
    min-height: 28px;
    line-height: 28px
}

.form-switch-lg .form-check-input {
    width: 48px;
    height: 24px;
    left: -.75rem;
    position: relative
}

.input-group-text {
    margin-bottom: 0
}

.form-switch-primary .form-check-input:checked {
    background-color: #4b38b3;
    border-color: #4b38b3
}

.form-switch-custom.form-switch-primary .form-check-input:checked::before {
    color: #4b38b3
}

.form-switch-secondary .form-check-input:checked {
    background-color: #3577f1;
    border-color: #3577f1
}

.form-switch-custom.form-switch-secondary .form-check-input:checked::before {
    color: #3577f1
}

.form-switch-success .form-check-input:checked {
    background-color: #45cb85;
    border-color: #45cb85
}

.form-switch-custom.form-switch-success .form-check-input:checked::before {
    color: #45cb85
}

.form-switch-info .form-check-input:checked {
    background-color: #299cdb;
    border-color: #299cdb
}

.form-switch-custom.form-switch-info .form-check-input:checked::before {
    color: #299cdb
}

.form-switch-warning .form-check-input:checked {
    background-color: #ffbe0b;
    border-color: #ffbe0b
}

.form-switch-custom.form-switch-warning .form-check-input:checked::before {
    color: #ffbe0b
}

.form-switch-danger .form-check-input:checked {
    background-color: #f06548;
    border-color: #f06548
}

.form-switch-custom.form-switch-danger .form-check-input:checked::before {
    color: #f06548
}

.form-switch-light .form-check-input:checked {
    background-color: #f3f6f9;
    border-color: #f3f6f9
}

.form-switch-custom.form-switch-light .form-check-input:checked::before {
    color: #f3f6f9
}

.form-switch-dark .form-check-input:checked {
    background-color: #212529;
    border-color: #212529
}

.form-switch-custom.form-switch-dark .form-check-input:checked::before {
    color: #212529
}

.form-switch-custom .form-check-input {
    position: relative;
    background-image: none
}

.form-switch-custom .form-check-input::before {
    content: "\f0765";
    font-family: "Material Design Icons";
    top: -9px;
    position: absolute;
    font-size: 20px;
    left: -3px;
    color: #878a99;
    -webkit-transition: background-position .15s ease-in-out;
    transition: background-position .15s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .form-switch-custom .form-check-input::before {
        -webkit-transition: none;
        transition: none
    }
}

.form-switch-custom .form-check-input:checked {
    background-image: none;
    background-color: var(--vz-gray-300);
    -webkit-transition: background-position .15s ease-in-out;
    transition: background-position .15s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .form-switch-custom .form-check-input:checked {
        -webkit-transition: none;
        transition: none
    }
}

.form-switch-custom .form-check-input:checked::before {
    right: -3px;
    left: auto
}

.form-switch-custom .form-check-input:focus {
    background-image: none
}

.form-switch-right {
    display: inline-block;
    padding-right: .8em;
    margin-bottom: 0;
    padding-left: 0 !important
}

.form-switch-right .form-check-input {
    float: right;
    margin-left: 0;
    margin-right: -1.6em;
    margin-top: .1em !important
}

.form-switch-right label {
    margin-bottom: 0;
    margin-right: 1rem
}

.card-radio {
    padding: 0
}

.card-radio .form-check-label {
    background-color: var(--vz-card-bg);
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: .25rem;
    padding: 1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    position: relative;
    padding-right: 32px
}

.card-radio .form-check-label:hover {
    cursor: pointer
}

.card-radio .form-check-input {
    display: none
}

.card-radio .form-check-input:checked+.form-check-label {
    border-color: #4b38b3 !important
}

.card-radio .form-check-input:checked+.form-check-label:before {
    content: "\eb80";
    font-family: remixicon;
    position: absolute;
    top: 2px;
    right: 6px;
    font-size: 16px;
    color: #4b38b3
}

.card-radio.dark .form-check-input:checked+.form-check-label:before {
    color: #fff
}

[data-layout-mode=dark] .form-switch .form-check-input,
[data-layout-mode=dark] .form-switch .form-check-input:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ced4da'/%3e%3c/svg%3e")
}

.form-icon {
    position: relative
}

.form-icon .form-control-icon {
    padding-left: 2.7rem;
    position: relative
}

.form-icon i {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 18px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.form-icon.right .form-control-icon {
    padding-right: 2.7rem;
    padding-left: .9rem;
    position: relative
}

.form-icon.right i {
    left: auto;
    right: 18px
}

[data-layout-mode=dark] .form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ced4da' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e")
}

.list-group-fill-primary {
    background-color: #4b38b3;
    border-color: #4b38b3;
    color: #fff
}

.list-group-fill-primary .list-group-item.active {
    background-color: #4b38b3 !important;
    border-color: #4b38b3
}

.list-group-fill-secondary {
    background-color: #3577f1;
    border-color: #3577f1;
    color: #fff
}

.list-group-fill-secondary .list-group-item.active {
    background-color: #3577f1 !important;
    border-color: #3577f1
}

.list-group-fill-success {
    background-color: #45cb85;
    border-color: #45cb85;
    color: #fff
}

.list-group-fill-success .list-group-item.active {
    background-color: #45cb85 !important;
    border-color: #45cb85
}

.list-group-fill-info {
    background-color: #299cdb;
    border-color: #299cdb;
    color: #fff
}

.list-group-fill-info .list-group-item.active {
    background-color: #299cdb !important;
    border-color: #299cdb
}

.list-group-fill-warning {
    background-color: #ffbe0b;
    border-color: #ffbe0b;
    color: #fff
}

.list-group-fill-warning .list-group-item.active {
    background-color: #ffbe0b !important;
    border-color: #ffbe0b
}

.list-group-fill-danger {
    background-color: #f06548;
    border-color: #f06548;
    color: #fff
}

.list-group-fill-danger .list-group-item.active {
    background-color: #f06548 !important;
    border-color: #f06548
}

.list-group-fill-light {
    background-color: #f3f6f9;
    border-color: #f3f6f9;
    color: #fff
}

.list-group-fill-light .list-group-item.active {
    background-color: #f3f6f9 !important;
    border-color: #f3f6f9
}

.list-group-fill-dark {
    background-color: #212529;
    border-color: #212529;
    color: #fff
}

.list-group-fill-dark .list-group-item.active {
    background-color: #212529 !important;
    border-color: #212529
}

.list-group-fill-light {
    color: #212529 !important
}

.list-group-item {
    margin-bottom: 0
}

.list-group-item .list-text {
    color: #878a99
}

.list-group-item.active {
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.list-group-item.active .list-title {
    color: #fff
}

.list-group-item.active .list-text {
    color: rgba(255, 255, 255, .5)
}

[data-simplebar] {
    position: relative;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -ms-flex-line-pack: start;
    align-content: flex-start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.simplebar-wrapper {
    overflow: hidden;
    width: inherit;
    height: inherit;
    max-width: inherit;
    max-height: inherit
}

.simplebar-mask {
    direction: inherit;
    position: absolute;
    overflow: hidden;
    padding: 0;
    margin: 0;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    width: auto !important;
    height: auto !important;
    z-index: 0
}

.simplebar-offset {
    direction: inherit !important;
    -webkit-box-sizing: inherit !important;
    box-sizing: inherit !important;
    resize: none !important;
    position: absolute;
    top: 0;
    left: 0 !important;
    bottom: 0;
    right: 0 !important;
    padding: 0;
    margin: 0;
    -webkit-overflow-scrolling: touch
}

.simplebar-content-wrapper {
    direction: inherit;
    -webkit-box-sizing: border-box !important;
    box-sizing: border-box !important;
    position: relative;
    display: block;
    height: 100%;
    width: auto;
    visibility: visible;
    overflow: auto;
    max-width: 100%;
    max-height: 100%;
    scrollbar-width: none;
    padding: 0 !important
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
    display: none
}

.simplebar-content:after,
.simplebar-content:before {
    content: " ";
    display: table
}

.simplebar-placeholder {
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    pointer-events: none
}

.simplebar-height-auto-observer-wrapper {
    -webkit-box-sizing: inherit !important;
    box-sizing: inherit !important;
    height: 100%;
    width: 100%;
    max-width: 1px;
    position: relative;
    float: left;
    max-height: 1px;
    overflow: hidden;
    z-index: -1;
    padding: 0;
    margin: 0;
    pointer-events: none;
    -webkit-box-flex: inherit;
    -ms-flex-positive: inherit;
    flex-grow: inherit;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -ms-flex-preferred-size: 0;
    flex-basis: 0
}

.simplebar-height-auto-observer {
    -webkit-box-sizing: inherit;
    box-sizing: inherit;
    display: block;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    height: 1000%;
    width: 1000%;
    min-height: 1px;
    min-width: 1px;
    overflow: hidden;
    pointer-events: none;
    z-index: -1
}

.simplebar-track {
    z-index: 1;
    position: absolute;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden
}

[data-simplebar].simplebar-dragging .simplebar-content {
    pointer-events: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-user-select: none
}

[data-simplebar].simplebar-dragging .simplebar-track {
    pointer-events: all
}

.simplebar-scrollbar {
    position: absolute;
    right: 2px;
    width: 6px;
    min-height: 10px
}

.simplebar-scrollbar:before {
    position: absolute;
    content: "";
    background: #a2adb7;
    border-radius: 7px;
    left: 0;
    right: 0;
    opacity: 0;
    -webkit-transition: opacity .2s linear;
    transition: opacity .2s linear
}

.simplebar-scrollbar.simplebar-visible:before {
    opacity: .5;
    -webkit-transition: opacity 0s linear;
    transition: opacity 0s linear
}

.simplebar-track.simplebar-vertical {
    top: 0;
    width: 11px
}

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
    top: 2px;
    bottom: 2px
}

.simplebar-track.simplebar-horizontal {
    left: 0;
    height: 11px
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
    height: 100%;
    left: 2px;
    right: 2px
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
    right: auto;
    left: 0;
    top: 2px;
    height: 7px;
    min-height: 0;
    min-width: 10px;
    width: auto
}

[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {
    right: auto;
    left: 0
}

.hs-dummy-scrollbar-size {
    direction: rtl;
    position: fixed;
    opacity: 0;
    visibility: hidden;
    height: 500px;
    width: 500px;
    overflow-y: hidden;
    overflow-x: scroll
}

.simplebar-hide-scrollbar {
    position: fixed;
    left: 0;
    visibility: hidden;
    overflow-y: scroll;
    scrollbar-width: none
}

.custom-scroll {
    height: 100%
}

[data-simplebar-track=primary] .simplebar-scrollbar:before {
    background: #4b38b3
}

[data-simplebar-track=secondary] .simplebar-scrollbar:before {
    background: #3577f1
}

[data-simplebar-track=success] .simplebar-scrollbar:before {
    background: #45cb85
}

[data-simplebar-track=info] .simplebar-scrollbar:before {
    background: #299cdb
}

[data-simplebar-track=warning] .simplebar-scrollbar:before {
    background: #ffbe0b
}

[data-simplebar-track=danger] .simplebar-scrollbar:before {
    background: #f06548
}

[data-simplebar-track=light] .simplebar-scrollbar:before {
    background: #f3f6f9
}

[data-simplebar-track=dark] .simplebar-scrollbar:before {
    background: #212529
}

code[class*=language-],
pre[class*=language-] {
    color: #000;
    background: 0 0;
    text-shadow: 0 1px #fff;
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
    font-size: 1em;
    text-align: left;
    white-space: pre;
    word-spacing: normal;
    word-break: normal;
    word-wrap: normal;
    line-height: 1.5;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    -webkit-hyphens: none;
    -ms-hyphens: none;
    hyphens: none
}

code[class*=language-] ::-moz-selection,
code[class*=language-]::-moz-selection,
pre[class*=language-] ::-moz-selection,
pre[class*=language-]::-moz-selection {
    text-shadow: none;
    background: #b3d4fc
}

code[class*=language-] ::-moz-selection,
code[class*=language-]::-moz-selection,
pre[class*=language-] ::-moz-selection,
pre[class*=language-]::-moz-selection {
    text-shadow: none;
    background: #b3d4fc
}

code[class*=language-] ::selection,
code[class*=language-]::selection,
pre[class*=language-] ::selection,
pre[class*=language-]::selection {
    text-shadow: none;
    background: #b3d4fc
}

@media print {

    code[class*=language-],
    pre[class*=language-] {
        text-shadow: none
    }
}

pre[class*=language-] {
    padding: 1em;
    margin: 0;
    overflow: auto
}

:not(pre)>code[class*=language-],
pre[class*=language-] {
    background: var(--vz-light) !important
}

:not(pre)>code[class*=language-] {
    padding: .1em;
    border-radius: .3em;
    white-space: normal
}

.token.cdata,
.token.comment,
.token.doctype,
.token.prolog {
    color: #708090
}

.token.punctuation {
    color: #999
}

.token.namespace {
    opacity: .7
}

.token.boolean,
.token.constant,
.token.deleted,
.token.number,
.token.property,
.token.symbol,
.token.tag {
    color: #f06548
}

.token.attr-name,
.token.builtin,
.token.char,
.token.inserted,
.token.selector,
.token.string {
    color: #690
}

.language-css .token.string,
.style .token.string,
.token.entity,
.token.operator,
.token.url {
    color: #9a6e3a;
    background: rgba(255, 255, 255, .5)
}

.token.atrule,
.token.attr-value,
.token.keyword {
    color: #07a
}

.token.class-name,
.token.function {
    color: #dd4a68
}

.token.important,
.token.regex,
.token.variable {
    color: #e90
}

.token.bold,
.token.important {
    font-weight: 700
}

.token.italic {
    font-style: italic
}

.token.entity {
    cursor: help
}

:not(pre)>code[class*=language-],
pre[class*=language-] {
    background: #eff2f7
}

code[class*=language-],
pre[class*=language-] {
    color: #878a99;
    text-shadow: none
}

.language-markup::-webkit-scrollbar {
    -webkit-appearance: none
}

.language-markup::-webkit-scrollbar:vertical {
    width: 10px
}

.language-markup::-webkit-scrollbar:horizontal {
    height: 10px
}

.language-markup::-webkit-scrollbar-thumb {
    background-color: rgba(var(--vz-dark-rgb), .1);
    border-radius: 10px;
    border: 2px solid var(--vz-light)
}

.language-markup::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: var(--vz-light)
}

#scroll-top {
    width: 30px;
    height: 30px;
    position: fixed;
    bottom: 65px;
    right: 30px;
    background: #343a40;
    border-color: transparent;
    border-radius: 3px;
    color: #fff;
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

.btn-clipboard {
    position: absolute !important;
    right: 15px !important;
    z-index: 1 !important
}

div.code-toolbar>.toolbar {
    opacity: 1 !important
}

div.code-toolbar>.toolbar button {
    display: inline-block !important;
    margin: .375rem .5rem !important;
    padding: .25rem .75rem !important;
    -webkit-transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out !important;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out !important;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !important;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-box-shadow .15s ease-in-out !important;
    border-radius: .2rem !important;
    border: 1px solid rgba(75, 56, 179, .35) !important;
    background-color: transparent;
    color: #4b38b3 !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

div.code-toolbar>.toolbar button:focus {
    outline: 0 !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

div.code-toolbar>.toolbar button:hover {
    background-color: #4b38b3 !important;
    color: #fff !important
}

.swal2-container .swal2-title {
    padding: 24px 24px 0;
    font-size: 20px;
    font-weight: 500
}

.swal2-popup {
    padding-bottom: 24px;
    border-radius: .3rem;
    background-color: var(--vz-modal-bg);
    color: var(--vz-body-color)
}

.swal2-popup .swal2-title {
    color: var(--vz-heading-color)
}

.swal2-popup .swal2-html-container {
    color: var(--vz-body-color)
}

.swal2-footer {
    border-top: 1px solid var(--vz-border-color);
    color: var(--vz-body-color)
}

.swal2-html-container {
    font-size: 16px
}

.swal2-icon.swal2-question {
    border-color: #299cdb;
    color: #299cdb
}

.swal2-icon.swal2-success [class^=swal2-success-line] {
    background-color: #45cb85
}

.swal2-icon.swal2-success .swal2-success-ring {
    border-color: rgba(69, 203, 133, .3)
}

.swal2-icon.swal2-warning {
    border-color: #ffbe0b;
    color: #ffbe0b
}

.swal2-styled:focus {
    -webkit-box-shadow: none;
    box-shadow: none
}

.swal2-loader {
    border-color: #4b38b3 transparent #4b38b3 transparent
}

.swal2-timer-progress-bar {
    background-color: rgba(69, 203, 133, .4)
}

.swal2-progress-steps .swal2-progress-step {
    background: #4b38b3
}

.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
    background: #4b38b3
}

.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step,
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line {
    background: rgba(75, 56, 179, .3)
}

.swal2-progress-steps .swal2-progress-step-line {
    background: #4b38b3
}

.swal2-actions.swal2-loading .swal2-styled.swal2-confirm {
    border-left-color: #4b38b3 !important;
    border-right-color: #4b38b3 !important
}

.swal2-file,
.swal2-input,
.swal2-textarea {
    border: 1px solid var(--vz-input-border)
}

.swal2-file:focus,
.swal2-input:focus,
.swal2-textarea:focus {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-color: var(--vz-input-focus-border)
}

.swal2-input {
    height: auto;
    display: block;
    padding: .5rem .9rem;
    font-size: .8125rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--vz-body-color);
    background-color: var(--vz-input-bg);
    background-clip: padding-box;
    border: 1px solid var(--vz-input-border)
}

.swal2-close {
    font-family: var(--vz-font-sans-serif);
    font-weight: 300;
    font-size: 28px
}

.swal2-close:focus {
    -webkit-box-shadow: none;
    box-shadow: none
}

.swal2-close:hover {
    color: #4b38b3
}

.swal2-validation-message {
    background-color: transparent
}

.dropzone {
    min-height: 230px;
    border: 2px dashed var(--vz-border-color);
    background: var(--vz-card-bg);
    border-radius: 6px
}

.dropzone .dz-message {
    font-size: 24px;
    width: 100%;
    margin: 1em 0
}

.noUi-connect {
    background: #45cb85
}

.noUi-handle {
    background: #45cb85;
    border: 2px solid var(--vz-card-bg);
    -webkit-box-shadow: none;
    box-shadow: none
}

.noUi-horizontal {
    height: 4px
}

.noUi-horizontal .noUi-handle {
    height: 16px;
    width: 16px;
    border-radius: 50%;
    right: -10px !important;
    top: -7px
}

.noUi-horizontal .noUi-handle::after,
.noUi-horizontal .noUi-handle::before {
    display: none
}

.noUi-horizontal .noUi-handle:focus {
    outline: 0
}

.noUi-pips-horizontal {
    height: 50px
}

.noUi-tooltip {
    padding: .4rem .7rem;
    border-color: var(--vz-border-color);
    border-radius: .2rem;
    background-color: var(--vz-card-bg);
    color: #212529
}

.noUi-vertical {
    width: 4px
}

.noUi-vertical .noUi-handle {
    height: 16px;
    width: 16px;
    right: -8px;
    top: -12px;
    left: auto;
    border-radius: 50%
}

.noUi-vertical .noUi-handle::after,
.noUi-vertical .noUi-handle::before {
    display: none
}

.noUi-vertical .noUi-handle:focus {
    outline: 0
}

.noUi-vertical .noUi-origin {
    top: 0
}

.noUi-value {
    font-size: 12px
}

.noUi-marker-horizontal.noUi-marker-large {
    height: 12px
}

.noUi-value-horizontal {
    padding-top: 4px
}

.noUi-target {
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: var(--vz-light);
    border-color: var(--vz-light)
}

.noUi-touch-area:focus {
    outline: 0
}

#blue,
#green,
#red {
    margin: 10px;
    display: inline-block;
    height: 200px
}

#colorpicker {
    height: 240px;
    width: 310px;
    margin: 0 auto;
    padding: 10px;
    border: 1px solid var(--vz-border-color)
}

#result {
    margin: 60px 26px;
    height: 100px;
    width: 100px;
    display: inline-block;
    vertical-align: top;
    border: 1px solid var(--vz-border-color);
    -webkit-box-shadow: 0 0 3px;
    box-shadow: 0 0 3px;
    border-radius: 7px
}

#red .noUi-connect {
    background: #f06548
}

#green .noUi-connect {
    background: #45cb85
}

#blue .noUi-connect {
    background: #4b38b3
}

.form-control.keyboard {
    max-width: 340px !important
}

.example-val {
    font-size: 12px;
    color: #878a99;
    display: block;
    margin: 15px 0
}

.example-val:before {
    content: "Value: ";
    font-size: 12px;
    font-weight: 600
}

.noUi-tooltip {
    display: none
}

.noUi-active .noUi-tooltip {
    display: block
}

.c-1-color {
    background: #f06548
}

.c-2-color {
    background: #ffbe0b
}

.c-3-color {
    background: #45cb85
}

.c-4-color {
    background: #4b38b3
}

.c-5-color {
    background: #6559cc
}

#slider-toggle {
    height: 50px
}

#slider-toggle.off .noUi-handle {
    border-color: #f06548
}

[data-slider-color=primary] .noUi-connect {
    background: #4b38b3
}

[data-slider-color=primary] .noUi-handle {
    background: #4b38b3
}

[data-slider-color=primary][data-slider-style=border] .noUi-handle,
[data-slider-color=primary][data-slider-style=square] .noUi-handle {
    border-color: #4b38b3
}

[data-slider-color=secondary] .noUi-connect {
    background: #3577f1
}

[data-slider-color=secondary] .noUi-handle {
    background: #3577f1
}

[data-slider-color=secondary][data-slider-style=border] .noUi-handle,
[data-slider-color=secondary][data-slider-style=square] .noUi-handle {
    border-color: #3577f1
}

[data-slider-color=success] .noUi-connect {
    background: #45cb85
}

[data-slider-color=success] .noUi-handle {
    background: #45cb85
}

[data-slider-color=success][data-slider-style=border] .noUi-handle,
[data-slider-color=success][data-slider-style=square] .noUi-handle {
    border-color: #45cb85
}

[data-slider-color=info] .noUi-connect {
    background: #299cdb
}

[data-slider-color=info] .noUi-handle {
    background: #299cdb
}

[data-slider-color=info][data-slider-style=border] .noUi-handle,
[data-slider-color=info][data-slider-style=square] .noUi-handle {
    border-color: #299cdb
}

[data-slider-color=warning] .noUi-connect {
    background: #ffbe0b
}

[data-slider-color=warning] .noUi-handle {
    background: #ffbe0b
}

[data-slider-color=warning][data-slider-style=border] .noUi-handle,
[data-slider-color=warning][data-slider-style=square] .noUi-handle {
    border-color: #ffbe0b
}

[data-slider-color=danger] .noUi-connect {
    background: #f06548
}

[data-slider-color=danger] .noUi-handle {
    background: #f06548
}

[data-slider-color=danger][data-slider-style=border] .noUi-handle,
[data-slider-color=danger][data-slider-style=square] .noUi-handle {
    border-color: #f06548
}

[data-slider-color=light] .noUi-connect {
    background: #f3f6f9
}

[data-slider-color=light] .noUi-handle {
    background: #f3f6f9
}

[data-slider-color=light][data-slider-style=border] .noUi-handle,
[data-slider-color=light][data-slider-style=square] .noUi-handle {
    border-color: #f3f6f9
}

[data-slider-color=dark] .noUi-connect {
    background: #212529
}

[data-slider-color=dark] .noUi-handle {
    background: #212529
}

[data-slider-color=dark][data-slider-style=border] .noUi-handle,
[data-slider-color=dark][data-slider-style=square] .noUi-handle {
    border-color: #212529
}

[data-slider-size=lg].noUi-horizontal {
    height: 12px
}

[data-slider-size=lg].noUi-horizontal .noUi-handle {
    width: 24px;
    height: 24px
}

[data-slider-size=md].noUi-horizontal {
    height: 8px
}

[data-slider-size=md].noUi-horizontal .noUi-handle {
    width: 20px;
    height: 20px
}

[data-slider-size=sm].noUi-horizontal {
    height: 4px
}

[data-slider-size=sm].noUi-horizontal .noUi-handle {
    width: 16px;
    height: 16px
}

[data-slider-style=line].noUi-horizontal .noUi-handle {
    width: 8px;
    border-radius: 4px;
    right: -8px
}

[data-slider-style=line].noUi-vertical .noUi-handle {
    height: 8px;
    border-radius: 4px;
    top: -3px
}

[data-slider-style=border] .noUi-handle {
    border-color: #45cb85;
    background-color: var(--vz-card-bg)
}

[data-slider-style=square] .noUi-handle {
    border-radius: 0;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    height: 10px;
    width: 10px;
    top: -4px;
    border-color: #45cb85;
    background-color: var(--vz-card-bg)
}

.nested-list .list-group-item {
    background-color: rgba(75, 56, 179, .05);
    border-color: rgba(75, 56, 179, .05)
}

.nested-1,
.nested-2,
.nested-3,
.nested-list {
    margin-top: 5px
}

.nested-sortable-handle .handle {
    position: absolute;
    left: 0;
    top: 0;
    width: 42px;
    height: 42px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    cursor: -webkit-grab;
    cursor: grab
}

.nested-sortable-handle .list-group-item {
    padding-left: 42px
}

.shepherd-element {
    background: var(--vz-card-bg);
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.shepherd-has-title .shepherd-content .shepherd-header {
    background-color: var(--vz-light);
    padding: .5rem .75rem
}

.shepherd-has-title .shepherd-content .shepherd-cancel-icon {
    color: rgba(var(--vz-dark-rgb), .75)
}

.shepherd-has-title .shepherd-content .shepherd-cancel-icon:hover {
    color: rgba(var(--vz-dark-rgb), .75)
}

.shepherd-element.shepherd-has-title[data-popper-placement^=bottom]>.shepherd-arrow:before {
    background-color: var(--vz-light)
}

.shepherd-title {
    font-size: 15px;
    font-weight: 500;
    color: var(--vz-body-color)
}

.shepherd-text {
    padding: .75rem;
    font-size: .8125rem;
    color: var(--vz-body-color)
}

.shepherd-button.btn-success:not(:disabled):hover {
    background: #3bc87e;
    color: #fff
}

.shepherd-button.btn-light:not(:disabled):hover {
    background: rgba(var(--vz-light-rgb), .75);
    color: var(--vz-dark)
}

.shepherd-button.btn-primary:not(:disabled):hover {
    background: #4735a9;
    color: #fff
}

.shepherd-footer {
    padding: 0 .75rem .75rem
}

.shepherd-arrow,
.shepherd-arrow:before {
    content: "\ea75";
    font-family: remixicon;
    font-size: 24px;
    z-index: 1;
    background-color: transparent !important;
    -webkit-transform: rotate(0);
    transform: rotate(0);
    color: #4b38b3
}

.shepherd-element[data-popper-placement^=bottom]>.shepherd-arrow {
    top: -18px
}

.shepherd-button {
    margin-right: .5rem
}

.swiper-button-next,
.swiper-button-prev {
    height: 32px;
    width: 32px;
    background-color: rgba(75, 56, 179, .2);
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    border-radius: 8px
}

.swiper-button-next::after,
.swiper-button-prev::after {
    font-family: remixicon;
    font-size: 28px;
    color: #4b38b3;
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}

.swiper-button-prev::after {
    content: "\ea64" !important
}

.swiper-button-next::after {
    content: "\ea6e" !important
}

.swiper-pagination-bullet {
    width: 22px;
    height: 5px;
    background-color: #fff;
    border-radius: 50px;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.swiper-pagination-bullet .swiper-pagination-bullet-active {
    opacity: 1
}

.dynamic-pagination .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    background-color: #fff;
    opacity: .5;
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

.dynamic-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1;
    width: 20px
}

.swiper-pagination-fraction {
    color: #fff;
    font-size: 16px;
    background-color: rgba(0, 0, 0, .3);
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px)
}

.pagination-custom .swiper-pagination-bullet {
    height: 25px;
    width: 25px;
    line-height: 25px;
    border-radius: 8px;
    background-color: #fff;
    opacity: .5;
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

.pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active {
    color: #3577f1;
    opacity: 1
}

.swiper-pagination-progressbar {
    height: 6px !important;
    background-color: rgba(69, 203, 133, .25)
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    background-color: #45cb85
}

.swiper-scrollbar {
    background-color: rgba(255, 255, 255, .35);
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
    padding: 1.2px;
    height: 6px !important
}

.swiper-scrollbar .swiper-scrollbar-drag {
    background-color: #fff
}

.swiper-pagination-dark .swiper-pagination-bullet {
    background-color: #3577f1
}

.swiper-pagination-dark .dynamic-pagination .swiper-pagination-bullet {
    background-color: #3577f1
}

.swiper-pagination-dark.pagination-custom .swiper-pagination-bullet {
    color: #fff
}

.swiper-pagination-dark.pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1
}

.swiper-pagination-dark.swiper-scrollbar {
    background-color: rgba(33, 37, 41, .35)
}

.multi-wrapper {
    border: none;
    position: relative
}

.multi-wrapper::before {
    content: "\ea61";
    position: absolute;
    font-family: remixicon;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    bottom: 86px;
    width: 32px;
    height: 32px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 16px;
    border-radius: 50%;
    color: #878a99;
    background-color: var(--vz-light);
    z-index: 1
}

.multi-wrapper .non-selected-wrapper {
    border: 1px solid var(--vz-input-border);
    background-color: var(--vz-input-bg);
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.multi-wrapper .non-selected-wrapper::-webkit-scrollbar,
.multi-wrapper .selected-wrapper::-webkit-scrollbar {
    -webkit-appearance: none
}

.multi-wrapper .non-selected-wrapper::-webkit-scrollbar:vertical,
.multi-wrapper .selected-wrapper::-webkit-scrollbar:vertical {
    width: 10px
}

.multi-wrapper .non-selected-wrapper::-webkit-scrollbar:horizontal,
.multi-wrapper .selected-wrapper::-webkit-scrollbar:horizontal {
    height: 9px
}

.multi-wrapper .non-selected-wrapper::-webkit-scrollbar-thumb,
.multi-wrapper .selected-wrapper::-webkit-scrollbar-thumb {
    background-color: rgba(var(--vz-dark-rgb), .2);
    border-radius: 10px;
    border: 2px solid var(--vz-input-bg)
}

.multi-wrapper .non-selected-wrapper::-webkit-scrollbar-track,
.multi-wrapper .selected-wrapper::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: var(--vz-input-bg)
}

.multi-wrapper .item-group .group-label {
    font-size: 12px
}

.multi-wrapper .item {
    color: var(--vz-body-color)
}

.multi-wrapper .item:hover {
    background-color: rgba(75, 56, 179, .1)
}

.multi-wrapper .selected-wrapper {
    border: 1px solid var(--vz-input-border);
    background: var(--vz-input-bg);
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem
}

.multi-wrapper .search-input {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    padding: .5rem .9rem;
    font-size: .8125rem;
    color: var(--vz-body-color);
    background-color: var(--vz-input-bg);
    border: 1px solid var(--vz-input-border);
    border-radius: .25rem;
    margin-bottom: 16px
}

.multi-wrapper .search-input::-webkit-input-placeholder {
    color: #878a99
}

.multi-wrapper .search-input::-moz-placeholder {
    color: #878a99
}

.multi-wrapper .search-input:-ms-input-placeholder {
    color: #878a99
}

.multi-wrapper .search-input::-ms-input-placeholder {
    color: #878a99
}

.multi-wrapper .search-input::placeholder {
    color: #878a99
}

.multi-wrapper .header {
    font-weight: 600;
    color: var(--vz-gray-600)
}

.pcr-app {
    background: var(--vz-card-bg);
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    border-radius: 4px;
    border: 1px solid var(--vz-border-color)
}

.pickr .pcr-button {
    border: 4px solid var(--vz-card-bg);
    -webkit-box-shadow: 0 0 0 2px var(--vz-border-color);
    box-shadow: 0 0 0 2px var(--vz-border-color);
    border-radius: 50%;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.pickr .pcr-button::after,
.pickr .pcr-button::before {
    border-radius: 50%
}

.pcr-app[data-theme=classic] .pcr-selection .pcr-color-preview {
    margin-right: .75em;
    margin-left: 0
}

.pcr-app[data-theme=classic] .pcr-selection .pcr-color-chooser,
.pcr-app[data-theme=classic] .pcr-selection .pcr-color-opacity {
    margin-left: .75em;
    margin-right: 0
}

.pcr-app[data-theme=monolith] .pcr-result {
    min-width: 100%
}

.pcr-app .pcr-interaction .pcr-type.active {
    background: #4b38b3
}

.pcr-app .pcr-interaction .pcr-result {
    background-color: var(--vz-input-bg);
    color: var(--vz-body-color);
    border: 1px solid var(--vz-input-border);
    border-radius: .25rem
}

.pcr-app .pcr-interaction input {
    border-radius: .25rem !important
}

.pcr-app .pcr-interaction input:focus {
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: var(--vz-input-bg);
    border-color: var(--vz-input-focus-border)
}

.pcr-app .pcr-interaction .pcr-save {
    background: #45cb85 !important
}

.pcr-app .pcr-interaction .pcr-cancel,
.pcr-app .pcr-interaction .pcr-clear {
    background: #f06548 !important
}

.filepond--root {
    margin-bottom: 0
}

.filepond--root[data-style-panel-layout~=circle] .filepond--drop-label label {
    font-size: 14px
}

.filepond--panel-root {
    border: 2px dashed var(--vz-border-color);
    background: var(--vz-card-bg)
}

.filepond--drop-label {
    color: var(--vz-body-color)
}

.filepond--drop-label label {
    font-weight: 500
}

.filepond--credits {
    display: none
}

.filepond--item-panel {
    background-color: #4b38b3 !important
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    height: auto
}

.input-step {
    border: 1px solid var(--vz-input-border);
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    overflow: visible;
    height: 37.5px;
    border-radius: .25rem;
    background: var(--vz-input-bg);
    padding: 4px
}

.input-step input {
    width: 4em;
    height: 100%;
    text-align: center;
    border: 0;
    background: 0 0;
    color: var(--vz-body-color);
    border-radius: .25rem
}

.input-step input:focus-visible {
    outline: 0
}

.input-step button {
    width: 1.4em;
    font-weight: 300;
    height: 100%;
    line-height: .1em;
    font-size: 1.4em;
    padding: .2em !important;
    background: var(--vz-light);
    color: var(--vz-body-color);
    border: none;
    border-radius: .25rem
}

.input-step.light {
    background: var(--vz-light)
}

.input-step.light button {
    background-color: var(--vz-input-bg)
}

.input-step.light-input {
    background: var(--vz-light)
}

.input-step.light-input input {
    background-color: var(--vz-input-bg)
}

.input-step.full-width {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%
}

.input-step.full-width button {
    -ms-flex-negative: 0;
    flex-shrink: 0
}

.input-step.full-width input {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
}

.input-step.step-primary button {
    background-color: #4b38b3;
    color: #fff
}

.input-step.step-secondary button {
    background-color: #3577f1;
    color: #fff
}

.input-step.step-success button {
    background-color: #45cb85;
    color: #fff
}

.input-step.step-info button {
    background-color: #299cdb;
    color: #fff
}

.input-step.step-warning button {
    background-color: #ffbe0b;
    color: #fff
}

.input-step.step-danger button {
    background-color: #f06548;
    color: #fff
}

.input-step.step-light button {
    background-color: #f3f6f9;
    color: #fff
}

.input-step.step-dark button {
    background-color: #212529;
    color: #fff
}

.ck {
    font-family: var(--vz-font-sans-serif) !important
}

.ck.ck-reset_all,
.ck.ck-reset_all * {
    color: var(--vz-body-color) !important
}

.ck.ck-toolbar {
    background: rgba(var(--vz-light-rgb), .75) !important
}

.ck p {
    margin-bottom: 0
}

.ck.ck-toolbar {
    border: 1px solid var(--vz-input-border) !important
}

.ck.ck-toolbar.ck-toolbar_grouping>.ck-toolbar__items {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
}

.ck.ck-toolbar .ck.ck-toolbar__separator {
    background: 0 0 !important
}

.ck.ck-editor__main>.ck-editor__editable {
    border-top: 0 !important;
    background-color: var(--vz-card-bg) !important;
    border-color: var(--vz-input-border) !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.ck.ck-dropdown__panel {
    background: var(--vz-dropdown-bg) !important;
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    -webkit-animation-name: DropDownSlide;
    animation-name: DropDownSlide;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    border-radius: .3rem
}

.ck.ck-list {
    background: var(--vz-dropdown-bg) !important
}

.ck.ck-dropdown .ck-dropdown__panel.ck-dropdown__panel_ne,
.ck.ck-dropdown .ck-dropdown__panel.ck-dropdown__panel_se {
    left: 0;
    right: auto !important
}

.ck.ck-editor__editable_inline[dir=ltr] {
    text-align: left !important
}

.ck.ck-dropdown__panel {
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12) !important;
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12) !important;
    border-radius: .3rem !important;
    border: 1px solid var(--vz-border-color) !important
}

.ck.ck-button:active,
.ck.ck-button:focus,
a.ck.ck-button:active,
a.ck.ck-button:focus {
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    border: 1px solid var(--vz-light) !important
}

.ck.ck-button:not(.ck-disabled):hover,
a.ck.ck-button:not(.ck-disabled):hover {
    background: var(--vz-light) !important
}

.ck.ck-button.ck-on,
a.ck.ck-button.ck-on {
    background: var(--vz-light) !important
}

.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar,
.ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners {
    border-radius: .25rem !important;
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important
}

.ck-rounded-corners .ck.ck-editor__main>.ck-editor__editable,
.ck.ck-editor__main>.ck-editor__editable.ck-rounded-corners {
    border-radius: .25rem !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important
}

.ck-editor__editable {
    min-height: 245px !important
}

.ck[class*=ck-heading_heading] {
    font-weight: 500 !important
}

.ck.ck-button.ck-on:not(.ck-disabled):active,
.ck.ck-button.ck-on:not(.ck-disabled):hover,
a.ck.ck-button.ck-on:not(.ck-disabled):active,
a.ck.ck-button.ck-on:not(.ck-disabled):hover {
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.ck.ck-tooltip .ck-tooltip__text {
    background: #212529 !important;
    color: #f3f6f9 !important
}

.ck.ck-input-text {
    background: var(--vz-input-bg) !important;
    border: 1px solid var(--vz-input-border) !important
}

.ck.ck-input-text:focus {
    border: 1px solid var(--vz-input-focus-border) !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.ck.ck-balloon-panel {
    background: var(--vz-dropdown-bg) !important;
    border: 1px solid var(--vz-border-color) !important
}

.ck.ck-balloon-panel[class*=arrow_n]:after {
    border-bottom-color: var(--vz-dropdown-bg) !important
}

.ck.ck-balloon-panel[class*=arrow_n]::before {
    border-bottom-color: var(--vz-border-color) !important
}

.ck.ck-labeled-field-view>.ck.ck-labeled-field-view__input-wrapper>.ck.ck-label {
    background: var(--vz-dropdown-bg) !important
}

.ck-editor-reverse .ck-editor {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse
}

.ck-editor-reverse .ck.ck-editor__main>.ck-editor__editable {
    border: 1px solid var(--vz-input-border) !important;
    border-bottom: 0 !important
}

.ck-editor-reverse .ck-rounded-corners .ck.ck-editor__main>.ck-editor__editable,
.ck-editor-reverse .ck.ck-editor__main>.ck-editor__editable.ck-rounded-corners {
    border-radius: .25rem !important;
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important
}

.ck-editor-reverse .ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar,
.ck-editor-reverse .ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners {
    border-radius: .25rem !important;
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0 !important
}

[dir=rtl] .ck.ck-toolbar>.ck-toolbar__items {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
    -ms-flex-direction: row-reverse;
    flex-direction: row-reverse
}

.ql-editor {
    text-align: left
}

.ql-editor ol,
.ql-editor ul {
    padding-left: 1.5em;
    padding-right: 0
}

.ql-editor li:not(.ql-direction-rtl)::before {
    margin-left: -1.5em;
    margin-right: .3em;
    text-align: right
}

.ql-container {
    font-family: var(--vz-font-sans-serif)
}

.ql-container.ql-snow {
    border-color: var(--vz-input-border);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px
}

.ql-bubble {
    border: 1px solid var(--vz-input-border);
    border-radius: .25rem
}

.ql-toolbar {
    font-family: var(--vz-font-sans-serif) !important
}

.ql-toolbar span {
    outline: 0 !important;
    color: var(--vz-dropdown-link-color)
}

.ql-toolbar span:hover {
    color: #4b38b3 !important
}

.ql-toolbar.ql-snow {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-color: var(--vz-input-border)
}

.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
    border-color: transparent
}

.ql-toolbar.ql-snow .ql-picker-options {
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    border-radius: .3rem
}

.ql-snow .ql-script,
.ql-snow .ql-strike svg,
.ql-snow .ql-stroke {
    stroke: var(--vz-dropdown-link-color)
}

.ql-snow .ql-fill {
    fill: var(--vz-dropdown-link-color)
}

.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
    right: 0;
    left: auto
}

.ql-snow .ql-picker.ql-expanded .ql-picker-label {
    color: var(--vz-dropdown-link-color)
}

.ql-snow .ql-picker-options {
    background-color: var(--vz-dropdown-bg);
    border-color: var(--vz-border-color) !important
}

.gridjs-container {
    color: var(--vz-body-color);
    padding: 0;
    display: block
}

.gridjs-wrapper {
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 1px solid var(--vz-border-color);
    border-radius: 0
}

.gridjs-wrapper::-webkit-scrollbar {
    -webkit-appearance: none
}

.gridjs-wrapper::-webkit-scrollbar:vertical {
    width: 12px
}

.gridjs-wrapper::-webkit-scrollbar:horizontal {
    height: 12px
}

.gridjs-wrapper::-webkit-scrollbar-thumb {
    background-color: rgba(var(--vz-dark-rgb), .075);
    border-radius: 10px;
    border: 2px solid var(--vz-card-bg)
}

.gridjs-wrapper::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: var(--vz-card-bg)
}

.gridjs-footer {
    border: none !important;
    padding: 12px 0 0
}

.gridjs-table {
    width: 100%
}

.gridjs-tbody,
td.gridjs-td {
    background-color: transparent
}

td.gridjs-td,
th.gridjs-th {
    border: 1px solid var(--vz-border-color);
    padding: .75rem .6rem
}

th.gridjs-th {
    border-top: 0;
    color: var(--vz-body-color);
    background-color: rgba(var(--vz-light-rgb), .75)
}

th.gridjs-th-sort:focus,
th.gridjs-th-sort:hover {
    background-color: rgba(var(--vz-light-rgb), .85)
}

.gridjs-head {
    padding-top: 0
}

.gridjs-footer {
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 1px solid var(--vz-border-color);
    border-top: 0;
    background-color: transparent
}

.gridjs-summary {
    color: #878a99;
    margin-top: 8px !important
}

.gridjs-pagination .gridjs-pages button {
    margin-left: .3rem;
    border-radius: .25rem !important;
    border: 1px solid transparent;
    background-color: var(--vz-card-bg);
    color: var(--vz-link-color)
}

.gridjs-pagination .gridjs-pages button:last-child {
    border-right: 1px solid transparent
}

.gridjs-pagination .gridjs-pages button:disabled,
.gridjs-pagination .gridjs-pages button:hover:disabled,
.gridjs-pagination .gridjs-pages button[disabled] {
    color: #878a99;
    background-color: var(--vz-card-bg)
}

.gridjs-pagination .gridjs-pages button:hover {
    background-color: var(--vz-pagination-hover-bg);
    color: var(--vz-link-hover-color)
}

.gridjs-pagination .gridjs-pages button:focus {
    -webkit-box-shadow: none;
    box-shadow: none
}

.gridjs-pagination .gridjs-pages button:first-child:hover,
.gridjs-pagination .gridjs-pages button:last-child:hover {
    background-color: transparent
}

.gridjs-pagination .gridjs-pages button.gridjs-currentPage {
    background-color: #4b38b3;
    color: #fff;
    border-color: #4b38b3;
    font-weight: 500
}

.gridjs-search {
    position: relative;
    float: left
}

.gridjs-search::before {
    content: "\f0d1";
    font-family: remixicon;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    left: 10px;
    font-size: 14px;
    color: #878a99
}

input.gridjs-input {
    border-color: var(--vz-input-border);
    background-color: var(--vz-input-bg);
    color: var(--vz-body-color);
    line-height: 1.5;
    padding: .5rem .9rem .5rem 2.025rem;
    border-radius: .25rem;
    font-size: .8125rem
}

input.gridjs-input:focus {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-color: var(--vz-input-focus-border);
    background-color: var(--vz-input-bg)
}

input.gridjs-input::-webkit-input-placeholder {
    color: #878a99
}

input.gridjs-input::-moz-placeholder {
    color: #878a99
}

input.gridjs-input:-ms-input-placeholder {
    color: #878a99
}

input.gridjs-input::-ms-input-placeholder {
    color: #878a99
}

input.gridjs-input::placeholder {
    color: #878a99
}

th.gridjs-th .gridjs-th-content {
    float: none;
    display: inline-block;
    vertical-align: middle;
    font-weight: 600
}

button.gridjs-sort {
    float: none;
    display: inline-block;
    vertical-align: middle;
    width: 10px;
    height: 20px
}

th.gridjs-th-sort .gridjs-th-content {
    width: calc(100% - 10px)
}

button.gridjs-sort-asc,
button.gridjs-sort-desc {
    background-size: 7px
}

.table-card .gridjs-head {
    padding: 16px 16px 5px
}

.table-card .gridjs-wrapper {
    border-top: 0;
    border-radius: 0;
    border-width: 1px 0
}

.table-card .gridjs-footer {
    padding: 8px 16px
}

.gridjs-tr-selected td {
    background-color: rgba(var(--vz-light-rgb), 1)
}

.gridjs-tr-selected .gridjs-td .gridjs-checkbox[type=checkbox] {
    background-color: #4b38b3;
    border-color: #4b38b3;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e")
}

.gridjs-td .gridjs-checkbox {
    width: 1.1em;
    height: 1.1em;
    vertical-align: top;
    background-color: var(--vz-gray-300);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 1px solid var(--vz-gray-300);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact
}

.gridjs-td .gridjs-checkbox[type=checkbox] {
    border-radius: .25em
}

.gridjs-border-none td.gridjs-td,
.gridjs-border-none th.gridjs-th {
    border-right-width: 0;
    border-left-width: 0
}

.gridjs-loading-bar {
    background-color: var(--vz-card-bg)
}

[data-layout-mode=dark] button.gridjs-sort-asc,
[data-layout-mode=dark] button.gridjs-sort-desc,
[data-layout-mode=dark] button.gridjs-sort-neutral {
    -webkit-filter: invert(1) grayscale(100%) brightness(200%);
    filter: invert(1) grayscale(100%) brightness(200%)
}

.listjs-pagination {
    margin-bottom: 0;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    gap: 8px
}

.listjs-pagination li .page {
    display: block;
    padding: .375rem .75rem;
    color: var(--vz-link-color);
    background-color: var(--vz-card-bg);
    border: 1px solid transparent;
    border-radius: .25rem
}

.listjs-pagination li.active .page {
    color: #fff;
    background-color: #4b38b3;
    border-color: #4b38b3
}

.pagination-wrap {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.pagination-wrap a {
    text-decoration: none;
    display: inline-block
}

.pagination-next,
.pagination-prev {
    color: #4b38b3;
    font-weight: 500;
    padding: .375rem .75rem;
    background-color: var(--vz-card-bg);
    border: 1px solid transparent;
    border-radius: .25rem
}

.pagination-next:hover,
.pagination-prev:hover {
    color: #897bd6
}

.pagination-next.disabled,
.pagination-prev.disabled {
    color: #878a99;
    cursor: default
}

.pagination-next.disabled:hover,
.pagination-prev.disabled:hover {
    color: #878a99
}

.apex-charts {
    min-height: 10px !important
}

.apex-charts text {
    font-family: var(--vz-font-sans-serif) !important
}

.apex-charts .apexcharts-canvas {
    margin: 0 auto
}

.apexcharts-tooltip-text,
.apexcharts-tooltip-title {
    font-family: var(--vz-font-sans-serif) !important
}

.apexcharts-tooltip {
    border: 1px solid var(--vz-border-color) !important;
    background-color: var(--vz-card-bg) !important;
    -webkit-box-shadow: var(--vz-box-shadow) !important;
    box-shadow: var(--vz-box-shadow) !important
}

.apexcharts-tooltip * {
    font-family: var(--vz-font-sans-serif) !important;
    color: #878a99 !important
}

.apexcharts-tooltip .apexcharts-tooltip-title {
    background-color: rgba(var(--vz-light-rgb), .75) !important;
    border-bottom: 1px solid var(--vz-border-color) !important
}

.apexcharts-tooltip.apexcharts-theme-dark * {
    color: #fff !important
}

.apexcharts-legend-series {
    font-weight: 500
}

.apexcharts-gridline {
    pointer-events: none;
    stroke: rgba(var(--vz-light-rgb), .75)
}

.apexcharts-legend-text {
    color: #878a99 !important;
    font-family: var(--vz-font-sans-serif) !important;
    font-size: 13px !important
}

.apexcharts-pie-label {
    fill: #fff !important
}

.apexcharts-subtitle-text,
.apexcharts-title-text {
    fill: #adb5bd
}

.apexcharts-xaxis text,
.apexcharts-yaxis text {
    font-family: var(--vz-font-sans-serif) !important;
    fill: #adb5bd
}

.apexcharts-yaxis-title {
    font-weight: 500
}

#dynamicloadedchart-wrap {
    margin: 0 auto;
    max-width: 800px;
    position: relative
}

.chart-box {
    padding-left: 0
}

#chart-quarter,
#chart-year {
    width: 96%;
    max-width: 48%;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding-left: 0;
    padding-top: 20px;
    background: var(--vz-card-bg);
    border: 1px solid var(--vz-border-color)
}

#chart-year {
    float: left;
    position: relative;
    -webkit-transition: 1s ease transform;
    transition: 1s ease transform;
    z-index: 3
}

#chart-year.chart-quarter-activated {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-transition: 1s ease transform;
    transition: 1s ease transform
}

#chart-quarter {
    float: left;
    position: relative;
    z-index: -2;
    -webkit-transition: 1s ease transform;
    transition: 1s ease transform
}

#chart-quarter.active {
    -webkit-transition: 1.1s ease-in-out transform;
    transition: 1.1s ease-in-out transform;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    z-index: 1
}

@media screen and (min-width:480px) {
    #chart-year {
        -webkit-transform: translateX(50%);
        transform: translateX(50%)
    }

    #chart-quarter {
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%)
    }
}

.apexcharts-radar-series line,
.apexcharts-radar-series polygon {
    stroke: var(--vz-border-color)
}

.apexcharts-pie circle,
.apexcharts-pie line {
    stroke: var(--vz-border-color)
}

.apexcharts-pie text {
    fill: var(--vz-body-color)
}

.chartjs-chart {
    max-height: 320px
}

.e-charts {
    height: 350px
}

.e-charts-height {
    height: 300px
}

.gmaps,
.gmaps-panaroma {
    height: 300px;
    background: #f3f6f9;
    border-radius: 3px
}

.gmaps-overlay {
    display: block;
    text-align: center;
    color: #fff;
    font-size: 16px;
    line-height: 40px;
    background: #4b38b3;
    border-radius: 4px;
    padding: 10px 20px
}

.gmaps-overlay_arrow {
    left: 50%;
    margin-left: -16px;
    width: 0;
    height: 0;
    position: absolute
}

.gmaps-overlay_arrow.above {
    bottom: -15px;
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
    border-top: 16px solid #4b38b3
}

.gmaps-overlay_arrow.below {
    top: -15px;
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
    border-bottom: 16px solid #4b38b3
}

.autoComplete_wrapper {
    display: block
}

.autoComplete_wrapper>input {
    display: block;
    width: 100%;
    height: auto;
    padding: .5rem .9rem;
    font-size: .8125rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--vz-body-color);
    background-color: var(--vz-input-bg);
    background-clip: padding-box;
    border: 1px solid var(--vz-input-border);
    border-radius: .25rem;
    background-image: none
}

.autoComplete_wrapper>input::-webkit-input-placeholder {
    padding: 0 !important;
    color: #878a99 !important;
    font-size: .8125rem !important
}

.autoComplete_wrapper>input::-moz-placeholder {
    padding: 0 !important;
    color: #878a99 !important;
    font-size: .8125rem !important
}

.autoComplete_wrapper>input:-ms-input-placeholder {
    padding: 0 !important;
    color: #878a99 !important;
    font-size: .8125rem !important
}

.autoComplete_wrapper>input::-ms-input-placeholder {
    padding: 0 !important;
    color: #878a99 !important;
    font-size: .8125rem !important
}

.autoComplete_wrapper>input::placeholder {
    padding: 0 !important;
    color: #878a99 !important;
    font-size: .8125rem !important
}

.autoComplete_wrapper>input:focus {
    border: 1px solid var(--vz-input-focus-border);
    color: var(--vz-body-color)
}

.autoComplete_wrapper>input:hover {
    color: var(--vz-body-color)
}

.autoComplete_wrapper>ul {
    border-radius: .25rem;
    border-color: var(--vz-border-color);
    background-color: var(--vz-dropdown-bg);
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    padding: 0;
    overflow: auto;
    max-height: 160px;
    margin: 0;
    -webkit-animation-name: DropDownSlide;
    animation-name: DropDownSlide;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.autoComplete_wrapper>ul>li {
    font-size: .8125rem;
    margin: 0;
    padding: .35rem 1.2rem;
    border-radius: 0;
    background-color: var(--vz-dropdown-bg);
    color: var(--vz-body-color)
}

.autoComplete_wrapper>ul>li mark {
    color: #f06548;
    font-weight: 600;
    padding: 1px
}

.autoComplete_wrapper>ul>li:hover,
.autoComplete_wrapper>ul>li[aria-selected=true] {
    color: var(--vz-dropdown-link-hover-color);
    background-color: var(--vz-dropdown-link-hover-bg)
}

.autoComplete_wrapper>ul .no_result {
    padding: .7rem 1.2rem;
    font-style: italic;
    font-weight: 500
}

.jvm-tooltip {
    border-radius: 3px;
    background-color: #4b38b3;
    font-family: var(--vz-font-sans-serif);
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    padding: 5px 9px
}

.jvm-container text {
    font-family: var(--vz-font-sans-serif);
    font-size: .8125rem;
    fill: var(--vz-gray-700)
}

.jvm-zoom-btn {
    background-color: #4b38b3
}

.leaflet-map {
    height: 300px
}

.leaflet-map.leaflet-container {
    z-index: 0;
    font-family: Inter, sans-serif
}

.fc td,
.fc th {
    border: 1px solid var(--vz-border-color)
}

.fc .fc-toolbar h2 {
    font-size: 16px;
    line-height: 30px;
    text-transform: uppercase
}

@media (max-width:767.98px) {

    .fc .fc-toolbar .fc-center,
    .fc .fc-toolbar .fc-left,
    .fc .fc-toolbar .fc-right {
        float: none;
        display: block;
        text-align: center;
        clear: both;
        margin: 10px 0
    }

    .fc .fc-toolbar>*>* {
        float: none
    }

    .fc .fc-toolbar .fc-today-button {
        display: none
    }
}

.fc .fc-toolbar .btn {
    text-transform: capitalize
}

.fc .fc-col-header-cell {
    background-color: var(--vz-light)
}

.fc .fc-col-header-cell-cushion {
    display: block;
    padding: 8px 4px
}

.fc .fc-daygrid-day-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    margin: 2px
}

.fc .fc-daygrid-day.fc-day-today {
    background-color: rgba(75, 56, 179, .1)
}

.fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-number {
    background-color: #4b38b3;
    color: #fff
}

.fc .fc-daygrid-day.fc-day-today {
    background-color: rgba(75, 56, 179, .1)
}

.fc .fc-timegrid-col.fc-day-today {
    background-color: rgba(75, 56, 179, .1)
}

.fc .fc-list-event:hover td {
    background: 0 0
}

.fc .fc-list-event-title a {
    color: #fff !important
}

.fc .fc-col-header,
.fc .fc-daygrid-body,
.fc .fc-scrollgrid-sync-table {
    width: 100% !important
}

.fc .fc-scrollgrid-section>* {
    border-left: 1px solid var(--vz-border-color);
    border-top: 1px solid var(--vz-border-color)
}

.fc .fc-scrollgrid-section-liquid>td {
    border-top: 0
}

.fc-theme-bootstrap a:not([href]) {
    color: var(--vz-body-color)
}

.fc-event {
    color: #fff
}

.fc th.fc-widget-header {
    background: #e9ebec;
    line-height: 20px;
    padding: 10px 0;
    text-transform: uppercase;
    font-weight: 700
}

.fc-unthemed .fc-content,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-heading td,
.fc-unthemed .fc-list-view,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-row,
.fc-unthemed tbody,
.fc-unthemed td,
.fc-unthemed th,
.fc-unthemed thead {
    border-color: #e9ebec
}

.fc-unthemed td.fc-today {
    background: #f6f8fa
}

.fc-button {
    background: var(--vz-card-bg);
    border-color: var(--vz-border-color);
    color: #495057;
    text-transform: capitalize;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 6px 12px !important;
    height: auto !important
}

.fc-state-active,
.fc-state-disabled,
.fc-state-down {
    background-color: #4b38b3;
    color: #fff;
    text-shadow: none
}

.fc-event {
    border-radius: 2px;
    border: none;
    cursor: move;
    font-size: .8125rem;
    margin: 5px 7px;
    padding: 5px 5px;
    text-align: center
}

.fc-event,
.fc-event-dot {
    background-color: #4b38b3
}

.fc-daygrid-dot-event.fc-event-mirror,
.fc-daygrid-dot-event:hover {
    background-color: #4b38b3
}

.fc-event-title,
.fc-sticky {
    font-weight: 600 !important;
    text-overflow: ellipsis;
    white-space: nowrap
}

.fc-daygrid-event-dot {
    border-color: #fff !important;
    display: none
}

.fc-event-time {
    display: none
}

.fc-event .fc-content {
    color: #fff
}

#external-events .external-event {
    text-align: left;
    padding: 8px 16px;
    margin: 6px 0
}

.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark .fc-content {
    color: #f3f6f9
}

.fc-next-button,
.fc-prev-button {
    position: relative;
    height: 37.5px;
    width: 37.5px
}

.fc-next-button::before,
.fc-prev-button::before {
    position: absolute;
    font-family: remixicon !important;
    font-size: 20px;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.fc-prev-button::before {
    content: "\ea64"
}

.fc-next-button::before {
    content: "\ea6e"
}

.fc-toolbar-chunk .btn-group {
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.fc-toolbar-chunk .btn-group .btn {
    color: #3577f1;
    background-color: rgba(53, 119, 241, .15);
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none
}

.fc-toolbar-chunk .btn-group .btn.active,
.fc-toolbar-chunk .btn-group .btn:hover {
    color: #fff;
    background-color: #3577f1
}

.fc-toolbar-chunk .fc-today-button {
    background-color: #3577f1 !important;
    border: #3577f1
}

@media (max-width:575.98px) {
    .fc-toolbar {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        gap: 16px
    }
}

#upcoming-event-list .card:last-child {
    margin-bottom: 6px !important
}

#event-modal .event-details {
    display: none
}

#event-modal .view-event .event-form {
    display: none
}

#event-modal .view-event #event-category-tag {
    display: none
}

#event-modal .view-event .event-details {
    display: block
}

.fc-daygrid-event-harness .fc-daygrid-event {
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px)
}

.fc-timegrid-event-harness .fc-timegrid-event {
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px)
}

.fc-timegrid-slots table tr {
    border-color: var(--vz-border-color) !important
}

.fc-list-table {
    border-color: var(--vz-border-color)
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-primary .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-primary .fc-event-title {
    color: #4b38b3 !important
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-soft-primary .fc-event-title {
    color: #4b38b3 !important
}

.fc-list-table .fc-list-event.bg-soft-primary {
    color: #4b38b3 !important
}

.fc-list-table .fc-list-event.bg-soft-primary .fc-list-event-title>a {
    color: #4b38b3 !important
}

.fc-list-table .fc-list-event.bg-soft-primary .fc-list-event-dot {
    border-color: #4b38b3
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-secondary .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-secondary .fc-event-title {
    color: #3577f1 !important
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-soft-secondary .fc-event-title {
    color: #3577f1 !important
}

.fc-list-table .fc-list-event.bg-soft-secondary {
    color: #3577f1 !important
}

.fc-list-table .fc-list-event.bg-soft-secondary .fc-list-event-title>a {
    color: #3577f1 !important
}

.fc-list-table .fc-list-event.bg-soft-secondary .fc-list-event-dot {
    border-color: #3577f1
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-success .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-success .fc-event-title {
    color: #45cb85 !important
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-soft-success .fc-event-title {
    color: #45cb85 !important
}

.fc-list-table .fc-list-event.bg-soft-success {
    color: #45cb85 !important
}

.fc-list-table .fc-list-event.bg-soft-success .fc-list-event-title>a {
    color: #45cb85 !important
}

.fc-list-table .fc-list-event.bg-soft-success .fc-list-event-dot {
    border-color: #45cb85
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-info .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-info .fc-event-title {
    color: #299cdb !important
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-soft-info .fc-event-title {
    color: #299cdb !important
}

.fc-list-table .fc-list-event.bg-soft-info {
    color: #299cdb !important
}

.fc-list-table .fc-list-event.bg-soft-info .fc-list-event-title>a {
    color: #299cdb !important
}

.fc-list-table .fc-list-event.bg-soft-info .fc-list-event-dot {
    border-color: #299cdb
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-warning .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-warning .fc-event-title {
    color: #ffbe0b !important
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-soft-warning .fc-event-title {
    color: #ffbe0b !important
}

.fc-list-table .fc-list-event.bg-soft-warning {
    color: #ffbe0b !important
}

.fc-list-table .fc-list-event.bg-soft-warning .fc-list-event-title>a {
    color: #ffbe0b !important
}

.fc-list-table .fc-list-event.bg-soft-warning .fc-list-event-dot {
    border-color: #ffbe0b
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-danger .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-danger .fc-event-title {
    color: #f06548 !important
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-soft-danger .fc-event-title {
    color: #f06548 !important
}

.fc-list-table .fc-list-event.bg-soft-danger {
    color: #f06548 !important
}

.fc-list-table .fc-list-event.bg-soft-danger .fc-list-event-title>a {
    color: #f06548 !important
}

.fc-list-table .fc-list-event.bg-soft-danger .fc-list-event-dot {
    border-color: #f06548
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-light .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-light .fc-event-title {
    color: #f3f6f9 !important
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-soft-light .fc-event-title {
    color: #f3f6f9 !important
}

.fc-list-table .fc-list-event.bg-soft-light {
    color: #f3f6f9 !important
}

.fc-list-table .fc-list-event.bg-soft-light .fc-list-event-title>a {
    color: #f3f6f9 !important
}

.fc-list-table .fc-list-event.bg-soft-light .fc-list-event-dot {
    border-color: #f3f6f9
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-dark .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-dark .fc-event-title {
    color: #212529 !important
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-soft-dark .fc-event-title {
    color: #212529 !important
}

.fc-list-table .fc-list-event.bg-soft-dark {
    color: #212529 !important
}

.fc-list-table .fc-list-event.bg-soft-dark .fc-list-event-title>a {
    color: #212529 !important
}

.fc-list-table .fc-list-event.bg-soft-dark .fc-list-event-dot {
    border-color: #212529
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-dark .fc-event-main,
.fc-daygrid-event-harness .fc-daygrid-event.bg-soft-dark .fc-event-title {
    color: var(--vz-dark) !important
}

.fc-direction-ltr {
    direction: ltr
}

.fc-direction-ltr .fc-toolbar>*>:not(:first-child) {
    margin-left: .75em
}

.fg-emoji-picker {
    width: 250px !important;
    -webkit-box-shadow: var(--vz-box-shadow) !important;
    box-shadow: var(--vz-box-shadow) !important;
    top: auto !important;
    bottom: 130px
}

.fg-emoji-picker * {
    font-family: var(--vz-font-sans-serif) !important;
    color: #212529 !important
}

@media (max-width:991.98px) {
    .fg-emoji-picker {
        left: 14px !important;
        top: auto !important;
        bottom: 118px
    }
}

.fg-emoji-picker .fg-emoji-picker-container-title {
    color: #212529 !important
}

.fg-emoji-picker .fg-emoji-picker-search {
    height: 40px !important
}

.fg-emoji-picker .fg-emoji-picker-search input {
    background-color: var(--vz-input-bg) !important;
    color: var(--vz-body-color) !important;
    padding: .5rem .9rem !important;
    font-size: .8125rem !important
}

.fg-emoji-picker .fg-emoji-picker-search input::-webkit-input-placeholder {
    color: #878a99 !important
}

.fg-emoji-picker .fg-emoji-picker-search input::-moz-placeholder {
    color: #878a99 !important
}

.fg-emoji-picker .fg-emoji-picker-search input:-ms-input-placeholder {
    color: #878a99 !important
}

.fg-emoji-picker .fg-emoji-picker-search input::-ms-input-placeholder {
    color: #878a99 !important
}

.fg-emoji-picker .fg-emoji-picker-search input::placeholder {
    color: #878a99 !important
}

.fg-emoji-picker .fg-emoji-picker-search svg {
    fill: #212529 !important;
    right: 11px;
    top: 12px
}

.fg-emoji-picker .fg-emoji-picker-categories {
    background-color: #f3f6f9 !important
}

.fg-emoji-picker .fg-emoji-picker-categories li.active {
    background-color: rgba(var(--bs-primary-rgb), .2)
}

.fg-emoji-picker .fg-emoji-picker-categories a:hover {
    background-color: rgba(var(--bs-primary-rgb), .2)
}

.fg-emoji-picker-grid>li:hover {
    background-color: rgba(var(--bs-primary-rgb), .2) !important
}

a.fg-emoji-picker-close-button {
    background-color: #e5ecf2 !important
}

table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
    text-align: center;
    padding: 50px;
    font-weight: 600;
    --vz-table-accent-bg: $card-bg
}

table.dataTable>thead .sorting:before,
table.dataTable>thead .sorting_asc:before,
table.dataTable>thead .sorting_asc_disabled:before,
table.dataTable>thead .sorting_desc:before,
table.dataTable>thead .sorting_desc_disabled:before {
    content: "\f0360";
    position: absolute;
    right: .5rem;
    top: 12px;
    font-size: .8rem;
    font-family: "Material Design Icons"
}

table.dataTable>thead .sorting:after,
table.dataTable>thead .sorting_asc:after,
table.dataTable>thead .sorting_asc_disabled:after,
table.dataTable>thead .sorting_desc:after,
table.dataTable>thead .sorting_desc_disabled:after {
    content: "\f035d";
    position: absolute;
    right: .5rem;
    top: 18px;
    font-size: .8rem;
    font-family: "Material Design Icons"
}

table.dataTable>tbody>tr.child span.dtr-title {
    font-weight: 600;
    min-width: 150px
}

table.dataTable.dtr-inline.collapsed>tbody>tr>td.dtr-control::before,
table.dataTable.dtr-inline.collapsed>tbody>tr>th.dtr-control::before {
    background-color: #4b38b3;
    border-color: var(--vz-card-bg)
}

table.dataTable.dtr-inline.collapsed>tbody>tr.parent.parent>th.dtr-control::before {
    background-color: #f06548
}

table.dataTable.dtr-inline.collapsed>tbody>tr.parent>td.dtr-control::before {
    background-color: #f06548
}

table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 135px
}

.table-card .dataTables_filter,
.table-card .dataTables_length {
    padding: 1rem 1rem;
    padding-bottom: 0
}

.table-card .dataTables_info,
.table-card .dataTables_paginate {
    padding: 1rem 1rem
}

.table-card div.dataTables_wrapper .col-md-6 {
    width: 100%
}

.table-card div.dataTables_wrapper div.dataTables_filter input {
    width: calc(100% - 52px)
}

.table-card div.dataTables_wrapper div.dataTables_filter label {
    display: block
}

div.dtr-modal div.dtr-modal-display {
    background-color: var(--vz-modal-bg);
    border-color: var(--vz-border-color);
    -webkit-box-shadow: none;
    box-shadow: none;
    height: 70%
}

div.dtr-modal div.dtr-modal-content {
    padding: 1.25rem
}

div.dtr-modal div.dtr-modal-content h2 {
    font-size: 1.015625rem;
    font-weight: 600;
    margin-bottom: 15px
}

div.dtr-modal div.dtr-modal-content .dtr-details tr td {
    padding: .75rem .6rem
}

div.dtr-modal div.dtr-modal-close {
    font-size: 24px;
    top: 9px;
    right: 11px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    background-color: var(--vz-card-bg);
    border-color: var(--vz-border-color)
}

div.dtr-modal div.dtr-modal-close:hover {
    background-color: var(--vz-light)
}

.dt-buttons a.dt-button,
.dt-buttons button.dt-button,
.dt-buttons div.dt-button,
.dt-buttons input.dt-button {
    border-color: var(--vz-border-color);
    background: var(--vz-light)
}

.select2.select2-container {
    width: 100% !important
}

.select2-container .select2-selection--single {
    border: 1px solid var(--vz-input-border);
    height: calc(1.5em + 1rem + 2px);
    background-color: var(--vz-input-bg);
    outline: 0
}

.select2-container .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
    color: var(--vz-body-color)
}

.select2-container .select2-selection--single .select2-selection__arrow {
    height: 34px;
    width: 34px;
    right: 3px
}

.select2-container .select2-selection--single .select2-selection__arrow b {
    border-color: var(--vz-input-border) transparent transparent transparent;
    border-width: 6px 6px 0 6px
}

.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent var(--vz-input-border) transparent !important;
    border-width: 0 6px 6px 6px !important
}

.select2-results__option {
    padding: 6px 12px
}

.select2-dropdown {
    border: var(--vz-dropdown-border-width) solid var(--vz-border-color);
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow);
    background-color: var(--vz-dropdown-bg);
    z-index: 1056
}

.select2-container--default .select2-results__option--selected {
    background-color: var(--vz-input-disabled-bg)
}

.select2-container--default .select2-search--dropdown {
    padding: 10px;
    background-color: var(--vz-dropdown-bg)
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    outline: 0;
    border: 1px solid var(--vz-input-border);
    background-color: var(--vz-input-bg);
    color: var(--vz-body-color);
    border-radius: .25rem
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #4b38b3
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: var(--vz-dropdown-bg);
    color: var(--vz-dropdown-link-hover-color)
}

.select2-container--default .select2-results__option[aria-selected=true]:hover {
    background-color: #4b38b3;
    color: #fff
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__display {
    padding-left: 36px;
    padding-right: 5px
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    right: 1px;
    left: auto
}

.select2-container .select2-selection--multiple {
    min-height: calc(1.5em + 1rem + 2px);
    border: 1px solid var(--vz-input-border) !important;
    background-color: var(--vz-input-bg)
}

.select2-container .select2-selection--multiple .select2-selection__rendered {
    padding: 1px 4px
}

.select2-container .select2-selection--multiple .select2-search__field {
    border: 0;
    color: var(--vz-body-color)
}

.select2-container .select2-selection--multiple .select2-selection__choice {
    background-color: #4b38b3;
    border: none;
    color: #fff;
    border-radius: 3px;
    padding: 3px;
    margin-top: 6px
}

.select2-container .select2-selection--multiple .select2-selection__choice__remove {
    color: #fff;
    margin-right: 7px;
    border-color: #5d4cbb;
    padding: 0 8px;
    top: 3px
}

.select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #fff;
    background-color: #4b38b3
}

.select2-container .select2-search--inline .select2-search__field {
    margin-top: 7px;
    font-family: var(--vz-font-sans-serif)
}

.select2-container .select2-search textarea::-webkit-input-placeholder {
    color: #878a99
}

.select2-container .select2-search textarea::-moz-placeholder {
    color: #878a99
}

.select2-container .select2-search textarea:-ms-input-placeholder {
    color: #878a99
}

.select2-container .select2-search textarea::-ms-input-placeholder {
    color: #878a99
}

.select2-container .select2-search textarea::placeholder {
    color: #878a99
}

.select2-container--default.select2-container--disabled .select2-selection--multiple,
.select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: var(--vz-input-disabled-bg);
    cursor: default
}

.toastify {
    padding: 12px 16px;
    color: #fff;
    display: inline-block;
    -webkit-box-shadow: 0 3px 6px -1px rgba(0, 0, 0, .12), 0 10px 36px -4px rgba(77, 96, 232, .3);
    box-shadow: 0 3px 6px -1px rgba(0, 0, 0, .12), 0 10px 36px -4px rgba(77, 96, 232, .3);
    background: #45cb85;
    position: fixed;
    opacity: 0;
    -webkit-transition: all .4s cubic-bezier(.215, .61, .355, 1);
    transition: all .4s cubic-bezier(.215, .61, .355, 1);
    border-radius: 2px;
    cursor: pointer;
    text-decoration: none;
    max-width: calc(50% - 20px);
    z-index: 2147483647
}

.toastify.on {
    opacity: 1
}

.toast-close {
    opacity: .4;
    padding: 0 5px;
    position: relative;
    left: 4px;
    margin-left: 4px
}

.toastify-right {
    right: 15px
}

.toastify-left {
    left: 15px
}

.toastify-left .toast-close {
    left: -4px;
    margin-left: 0;
    margin-right: 4px
}

.toastify-top {
    top: -150px
}

.toastify-bottom {
    bottom: -150px
}

.toastify-rounded {
    border-radius: 25px
}

.toastify-avatar {
    width: 1.5em;
    height: 1.5em;
    margin: -7px 5px;
    border-radius: 2px
}

.toastify-center {
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
    max-width: -webkit-fit-content;
    max-width: fit-content;
    max-width: -moz-fit-content
}

@media only screen and (max-width:360px) {

    .toastify-left,
    .toastify-right {
        margin-left: auto;
        margin-right: auto;
        left: 0;
        right: 0;
        max-width: -webkit-fit-content;
        max-width: -moz-fit-content;
        max-width: fit-content
    }
}

.choices {
    position: relative;
    margin-bottom: 24px;
    font-size: 16px
}

.choices:focus {
    outline: 0
}

.choices:last-child {
    margin-bottom: 0
}

.choices.is-disabled .choices__inner,
.choices.is-disabled .choices__input {
    background-color: rgba(var(--vz-light-rgb), .75);
    cursor: not-allowed;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.choices.is-disabled .choices__item {
    cursor: not-allowed
}

.choices [hidden] {
    display: none !important
}

.choices[data-type*=select-one] {
    cursor: pointer
}

.choices[data-type*=select-one] .choices__inner {
    padding-bottom: .25rem
}

.choices[data-type*=select-one] .choices__input {
    display: block;
    width: 100%;
    padding: .5rem .9rem;
    background-color: var(--vz-input-bg);
    border: 1px solid var(--vz-input-border);
    font-size: .8125rem;
    border-radius: .25rem;
    color: var(--vz-body-color);
    margin-bottom: 10px
}

.choices[data-type*=select-one] .choices__button {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
    padding: 0;
    background-size: 8px;
    position: absolute;
    top: 50%;
    right: 0;
    left: auto;
    margin-top: -10px;
    margin-right: 25px;
    margin-left: 0;
    height: 20px;
    width: 20px;
    border-radius: 10em;
    opacity: .5
}

.choices[data-type*=select-one] .choices__button:focus,
.choices[data-type*=select-one] .choices__button:hover {
    opacity: 1
}

.choices[data-type*=select-one] .choices__button:focus {
    -webkit-box-shadow: 0 0 0 2px #00bcd4;
    box-shadow: 0 0 0 2px #00bcd4
}

.choices[data-type*=select-one] .choices__item[data-value=""] .choices__button {
    display: none
}

.choices[data-type*=select-one]:after {
    content: "\f0140";
    position: absolute;
    border: 0;
    border-color: #212529 transparent transparent;
    right: 11.5px;
    left: auto;
    font-family: "Material Design Icons";
    width: auto;
    height: auto;
    margin-top: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    pointer-events: none
}

.choices[data-type*=select-one].is-open:after {
    margin-top: 0;
    -webkit-transform: translateY(-50%) rotate(-180deg);
    transform: translateY(-50%) rotate(-180deg)
}

.choices[data-type*=select-multiple] .choices__inner,
.choices[data-type*=text] .choices__inner {
    cursor: text;
    padding-right: .9rem
}

.choices[data-type*=select-multiple] .choices__button,
.choices[data-type*=text] .choices__button {
    position: relative;
    display: inline-block;
    margin-top: 0;
    margin-right: -4px;
    margin-bottom: 0;
    margin-left: 8px;
    padding-left: 16px;
    border-left: 1px solid rgba(255, 255, 255, .5);
    border-right: 0;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
    background-size: 8px;
    width: 8px;
    line-height: 1;
    opacity: .75;
    border-radius: 0
}

.choices[data-type*=select-multiple] .choices__button:focus,
.choices[data-type*=select-multiple] .choices__button:hover,
.choices[data-type*=text] .choices__button:focus,
.choices[data-type*=text] .choices__button:hover {
    opacity: 1
}

.choices[data-type*=select-multiple] .choices__list--dropdown,
.choices[data-type*=text] .choices__list--dropdown {
    padding-bottom: 10px
}

.choices[data-type*=select-multiple] .choices__list--dropdown .choices__list,
.choices[data-type*=text] .choices__list--dropdown .choices__list {
    margin-bottom: 0
}

.choices[data-type*=select-multiple] .choices__input {
    padding-top: 3px
}

.input-light .choices__inner {
    background-color: var(--vz-light);
    border: none
}

.choices__inner {
    display: inline-block;
    vertical-align: middle;
    width: 100%;
    background-color: var(--vz-input-bg);
    padding: .25rem 3.6rem .1rem .5rem;
    border: 1px solid var(--vz-input-border);
    border-radius: .25rem !important;
    font-size: .8125rem;
    min-height: 37.5px;
    overflow: hidden
}

.is-focused .choices__inner,
.is-open .choices__inner {
    border-color: var(--vz-input-focus-border)
}

.choices__list {
    margin: 0;
    padding-left: 0;
    list-style: none
}

.choices__list--single {
    display: inline-block;
    padding: 4px 16px 4px 4px;
    width: 100%
}

.choices__list--single .choices__item {
    width: 100%
}

.choices__list--multiple {
    display: inline
}

.choices__list--multiple .choices__item {
    display: inline-block;
    vertical-align: initial;
    border-radius: 7px;
    padding: 2px 7px;
    font-size: 11px;
    font-weight: 400;
    margin-right: 3.75px;
    margin-bottom: 3.75px;
    margin-top: 2px;
    background-color: #4b38b3;
    border: 1px solid #4b38b3;
    word-break: break-all;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #fff;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.choices__list--multiple .choices__item[data-deletable] {
    padding-right: 5px
}

.choices__list--multiple .choices__item.is-highlighted {
    background-color: #4b38b3;
    border: 1px solid #4b38b3
}

.is-disabled .choices__list--multiple .choices__item {
    background-color: #3577f1;
    border: 1px solid #3577f1
}

.choices__list--dropdown {
    visibility: hidden;
    z-index: 1;
    position: absolute;
    width: 100%;
    background-color: var(--vz-dropdown-bg);
    border: 1px solid var(--vz-border-color);
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    top: 100%;
    margin-top: 0;
    padding: 10px 10px 20px 10px;
    border-bottom-left-radius: 2.5px;
    border-bottom-right-radius: 2.5px;
    overflow: hidden;
    word-break: break-all;
    will-change: visibility;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-transform: translateY(1px);
    transform: translateY(1px)
}

.choices__list--dropdown.is-active {
    visibility: visible;
    -webkit-animation-name: DropDownSlide;
    animation-name: DropDownSlide
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
    background-color: var(--vz-dropdown-link-hover-bg)
}

.choices__list--dropdown .choices__list {
    margin: 0 -16px -16px
}

.choices__list--dropdown .has-no-results {
    font-style: italic;
    font-weight: 500
}

.choices__list--dropdown .choices__item--selectable:after {
    display: none
}

.is-open .choices__list--dropdown {
    border-color: var(--vz-border-color)
}

.is-flipped .choices__list--dropdown {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: -1px;
    border-radius: .25rem .25rem 0 0
}

.is-flipped .choices__list--dropdown.is-active {
    -webkit-animation-name: DropDownSlideDown;
    animation-name: DropDownSlideDown;
    -webkit-transform: translateY(-1px);
    transform: translateY(-1px)
}

.choices__list--dropdown .choices__list {
    position: relative;
    max-height: 300px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position
}

.choices__list--dropdown .choices__item {
    position: relative;
    font-size: .8125rem;
    padding: .35rem 1.2rem .35rem 16px
}

@media (min-width:640px) {
    .choices__list--dropdown .choices__item--selectable:after {
        content: attr(data-select-text);
        font-size: 12px;
        opacity: 0;
        position: absolute;
        right: 10px;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%)
    }

    .choices__list--dropdown .choices__item--selectable.is-highlighted:after {
        opacity: .5
    }
}

.choices__item {
    cursor: default
}

.choices__item--selectable {
    cursor: pointer
}

.choices__item--disabled {
    cursor: not-allowed;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    opacity: .5
}

.choices__heading {
    font-weight: 600;
    font-size: 12px;
    padding: 10px 16px;
    border-bottom: 1px solid var(--vz-border-color);
    color: #878a99
}

.choices__button {
    text-indent: -9999px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 0;
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer
}

.choices__button:focus {
    outline: 0
}

.choices__input {
    display: inline-block;
    vertical-align: baseline;
    background-color: var(--vz-input-bg);
    color: var(--vz-body-color);
    font-size: .8125rem;
    margin-bottom: 0;
    border: 0;
    border-radius: 0;
    max-width: 100%;
    padding: 2px 0 2px 2px
}

.choices__input:focus {
    outline: 0
}

.choices__input::-webkit-input-placeholder {
    color: #878a99
}

.choices__input::-moz-placeholder {
    color: #878a99
}

.choices__input:-ms-input-placeholder {
    color: #878a99
}

.choices__input::-ms-input-placeholder {
    color: #878a99
}

.choices__input::placeholder {
    color: #878a99
}

.choices__placeholder {
    color: #878a99;
    opacity: 1
}

[data-layout-mode=dark] .choices[data-type*=select-one] .choices__button {
    -webkit-filter: invert(1) grayscale(100%) brightness(200%);
    filter: invert(1) grayscale(100%) brightness(200%)
}

.flatpickr-calendar {
    background: 0 0;
    opacity: 0;
    display: none;
    text-align: center;
    visibility: hidden;
    padding: 0;
    -webkit-animation: none;
    animation: none;
    direction: ltr;
    border: 0;
    font-size: 14px;
    line-height: 24px;
    border-radius: 5px;
    position: absolute;
    width: 307.875px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    background: var(--vz-dropdown-bg);
    -webkit-box-shadow: 1px 0 0 var(--vz-border-color), -1px 0 0 var(--vz-border-color), 0 1px 0 var(--vz-border-color), 0 -1px 0 var(--vz-border-color), 0 3px 13px rgba(0, 0, 0, .08);
    box-shadow: 1px 0 0 var(--vz-border-color), -1px 0 0 var(--vz-border-color), 0 1px 0 var(--vz-border-color), 0 -1px 0 var(--vz-border-color), 0 3px 13px rgba(0, 0, 0, .08)
}

.flatpickr-calendar.inline,
.flatpickr-calendar.open {
    opacity: 1;
    max-height: 640px;
    visibility: visible
}

.flatpickr-calendar.open {
    display: inline-block;
    z-index: 1056
}

.flatpickr-calendar.animate.open {
    -webkit-animation: fpFadeInDown .3s cubic-bezier(.23, 1, .32, 1);
    animation: fpFadeInDown .3s cubic-bezier(.23, 1, .32, 1)
}

.flatpickr-calendar.inline {
    display: block;
    position: relative;
    top: 2px;
    width: 100%;
    -webkit-box-shadow: none;
    box-shadow: none
}

.flatpickr-calendar.inline .flatpickr-rContainer {
    display: block;
    width: 100%
}

.flatpickr-calendar.inline .flatpickr-rContainer .flatpickr-days {
    width: 100%;
    border: 1px solid var(--vz-input-border);
    border-top: none;
    border-radius: 0 0 5px 5px
}

.flatpickr-calendar.inline .flatpickr-rContainer .flatpickr-days .dayContainer {
    width: 100%;
    min-width: 100%;
    max-width: 100%
}

.flatpickr-calendar.inline .flatpickr-rContainer .flatpickr-days .dayContainer .flatpickr-day {
    max-width: 100%;
    border-radius: 4px
}

.flatpickr-calendar.inline .flatpickr-time {
    border: 1px solid var(--vz-input-border) !important;
    border-radius: .25rem
}

.flatpickr-calendar.static {
    position: absolute;
    top: calc(100% + 2px)
}

.flatpickr-calendar.static.open {
    z-index: 999;
    display: block
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7) {
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1) {
    -webkit-box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
    box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6
}

.flatpickr-calendar .hasTime .dayContainer,
.flatpickr-calendar .hasWeeks .dayContainer {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.flatpickr-calendar .hasWeeks .dayContainer {
    border-left: 0
}

.flatpickr-calendar.hasTime .flatpickr-time {
    height: 40px;
    border-top: 1px solid var(--vz-border-color)
}

.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
    height: auto
}

.flatpickr-calendar::after,
.flatpickr-calendar::before {
    position: absolute;
    display: block;
    pointer-events: none;
    border: solid transparent;
    content: "";
    height: 0;
    width: 0;
    left: 22px
}

.flatpickr-calendar.arrowRight::after,
.flatpickr-calendar.arrowRight::before,
.flatpickr-calendar.rightMost::after,
.flatpickr-calendar.rightMost::before {
    left: auto;
    right: 22px
}

.flatpickr-calendar.arrowCenter::after,
.flatpickr-calendar.arrowCenter::before {
    left: 50%;
    right: 50%
}

.flatpickr-calendar::before {
    border-width: 5px;
    margin: 0 -5px
}

.flatpickr-calendar::after {
    border-width: 4px;
    margin: 0 -4px
}

.flatpickr-calendar.arrowTop::after,
.flatpickr-calendar.arrowTop::before {
    bottom: 100%
}

.flatpickr-calendar.arrowTop::before {
    border-bottom-color: #e6e6e6
}

.flatpickr-calendar.arrowTop::after {
    border-bottom-color: #fff
}

.flatpickr-calendar.arrowBottom::after,
.flatpickr-calendar.arrowBottom::before {
    top: 100%
}

.flatpickr-calendar.arrowBottom::before {
    border-top-color: #e6e6e6
}

.flatpickr-calendar.arrowBottom::after {
    border-top-color: #fff
}

.flatpickr-calendar:focus {
    outline: 0
}

.flatpickr-wrapper {
    position: relative;
    display: inline-block
}

.flatpickr-months {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background-color: #4b38b3;
    border-radius: 5px 5px 0 0
}

.flatpickr-months .flatpickr-month {
    background: 0 0;
    color: rgba(255, 255, 255, .9);
    fill: rgba(255, 255, 255, .9);
    height: 34px;
    line-height: 1;
    text-align: center;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
    text-decoration: none;
    cursor: pointer;
    position: absolute;
    top: 0;
    height: 34px;
    padding: 10px;
    z-index: 3;
    color: rgba(255, 255, 255, .9);
    fill: rgba(255, 255, 255, .9)
}

.flatpickr-months .flatpickr-next-month.flatpickr-disabled,
.flatpickr-months .flatpickr-prev-month.flatpickr-disabled {
    display: none
}

.flatpickr-months .flatpickr-next-month i,
.flatpickr-months .flatpickr-prev-month i {
    position: relative
}

.flatpickr-months .flatpickr-next-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month {
    left: 0
}

.flatpickr-months .flatpickr-next-month.flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month {
    right: 0
}

.flatpickr-months .flatpickr-next-month:hover,
.flatpickr-months .flatpickr-prev-month:hover {
    color: #959ea9
}

.flatpickr-months .flatpickr-next-month:hover svg,
.flatpickr-months .flatpickr-prev-month:hover svg {
    fill: rgba(255, 255, 255, .9)
}

.flatpickr-months .flatpickr-next-month svg,
.flatpickr-months .flatpickr-prev-month svg {
    width: 14px;
    height: 14px
}

.flatpickr-months .flatpickr-next-month svg path,
.flatpickr-months .flatpickr-prev-month svg path {
    -webkit-transition: fill .1s;
    transition: fill .1s;
    fill: inherit
}

.numInputWrapper {
    position: relative;
    height: auto
}

.numInputWrapper input,
.numInputWrapper span {
    display: inline-block
}

.numInputWrapper input {
    width: 100%
}

.numInputWrapper input::-ms-clear {
    display: none
}

.numInputWrapper input::-webkit-inner-spin-button,
.numInputWrapper input::-webkit-outer-spin-button {
    margin: 0;
    -webkit-appearance: none
}

.numInputWrapper span {
    position: absolute;
    right: 0;
    width: 14px;
    padding: 0 4px 0 2px;
    height: 50%;
    line-height: 50%;
    opacity: 0;
    cursor: pointer;
    border: 1px solid rgba(var(--vz-dark-rgb), .15);
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.numInputWrapper span:hover {
    background: rgba(0, 0, 0, .1)
}

.numInputWrapper span:active {
    background: rgba(0, 0, 0, .2)
}

.numInputWrapper span:after {
    display: block;
    content: "";
    position: absolute
}

.numInputWrapper span.arrowUp {
    top: 0;
    border-bottom: 0
}

.numInputWrapper span.arrowUp:after {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid rgba(57, 57, 57, .6);
    top: 26%
}

.numInputWrapper span.arrowDown {
    top: 50%
}

.numInputWrapper span.arrowDown:after {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid rgba(57, 57, 57, .6);
    top: 40%
}

.numInputWrapper span svg {
    width: inherit;
    height: auto
}

.numInputWrapper span svg path {
    fill: rgba(0, 0, 0, .5)
}

.numInputWrapper:hover {
    background: rgba(0, 0, 0, .05)
}

.numInputWrapper:hover span {
    opacity: 1
}

.flatpickr-current-month {
    font-size: 100%;
    line-height: inherit;
    font-weight: 300;
    color: inherit;
    position: absolute;
    width: 75%;
    left: 12.5%;
    padding: 7.48px 0 0 0;
    line-height: 1;
    height: 34px;
    display: inline-block;
    text-align: center;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.flatpickr-current-month span.cur-month {
    font-family: inherit;
    font-weight: 700;
    color: inherit;
    display: inline-block;
    margin-left: .5ch;
    padding: 0
}

.flatpickr-current-month span.cur-month:hover {
    background: rgba(0, 0, 0, .05)
}

.flatpickr-current-month .numInputWrapper {
    width: 6ch;
    display: inline-block
}

.flatpickr-current-month span.arrowUp::after {
    border-bottom-color: rgba(0, 0, 0, .9)
}

.flatpickr-current-month span.arrowDown:after {
    border-top-color: rgba(0, 0, 0, .9)
}

.flatpickr-current-month input.cur-year {
    background: 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: inherit;
    cursor: text;
    padding: 0 0 0 .5ch;
    margin: 0;
    display: inline-block;
    font-size: inherit;
    font-family: inherit;
    font-weight: 600;
    line-height: inherit;
    height: auto;
    border: 0;
    border-radius: 0;
    vertical-align: initial;
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield
}

.flatpickr-current-month input.cur-year:focus {
    outline: 0
}

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
    font-size: 100%;
    color: rgba(255, 255, 255, .9);
    background: 0 0;
    pointer-events: none
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    -webkit-appearance: menulist;
    -moz-appearance: menulist;
    appearance: menulist;
    background: 0 0;
    border: none;
    border-radius: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: inherit;
    cursor: pointer;
    font-size: inherit;
    font-family: inherit;
    font-weight: 600;
    height: auto;
    line-height: inherit;
    margin: -1px 0 0 0;
    outline: 0;
    padding: 0 0 0 .5ch;
    position: relative;
    vertical-align: initial;
    width: auto
}

.flatpickr-current-month .flatpickr-monthDropdown-months:active,
.flatpickr-current-month .flatpickr-monthDropdown-months:focus {
    outline: 0
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
    background-color: transparent
}

.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
    background-color: transparent;
    outline: 0;
    padding: 0;
    color: rgba(0, 0, 0, .8)
}

.flatpickr-weekdays {
    background-color: #4b38b3;
    text-align: center;
    overflow: hidden;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 36px;
    border-bottom: 1px solid var(--vz-border-color)
}

.flatpickr-weekdays .flatpickr-weekdaycontainer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

span.flatpickr-weekday {
    cursor: default;
    font-size: 90%;
    background: 0 0;
    color: #fff;
    line-height: 1;
    margin: 0;
    text-align: center;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-weight: 500
}

.dayContainer,
.flatpickr-weeks {
    padding: 1px 0 0 0
}

.flatpickr-days {
    position: relative;
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    width: 307.875px
}

.flatpickr-days:focus {
    outline: 0
}

.dayContainer {
    padding: 0;
    outline: 0;
    text-align: left;
    width: 307.875px;
    min-width: 307.875px;
    max-width: 307.875px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-pack: distribute;
    justify-content: space-around;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1
}

.dayContainer+.dayContainer {
    -webkit-box-shadow: -1px 0 0 #e6e6e6;
    box-shadow: -1px 0 0 #e6e6e6
}

.flatpickr-day {
    background: 0 0;
    border: 1px solid transparent;
    border-radius: 150px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--vz-body-color);
    cursor: pointer;
    font-weight: 400;
    width: 14.2857143%;
    -ms-flex-preferred-size: 14.2857143%;
    flex-basis: 14.2857143%;
    max-width: 39px;
    height: 39px;
    line-height: 39px;
    margin: 0;
    display: inline-block;
    position: relative;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center
}

.flatpickr-day:focus,
.flatpickr-day:hover {
    background-color: rgba(var(--vz-light-rgb), .7)
}

.flatpickr-day.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day.nextMonthDay:focus,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.today.inRange,
.flatpickr-day:focus,
.flatpickr-day:hover {
    cursor: pointer;
    outline: 0;
    background-color: var(--vz-light);
    border-color: var(--vz-light)
}

.flatpickr-day.today {
    border-color: #4b38b3;
    background-color: rgba(75, 56, 179, .1);
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.flatpickr-day.today:focus,
.flatpickr-day.today:hover {
    border-color: #4b38b3;
    background-color: rgba(75, 56, 179, .15);
    color: var(--vz-dark)
}

.flatpickr-day.endRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.endRange.nextMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.endRange:focus,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected,
.flatpickr-day.selected.inRange,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.selected:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.startRange:focus,
.flatpickr-day.startRange:hover {
    background: #4b38b3;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow);
    color: #fff;
    border-color: #4b38b3
}

.flatpickr-day.endRange.startRange,
.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange {
    border-radius: 50px 0 0 50px
}

.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)),
.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)) {
    -webkit-box-shadow: -10px 0 0 #4b38b3;
    box-shadow: -10px 0 0 #4b38b3
}

.flatpickr-day.endRange.startRange .endRange,
.flatpickr-day.selected.startRange .endRange,
.flatpickr-day.startRange.startRange .endRange {
    border-radius: 50px
}

.flatpickr-day.endRange.endRange,
.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange {
    border-radius: 0 50px 50px 0
}

.flatpickr-day.inRange {
    border-radius: 0;
    -webkit-box-shadow: -5px 0 0 var(--vz-light), 5px 0 0 var(--vz-light);
    box-shadow: -5px 0 0 var(--vz-light), 5px 0 0 var(--vz-light)
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.nextMonthDay,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.prevMonthDay {
    color: rgba(var(--vz-body-color-rgb), .3);
    background: 0 0;
    border-color: transparent;
    cursor: default
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
    cursor: not-allowed;
    color: rgba(var(--vz-body-color-rgb), .3)
}

.flatpickr-day.week.selected {
    border-radius: 0;
    -webkit-box-shadow: -5px 0 0 #4b38b3, 5px 0 0 #4b38b3;
    box-shadow: -5px 0 0 #4b38b3, 5px 0 0 #4b38b3
}

.flatpickr-day.hidden {
    visibility: hidden
}

.rangeMode .flatpickr-day {
    margin-top: 1px
}

.flatpickr-weekwrapper {
    float: left
}

.flatpickr-weekwrapper .flatpickr-weeks {
    padding: 0 12px;
    -webkit-box-shadow: 1px 0 0 #e6e6e6;
    box-shadow: 1px 0 0 #e6e6e6
}

.flatpickr-weekwrapper .flatpickr-weekday {
    float: none;
    width: 100%;
    line-height: 28px
}

.flatpickr-weekwrapper span.flatpickr-day,
.flatpickr-weekwrapper span.flatpickr-day:hover {
    display: block;
    width: 100%;
    max-width: none;
    color: rgba(var(--vz-dark-rgb), .3);
    background: 0 0;
    cursor: default;
    border: none
}

.flatpickr-innerContainer {
    display: block;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden
}

.flatpickr-rContainer {
    display: inline-block;
    padding: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.flatpickr-time {
    text-align: center;
    outline: 0;
    display: block;
    height: 0;
    line-height: 40px;
    max-height: 40px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.flatpickr-time:after {
    content: "";
    display: table;
    clear: both
}

.flatpickr-time .numInputWrapper {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    width: 40%;
    height: 40px;
    float: left
}

.flatpickr-time .numInputWrapper span.arrowDown:after,
.flatpickr-time .numInputWrapper span.arrowUp:after {
    border-bottom-color: var(--vz-border-color)
}

.flatpickr-time.hasSeconds .numInputWrapper {
    width: 26%
}

.flatpickr-time.time24hr .numInputWrapper {
    width: 49%
}

.flatpickr-time input {
    background: 0 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 0;
    border-radius: 0;
    text-align: center;
    margin: 0;
    padding: 0;
    height: inherit;
    line-height: inherit;
    color: var(--vz-body-color);
    font-size: 14px;
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield
}

.flatpickr-time input.flatpickr-hour {
    font-weight: 700
}

.flatpickr-time input.flatpickr-minute,
.flatpickr-time input.flatpickr-second {
    font-weight: 400
}

.flatpickr-time input:focus {
    outline: 0;
    border: 0
}

.flatpickr-time .flatpickr-am-pm,
.flatpickr-time .flatpickr-time-separator {
    height: inherit;
    float: left;
    line-height: inherit;
    color: var(--vz-body-color);
    font-weight: 700;
    width: 2%;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-flex-item-align: center;
    align-self: center
}

.flatpickr-time .flatpickr-am-pm {
    outline: 0;
    width: 18%;
    cursor: pointer;
    text-align: center;
    font-weight: 400
}

.flatpickr-time .flatpickr-am-pm:focus,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time input:hover {
    background: rgba(75, 56, 179, .04)
}

.flatpickr-am-pm:focus,
.flatpickr-am-pm:hover,
.numInput:focus,
.numInput:hover,
.numInputWrapper:focus,
.numInputWrapper:hover {
    background-color: transparent
}

.flatpickr-input[readonly] {
    cursor: pointer;
    background-color: var(--vz-input-bg)
}

[data-inline-date=true],
[data-time-inline] {
    display: none
}

@-webkit-keyframes fpFadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

@keyframes fpFadeInDown {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, -20px, 0);
        transform: translate3d(0, -20px, 0)
    }

    to {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}

[datepicker-inline],
[timepicker-inline] {
    display: none
}

.auth-page-wrapper .auth-page-content {
    padding-bottom: 60px;
    position: relative;
    z-index: 2;
    width: 100%
}

.auth-page-wrapper .footer {
    left: 0;
    background-color: transparent;
    color: #212529
}

.auth-one-bg-position {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 380px
}

@media (max-width:575.98px) {
    .auth-one-bg-position {
        height: 280px
    }
}

.auth-one-bg {
    background-image: url(../images/background.jpg);
    background-position: center;
    background-size: cover
}

.auth-one-bg .bg-overlay {
    background: -webkit-gradient(linear, left top, right top, from(#41319c), to(#4b38b3));
    background: linear-gradient(to right, #41319c, #4b38b3);
    opacity: .9
}

.auth-one-bg .shape {
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 1;
    pointer-events: none
}

.auth-one-bg .shape>svg {
    width: 100%;
    height: auto;
    fill: var(--vz-body-bg)
}

.auth-pass-inputgroup input[type=text]+.btn .ri-eye-fill:before {
    content: "\ecb6"
}

.particles-js-canvas-el {
    position: relative
}

.signin-other-title {
    position: relative
}

.signin-other-title:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 1px;
    left: 0;
    right: 0;
    border-top: 1px dashed var(--vz-border-color);
    top: 10px
}

.signin-other-title .title {
    display: inline-block;
    position: relative;
    z-index: 9;
    background-color: var(--vz-card-bg);
    padding: 2px 16px
}

.auth-bg-cover {
    background: linear-gradient(-45deg, #4b38b3 50%, #45cb85)
}

.auth-bg-cover>.bg-overlay {
    /* background-image: url(../images/cover-pattern.png); */
    background-position: center;
    background-size: cover;
    opacity: 1;
    background-color: transparent
}

.auth-bg-cover .footer {
    color: rgba(255, 255, 255, .5)
}

#password-contain {
    display: none
}

#password-contain p {
    padding-left: 13px
}

#password-contain p.valid {
    color: #45cb85
}

#password-contain p.valid::before {
    position: relative;
    left: -8px;
    content: "✔"
}

#password-contain p.invalid {
    color: #f06548
}

#password-contain p.invalid::before {
    position: relative;
    left: -8px;
    content: "✖"
}

.dash-filter-picker {
    min-width: 210px !important
}

.upcoming-scheduled {
    position: relative
}

.upcoming-scheduled .flatpickr-months {
    position: absolute !important;
    top: -45px !important;
    left: auto !important;
    right: 0 !important;
    width: 200px;
    background-color: transparent
}

.upcoming-scheduled .flatpickr-months .flatpickr-month {
    color: #878a99 !important;
    fill: #878a99 !important
}

.upcoming-scheduled .flatpickr-months .flatpickr-next-month,
.upcoming-scheduled .flatpickr-months .flatpickr-prev-month {
    display: none
}

.upcoming-scheduled .flatpickr-calendar {
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.upcoming-scheduled .flatpickr-calendar .flatpickr-current-month {
    font-size: 13px;
    width: 100%;
    left: 0
}

.upcoming-scheduled .flatpickr-calendar .flatpickr-monthDropdown-months {
    border: 1px solid var(--vz-border-color);
    border-radius: 4px;
    height: 26px
}

.upcoming-scheduled .flatpickr-calendar .flatpickr-weekdays {
    background-color: var(--vz-light);
    border: none
}

.upcoming-scheduled .flatpickr-calendar .flatpickr-weekdays span.flatpickr-weekday {
    color: var(--vz-dark)
}

.upcoming-scheduled .flatpickr-calendar .flatpickr-day.today {
    color: #fff !important;
    background-color: #45cb85;
    border-color: #45cb85 !important
}

.upcoming-scheduled .flatpickr-calendar .flatpickr-day.today:hover {
    color: #45cb85 !important;
    background-color: rgba(69, 203, 133, .2) !important
}

.upcoming-scheduled .flatpickr-calendar .flatpickr-day.selected {
    background-color: #45cb85 !important;
    border-color: #45cb85 !important;
    color: #fff
}

.upcoming-scheduled .flatpickr-calendar .numInputWrapper {
    width: 7.5ch;
    margin-left: 10px
}

.upcoming-scheduled .flatpickr-days {
    border: none !important
}

.crm-widget .col {
    border-right: 1px solid var(--vz-border-color)
}

.crm-widget .col:last-child {
    border: 0
}

@media (min-width:768px) and (max-width:1399.98px) {
    .crm-widget .col:nth-child(3) {
        border-right: 0
    }

    .crm-widget .col:last-child {
        border-right: 1px solid var(--vz-border-color)
    }
}

@media (max-width:767.98px) {
    .crm-widget .col {
        border-right: 0;
        border-bottom: 1px solid var(--vz-border-color)
    }
}

@media (min-width:1400px) and (max-width:1599.98px) {

    .project-wrapper .col-xxl-4,
    .project-wrapper>.col-xxl-8 {
        width: 100%
    }
}

.crypto-widget {
    max-width: 130px !important
}

.bg-marketplace {
    /* background-image: url(../images/nft/marketplace.png); */
    background-size: cover
}

.dash-countdown .countdownlist .count-num {
    background-color: var(--vz-card-bg);
    padding: 16px 8px;
    font-size: 22px
}

@media (max-width:575.98px) {
    .dash-countdown .countdownlist .count-num {
        font-size: 16px;
        padding: 8px 6px
    }
}

@media (max-width:575.98px) {
    .dash-countdown .countdownlist .count-title {
        font-size: 10px
    }
}

.marketplace-icon {
    position: absolute;
    float: right;
    top: 30px;
    left: 30px
}

.marketplace-swiper .swiper-button-next,
.marketplace-swiper .swiper-button-prev {
    top: 34px;
    width: 28px;
    height: 28px;
    background-color: rgba(75, 56, 179, .1);
    color: #4b38b3;
    border-radius: .3rem;
    right: 16px !important
}

.marketplace-swiper .swiper-button-prev {
    right: 58px !important;
    left: auto !important
}

.dash-collection .content {
    background-color: rgba(255, 255, 255, .25);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px)
}

@media (max-width:1441.98px) {

    .dash-nft .col-xxl-3,
    .dash-nft .col-xxl-9 {
        width: 100% !important;
        max-width: 100% !important
    }
}

.jvectormap-legend-cnt-h .jvectormap-legend-tick-sample {
    width: 32px;
    height: 32px;
    display: inline-block;
    vertical-align: middle
}

.timeline {
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto
}

.timeline::after {
    content: "";
    position: absolute;
    width: 2px;
    background: var(--vz-card-bg);
    top: 57px;
    bottom: 166px;
    left: 50%;
    margin-left: -1.5px
}

.timeline-item {
    padding: 30px 60px;
    position: relative;
    background: inherit;
    width: 50%
}

.timeline-item .icon {
    position: absolute;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    width: 60px;
    height: 60px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 25px;
    top: 30px;
    right: -30px;
    padding: 9px 0;
    background: var(--vz-card-bg);
    border: 1px solid var(--vz-border-color);
    border-radius: 50px;
    color: #45cb85;
    z-index: 1
}

.timeline-item .date {
    position: absolute;
    display: inline-block;
    width: calc(100% - 48px);
    top: 50px;
    font-size: 14px;
    font-weight: 500;
    font-style: italic
}

.timeline-item .content {
    padding: 20px;
    background: var(--vz-card-bg);
    position: relative;
    border: 1px solid var(--vz-border-color);
    border-radius: .25rem;
    -webkit-box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075);
    box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075)
}

.timeline-item.left {
    left: 0
}

.timeline-item.left .date {
    left: calc(100% + 48px);
    text-align: start
}

.timeline-item.right {
    left: 50%
}

.timeline-item.right .icon {
    left: -30px
}

.timeline-item.right .date {
    right: calc(100% + 48px);
    text-align: end
}

.timeline-item.right::before {
    left: 28px;
    border-color: transparent transparent transparent #fff
}

@media (max-width:991.98px) {
    .timeline::after {
        left: 112px;
        bottom: 180px
    }

    .timeline-item {
        width: 100%;
        padding-left: 165px;
        padding-right: 0
    }

    .timeline-item.left,
    .timeline-item.right {
        left: 0
    }

    .timeline-item.left .icon,
    .timeline-item.right .icon {
        width: 45px;
        height: 45px;
        top: 37.5px;
        font-size: 18px;
        left: 90px
    }

    .timeline-item.left::before,
    .timeline-item.right::before {
        left: 110px;
        border-color: transparent transparent transparent var(--vz-border-color)
    }

    .timeline-item.left .date,
    .timeline-item.right .date {
        right: auto;
        left: 0;
        width: 79px
    }
}

.timeline-2 {
    position: relative
}

.timeline-2::after {
    position: absolute;
    content: "";
    width: 2px;
    height: 83%;
    top: 50px;
    left: 40px;
    margin-left: -1px;
    background: var(--vz-card-bg)
}

.timeline-2 .timeline-year {
    position: relative;
    width: 100%;
    text-align: left;
    z-index: 1
}

.timeline-2 .timeline-year p {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    width: 80px;
    height: 80px;
    margin: 0;
    padding: 23px 10px;
    background: var(--vz-card-bg);
    border-radius: 50px;
    text-transform: uppercase;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center
}

.timeline-2 .timeline-year p span {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis
}

.timeline-2 .timeline-date {
    font-size: 14px;
    font-weight: 500;
    margin: 24px 0 0 0;
    margin-left: 55px
}

.timeline-2 .timeline-date::after {
    content: "";
    display: block;
    position: absolute;
    width: 14px;
    height: 14px;
    top: 26px;
    left: 45px;
    -webkit-box-align: left;
    -ms-flex-align: left;
    align-items: left;
    background: #45cb85;
    border: 3px solid var(--vz-card-bg);
    border-radius: 50px;
    z-index: 1
}

.timeline-2 .timeline-box {
    position: relative;
    display: inline-block;
    margin: 23px 62px;
    padding: 20px;
    border: 1px solid var(--vz-border-color);
    border-radius: 6px;
    background: var(--vz-card-bg);
    max-width: 695px
}

.timeline-2 .timeline-box::after {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    top: 26px;
    right: 100%;
    border-color: transparent var(--vz-card-bg) transparent transparent;
    border-width: 10px
}

.timeline-2 .timeline-box::before {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    right: 100%;
    top: 24px;
    border-color: transparent var(--vz-border-color) transparent transparent;
    border-width: 12px
}

.timeline-2 .timeline-box .timeline-text {
    position: relative;
    float: left
}

.timeline-2 .timeline-launch {
    position: relative;
    display: inline-block;
    border: 1px solid var(--vz-border-color);
    border-radius: 6px;
    background: #fff;
    width: 100%;
    margin-top: 15px;
    padding: 0;
    border: none;
    text-align: left;
    background: 0 0
}

.timeline-2 .timeline-launch .timeline-box {
    margin-left: 0
}

.timeline-2 .timeline-launch .timeline-box::after {
    left: 30px;
    margin-left: 0;
    top: -20px;
    border-color: transparent transparent var(--vz-border-color) transparent
}

.timeline-2 .timeline-launch .timeline-box::before {
    left: 30px;
    margin-left: 0;
    top: -19px;
    border-color: transparent transparent var(--vz-card-bg) transparent;
    border-width: 10px;
    z-index: 1
}

.horizontal-timeline {
    position: relative;
    width: 100%;
    margin: 0 auto
}

.horizontal-timeline::before {
    content: "";
    position: absolute;
    width: 100%;
    top: 174px;
    left: 0;
    height: 2px;
    background-color: var(--vz-card-bg)
}

.horizontal-timeline .swiper-slide .item-box {
    margin: 227px 0 0;
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.horizontal-timeline .swiper-slide .item-box::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    background: #4b38b3;
    width: 13px;
    height: 13px;
    top: -59px;
    border-radius: 50px;
    border: 3px solid var(--vz-card-bg)
}

.horizontal-timeline .swiper-slide .timeline-content {
    min-height: 110px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    background-color: var(--vz-card-bg)
}

.horizontal-timeline .swiper-slide .timeline-content::before {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    left: 0;
    top: -23px;
    border-color: transparent transparent var(--vz-card-bg) transparent;
    border-width: 12px;
    right: 0;
    margin: 0 auto
}

.horizontal-timeline .swiper-slide .time {
    position: absolute;
    top: -86px;
    right: 0;
    left: 0;
    margin: 0 auto
}

.horizontal-timeline .swiper-slide:nth-child(even) {
    margin-top: 5px;
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg)
}

.horizontal-timeline .swiper-slide:nth-child(even) .timeline-content {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.horizontal-timeline .swiper-slide:nth-child(even) .timeline-content::before {
    bottom: -23px;
    top: auto;
    border-color: var(--vz-card-bg) transparent transparent transparent
}

.horizontal-timeline .swiper-slide:nth-child(even) .time {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.horizontal-timeline .swiper-button-next,
.horizontal-timeline .swiper-button-prev {
    height: 40px;
    width: 40px;
    line-height: 40px;
    border-radius: 50%;
    background-color: #4f3bbd
}

.horizontal-timeline .swiper-button-next::after,
.horizontal-timeline .swiper-button-prev::after {
    font-size: 24px;
    color: #fff
}

.horizontal-timeline .swiper-button-next.swiper-button-disabled,
.horizontal-timeline .swiper-button-prev.swiper-button-disabled {
    background-color: rgba(75, 56, 179, .5);
    opacity: 1;
    cursor: auto;
    -webkit-backdrop-filter: blur(25px);
    backdrop-filter: blur(25px);
    pointer-events: none
}

.horizontal-timeline .swiper-button-next {
    right: 0
}

.horizontal-timeline .swiper-button-next::after {
    content: "\ea6e";
    font-family: remixicon
}

.horizontal-timeline .swiper-button-prev {
    left: 0
}

.horizontal-timeline .swiper-button-prev::after {
    content: "\ea64";
    font-family: remixicon
}

.acitivity-timeline {
    position: relative;
    overflow: hidden
}

.acitivity-timeline .acitivity-item {
    position: relative
}

.acitivity-timeline .acitivity-item .flex-shrink-0 {
    z-index: 2
}

.acitivity-timeline .acitivity-item .acitivity-avatar {
    background-color: var(--vz-card-bg);
    border: 3px solid var(--vz-card-bg);
    height: 32px;
    width: 32px
}

.acitivity-timeline .acitivity-item:before {
    content: "";
    position: absolute;
    border-left: 1px dashed var(--vz-border-color);
    left: 16px;
    height: 100%;
    top: 0;
    z-index: 0
}

.acitivity-timeline .acitivity-item:last-child::before {
    border-color: transparent
}

.categories-filter .list-inline-item {
    position: relative;
    margin-right: 0
}

.categories-filter .list-inline-item a {
    display: block;
    color: var(--vz-body-color);
    font-weight: 600;
    padding: 8px 15px;
    margin: 5px;
    cursor: pointer
}

.gallery-box {
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
    margin-bottom: 10px;
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: transparent;
    padding: 8px
}

.gallery-box::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    width: 100%;
    height: 0;
    background-color: var(--vz-card-bg);
    -webkit-transition: all .4s;
    transition: all .4s
}

.gallery-box .gallery-container {
    position: relative;
    overflow: hidden;
    border-radius: 4px
}

.gallery-box .gallery-container a {
    display: block
}

.gallery-box .gallery-container .gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    opacity: 0;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(10%, rgba(0, 0, 0, 0)), color-stop(60%, rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, .5)));
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, .5) 100%);
    visibility: hidden;
    overflow: hidden;
    -webkit-transition: all .4s ease-in-out 0s;
    transition: all .4s ease-in-out 0s;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    padding: 16px
}

.gallery-box .gallery-container .gallery-overlay .overlay-caption {
    color: #fff;
    margin: 0;
    font-size: 16px
}

.gallery-box .gallery-img {
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.gallery-box .box-content {
    position: relative
}

.gallery-box .box-content .title {
    font-size: 14px;
    margin-bottom: 4px;
    display: none
}

.gallery-box .box-content .post {
    margin: 0;
    -webkit-transition: all .2s;
    transition: all .2s;
    color: #878a99
}

.gallery-box:hover::before {
    height: 100%
}

.gallery-box:hover .box-content {
    bottom: 0
}

.gallery-box:hover .box-content .post {
    opacity: 1
}

.gallery-box:hover .gallery-overlay {
    opacity: 1;
    visibility: visible
}

.gallery-light .gallery-box::before {
    background-color: var(--vz-light)
}

.error-basic-img {
    max-width: 450px
}

.error-500 .title {
    font-size: 250px
}

.error-500 .error-500-img {
    position: absolute;
    top: 57px;
    left: 0;
    right: 0;
    margin: 0 auto
}

@media (min-width:768px) and (max-width:991.98px) {
    .error-500 .title {
        font-size: 150px
    }

    .error-500 .error-500-img {
        width: 20% !important;
        top: 43px
    }
}

@media (max-width:767.98px) {
    .error-500 .title {
        font-size: 68px;
        margin-top: 35px
    }

    .error-500 .error-500-img {
        position: relative;
        top: 0
    }
}

@-webkit-keyframes errorAnimation {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    15% {
        -webkit-transform: translateX(-25%) rotate(-5deg);
        transform: translateX(-25%) rotate(-5deg)
    }

    30% {
        -webkit-transform: translateX(20%) rotate(3deg);
        transform: translateX(20%) rotate(3deg)
    }

    45% {
        -webkit-transform: translateX(-15%) rotate(-3deg);
        transform: translateX(-15%) rotate(-3deg)
    }

    60% {
        -webkit-transform: translateX(10%) rotate(2deg);
        transform: translateX(10%) rotate(2deg)
    }

    75% {
        -webkit-transform: translateX(-5%) rotate(-1deg);
        transform: translateX(-5%) rotate(-1deg)
    }

    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes errorAnimation {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    15% {
        -webkit-transform: translateX(-25%) rotate(-5deg);
        transform: translateX(-25%) rotate(-5deg)
    }

    30% {
        -webkit-transform: translateX(20%) rotate(3deg);
        transform: translateX(20%) rotate(3deg)
    }

    45% {
        -webkit-transform: translateX(-15%) rotate(-3deg);
        transform: translateX(-15%) rotate(-3deg)
    }

    60% {
        -webkit-transform: translateX(10%) rotate(2deg);
        transform: translateX(10%) rotate(2deg)
    }

    75% {
        -webkit-transform: translateX(-5%) rotate(-1deg);
        transform: translateX(-5%) rotate(-1deg)
    }

    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

.error-img {
    -webkit-animation: errorAnimation 20s infinite;
    animation: errorAnimation 20s infinite
}

.error-text {
    text-shadow: 4px 4px rgba(69, 203, 133, .4)
}

@media (min-width:1200px) {
    .error-text {
        font-size: 10rem
    }
}

.profile-wid-bg {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: 320px
}

@media (max-width:575.98px) {
    .profile-wid-bg {
        height: 445px
    }
}

.profile-wid-bg::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    opacity: .9;
    background: #4b38b3;
    background: -webkit-gradient(linear, left bottom, left top, from(#221a52), to(#4b38b3));
    background: linear-gradient(to top, #221a52, #4b38b3)
}

.profile-wid-bg .profile-wid-img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover
}

.profile-nav.nav-pills .nav-link {
    color: rgba(255, 255, 255, .8)
}

.profile-nav.nav-pills .nav-link::before {
    background-color: rgba(255, 255, 255, .1)
}

.profile-project-card {
    border: 1px solid var(--vz-border-color);
    border-left: 3px solid var(--vz-border-color)
}

.profile-project-card.profile-project-primary {
    border-left-color: #4b38b3
}

.profile-project-card.profile-project-secondary {
    border-left-color: #3577f1
}

.profile-project-card.profile-project-success {
    border-left-color: #45cb85
}

.profile-project-card.profile-project-info {
    border-left-color: #299cdb
}

.profile-project-card.profile-project-warning {
    border-left-color: #ffbe0b
}

.profile-project-card.profile-project-danger {
    border-left-color: #f06548
}

.profile-project-card.profile-project-light {
    border-left-color: #f3f6f9
}

.profile-project-card.profile-project-dark {
    border-left-color: #212529
}

.user-profile-img {
    position: relative
}

.user-profile-img .profile-img {
    width: 100%;
    height: 250px;
    -o-object-fit: cover;
    object-fit: cover
}

@media (max-width:991px) {
    .user-profile-img .profile-img {
        height: 160px
    }
}

.user-profile-img .profile-foreground-img-file-input {
    display: none
}

.user-profile-img .profile-photo-edit {
    cursor: pointer
}

.profile-user {
    position: relative;
    display: inline-block
}

.profile-user .profile-photo-edit {
    position: absolute;
    right: 0;
    left: auto;
    bottom: 0;
    cursor: pointer
}

.profile-user .user-profile-image {
    -o-object-fit: cover;
    object-fit: cover
}

.profile-user .profile-img-file-input {
    display: none
}

.profile-timeline .accordion-item {
    position: relative
}

.profile-timeline .accordion-item .accordion-button {
    background-color: transparent
}

.profile-timeline .accordion-item .accordion-button::after {
    background: 0 0
}

.profile-timeline .accordion-item::before {
    content: "";
    border-left: 2px dashed var(--vz-border-color);
    position: absolute;
    height: 100%;
    left: 23px
}

.profile-timeline .accordion-item:first-child::before {
    top: 8px
}

.profile-timeline .accordion-item:last-child::before {
    height: 20px;
    top: 3px
}

.profile-setting-img {
    position: relative;
    height: 260px
}

.profile-setting-img .overlay-content {
    content: "";
    position: absolute;
    top: 0;
    right: 0
}

.profile-setting-img .profile-img {
    width: 100%;
    height: 250px;
    -o-object-fit: cover;
    object-fit: cover
}

@media (max-width:991.98px) {
    .profile-setting-img .profile-img {
        height: 160px
    }
}

.profile-setting-img .profile-foreground-img-file-input {
    display: none
}

.profile-setting-img .profile-photo-edit {
    cursor: pointer
}

@media (max-width:575.98px) {
    [data-layout=horizontal] .profile-foreground {
        margin-top: 0 !important
    }
}

.sitemap-content {
    width: 100%;
    max-width: 1142px;
    margin: 0 auto;
    padding: 0 20px
}

.sitemap-content * {
    position: relative
}

.sitemap-horizontal {
    position: relative
}

.sitemap-horizontal ul {
    padding: 0;
    margin: 0;
    list-style: none
}

.sitemap-horizontal ul a {
    display: block;
    background: var(--vz-light);
    border: 2px solid var(--vz-card-bg);
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow);
    font-size: .8125rem;
    height: 60px;
    padding: 8px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.sitemap-horizontal ul a span {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis
}

.administration .director>li>a {
    width: 50%;
    margin: 0 auto 0 auto;
    border-radius: 4px
}

.administration .subdirector {
    position: absolute;
    width: 100%
}

.administration .subdirector::after {
    content: "";
    display: block;
    width: 0;
    height: 130px;
    border-left: 2px dashed var(--vz-border-color);
    left: 45.45%;
    position: relative
}

.administration .subdirector>li:first-child {
    width: 18.59%;
    height: 64px;
    margin: 0 auto 92px auto;
    padding-top: 25px;
    border-bottom: 2px dashed var(--vz-border-color);
    z-index: 1;
    float: right;
    right: 27.2%;
    border-left: 2px dashed var(--vz-border-color)
}

.administration .subdirector>li:first-child a {
    width: 100%;
    left: 25px
}

@media screen and (max-width:767px) {
    .administration .subdirector>li:first-child {
        width: 40%;
        right: 10%;
        margin-right: 2px
    }

    .administration .subdirector::after {
        left: 49.8%
    }
}

.departments {
    width: 100%
}

.departments>li:first-child {
    width: 18.59%;
    height: 64px;
    margin: 0 auto 92px auto;
    padding-top: 25px;
    border-bottom: 2px dashed var(--vz-border-color);
    z-index: 1;
    float: left;
    left: 27%
}

.departments>li:first-child a {
    width: 100%;
    right: 25px
}

.departments>li:nth-child(2) {
    margin-left: 0;
    clear: left
}

.departments>li:nth-child(2).department:before {
    border: none
}

.departments::after {
    content: "";
    display: block;
    position: absolute;
    width: 81.1%;
    height: 22px;
    border-top: 2px dashed var(--vz-border-color);
    border-right: 2px dashed var(--vz-border-color);
    border-left: 2px dashed var(--vz-border-color);
    margin: 0 auto;
    top: 130px;
    left: 9.1%
}

@media screen and (max-width:767px) {
    .departments>li:first-child {
        width: 40%;
        left: 10%;
        margin-left: 2px
    }

    .departments::after {
        border-right: none;
        left: 0;
        width: 50%
    }
}

.department {
    border-left: 2px dashed var(--vz-border-color);
    float: left;
    margin-left: 1.75%;
    margin-bottom: 60px;
    width: 18.25%
}

.department::before {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 22px;
    border-left: 2px dashed var(--vz-border-color);
    z-index: 1;
    top: -22px;
    left: 50%;
    margin-left: -4px
}

.department>a {
    margin: 0 0 -26px -4px;
    z-index: 1
}

.department ul {
    margin-top: 0;
    margin-bottom: 0
}

.department ul li {
    padding-left: 25px;
    border-bottom: 2px dashed var(--vz-border-color);
    height: 80px
}

.department ul li a {
    background: var(--vz-card-bg);
    margin-top: 48px;
    position: absolute;
    z-index: 1;
    width: 90%;
    height: 60px;
    vertical-align: middle;
    right: -1px;
    text-align: center
}

.department:first-child {
    margin-left: 0;
    clear: left
}

@media screen and (min-width:768px) {
    .department:last-child:before {
        border: none
    }
}

@media screen and (max-width:767px) {
    .department {
        float: none;
        width: 100%;
        margin-left: 0
    }

    .department::before {
        content: "";
        display: block;
        position: absolute;
        width: 0;
        height: 60px;
        border-left: 2px dashed #fff;
        z-index: 1;
        top: -60px;
        left: 0;
        margin-left: -4px
    }

    .department:nth-child(2)::before {
        display: none
    }
}

.hori-sitemap ul {
    padding: 0;
    padding-top: 10px;
    text-align: center
}

.hori-sitemap ul li {
    position: relative
}

@media (max-width:575.98px) {
    .hori-sitemap ul {
        text-align: left
    }

    .hori-sitemap ul .parent-title a {
        padding-left: 0
    }

    .hori-sitemap ul .parent-title a:after {
        display: none
    }

    .hori-sitemap ul .parent-title:before {
        display: none
    }
}

@media (max-width:575.98px) {
    .hori-sitemap>ul {
        position: relative
    }

    .hori-sitemap>ul li {
        padding-top: 10px
    }

    .hori-sitemap>ul li .second-list,
    .hori-sitemap>ul li .sub-list {
        position: relative
    }

    .hori-sitemap>ul li .second-list:before,
    .hori-sitemap>ul li .sub-list:before {
        content: "";
        height: calc(100% - 14px);
        border-right: 2px dashed var(--vz-border-color);
        position: absolute;
        top: 0;
        left: 0
    }

    .hori-sitemap>ul li .sub-list:before {
        height: 38%
    }

    .hori-sitemap>ul li a {
        position: relative;
        padding: 4px 16px 4px 36px
    }

    .hori-sitemap>ul li a:after {
        content: "";
        width: 24px;
        border-top: 2px dashed var(--vz-border-color);
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        left: 0
    }

    .hori-sitemap>ul li ul {
        margin-left: 36px
    }
}

@media (min-width:576px) {
    .hori-sitemap ul {
        padding-top: 20px
    }

    .hori-sitemap ul li {
        padding-top: 30px
    }

    .hori-sitemap ul li:before {
        content: "";
        height: 24px;
        width: 0;
        border-right: 2px dashed var(--vz-border-color);
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        margin: 0 auto
    }

    .hori-sitemap ul li:after {
        content: "";
        width: 100%;
        border-top: 2px dashed var(--vz-border-color);
        position: absolute;
        top: 0;
        left: 50%
    }

    .hori-sitemap ul li:last-of-type:after {
        display: none
    }

    .hori-sitemap ul li.parent-title::before {
        content: "";
        height: 23px;
        border-right: 2px dashed var(--vz-border-color);
        position: absolute;
        top: 28px;
        left: 0
    }

    .hori-sitemap ul li.parent-title::after {
        border: none
    }

    .hori-sitemap ul li .sub-title {
        position: relative
    }

    .hori-sitemap ul li .sub-title::before {
        content: "";
        height: 21px;
        border-right: 2px dashed var(--vz-border-color);
        position: absolute;
        top: 27px;
        left: 49%;
        margin: 0 auto
    }
}

.hori-sitemap a {
    color: var(--vz-body-color);
    padding: 4px 0;
    display: block
}

.verti-sitemap a {
    color: var(--vz-body-color);
    display: block
}

.verti-sitemap .parent-title a {
    padding-left: 0
}

.verti-sitemap .parent-title a:before {
    display: none
}

.verti-sitemap .parent-title:before {
    display: none
}

.verti-sitemap .first-list {
    position: relative;
    padding-top: 10px
}

.verti-sitemap .first-list:before {
    content: "";
    border-left: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 0;
    height: 100%;
    bottom: 0;
    left: 0
}

.verti-sitemap .first-list .list-wrap a,
.verti-sitemap .first-list li a {
    position: relative;
    padding: 10px 16px 4px 36px
}

.verti-sitemap .first-list .list-wrap a::before,
.verti-sitemap .first-list li a::before {
    content: "";
    width: 24px;
    border-top: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    left: 0
}

.verti-sitemap .first-list .second-list,
.verti-sitemap .first-list .third-list {
    margin-left: 42px
}

.verti-sitemap .first-list .second-list,
.verti-sitemap .first-list .third-list {
    position: relative
}

.verti-sitemap .first-list .second-list li,
.verti-sitemap .first-list .third-list li {
    position: relative
}

.verti-sitemap .first-list .second-list li:before,
.verti-sitemap .first-list .third-list li:before {
    content: "";
    height: 100%;
    border-left: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 0;
    left: 0;
    margin: 0 auto
}

.verti-sitemap .first-list .second-list li:last-child::before,
.verti-sitemap .first-list .third-list li:last-child::before {
    height: 13px
}

.verti-sitemap .first-list:last-child::before {
    height: 25px
}

.bookmark-icon svg {
    fill: transparent
}

.bookmark-icon .btn-star {
    cursor: pointer
}

.bookmark-icon .bookmark-input:checked~.btn-star svg {
    fill: #ffbe0b !important;
    color: #ffbe0b !important
}

.bookmark-hide {
    display: none;
    visibility: hidden;
    height: 0
}

.profile-offcanvas .team-cover,
.team-box .team-cover {
    display: none;
    position: relative;
    margin-bottom: -140px
}

.profile-offcanvas .team-cover img,
.team-box .team-cover img {
    height: 140px;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover
}

.profile-offcanvas .team-cover::before,
.team-box .team-cover::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: -webkit-gradient(linear, left bottom, left top, from(#221a52), to(#4b38b3));
    background: linear-gradient(to top, #221a52, #4b38b3);
    opacity: .6
}

.team-list.grid-view-filter {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap
}

.team-list.grid-view-filter .col {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%
}

.team-list.grid-view-filter .team-box {
    overflow: hidden
}

.team-list.grid-view-filter .team-box .team-row {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start
}

.team-list.grid-view-filter .team-box .team-row .col {
    width: 100%
}

.team-list.grid-view-filter .team-box .team-cover {
    display: block
}

.team-list.grid-view-filter .team-box .team-settings .col {
    width: 50% !important;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto
}

.team-list.grid-view-filter .team-box .team-settings .btn-star {
    color: #fff
}

.team-list.grid-view-filter .team-box .team-settings .dropdown>a {
    color: #fff
}

.team-list.grid-view-filter .team-box .team-profile-img {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 25px;
    margin-top: 36px
}

.team-list.grid-view-filter .team-box .team-profile-img .avatar-lg {
    font-size: 22px
}

.team-list.grid-view-filter .team-box .team-profile-img .team-content {
    margin-left: 0;
    margin-top: 25px;
    text-align: center
}

.team-list.grid-view-filter .team-box .view-btn {
    width: 100%;
    margin-top: 25px
}

@media (min-width:1200px) and (max-width:1399.98px) {
    .team-list.grid-view-filter .col {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 33.33%
    }
}

@media (max-width:1199.98px) {
    .team-list.grid-view-filter .col {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 50%
    }
}

@media (max-width:767.98px) {
    .team-list.grid-view-filter .col {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }
}

.team-list.list-view-filter {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.team-list.list-view-filter .team-box {
    margin-bottom: 10px
}

.team-list.list-view-filter .team-box .team-row {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.team-list.list-view-filter .team-box .team-profile-img {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.team-list.list-view-filter .team-box .team-profile-img .avatar-lg {
    height: 4rem;
    width: 4rem;
    font-size: 16px
}

.team-list.list-view-filter .team-box .team-profile-img .team-content {
    margin-left: 15px
}

.team-list.list-view-filter .team-box .team-settings {
    width: auto;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
}

.team-list.list-view-filter .team-box .btn-star {
    color: #4b38b3
}

@media (max-width:767.98px) {
    .team-list.list-view-filter {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .team-list.list-view-filter .col {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: 100%
    }

    .team-list.list-view-filter .team-box .team-settings {
        width: 100%;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1;
        margin-bottom: 10px
    }

    .team-list.list-view-filter .team-box .team-settings .col {
        width: 50%
    }

    .team-list.list-view-filter .team-box .team-profile-img {
        margin-bottom: 25px
    }

    .team-list.list-view-filter .team-box .view-btn {
        width: 100%;
        margin-top: 25px
    }
}

.list-grid-nav .nav-link.active {
    background-color: #299cdb;
    color: #fff
}

.profile-offcanvas .team-cover {
    margin-bottom: -132px;
    display: block;
    z-index: -1
}

.profile-offcanvas .btn-star {
    color: #fff
}

.profile-offcanvas .dropdown>a {
    color: #fff
}

.countdownlist {
    text-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 24px
}

.countdownlist .countdownlist-item {
    width: 25%
}

.countdownlist .countdownlist-item:last-of-type .count-num::after {
    display: none
}

.countdownlist .count-title {
    font-size: 13px;
    font-weight: 500;
    display: block;
    margin-bottom: 8px;
    color: rgba(var(--vz-dark-rgb), .5);
    text-transform: uppercase
}

.countdownlist .count-num {
    background-color: var(--vz-card-bg);
    padding: 16px 8px;
    position: relative;
    border-radius: .25rem;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow);
    font-weight: 600;
    font-size: 32px
}

@media (max-width:575.98px) {
    .countdownlist .count-num {
        font-size: 18px
    }
}

.countdownlist .count-num::after {
    content: ":";
    font-size: 20px;
    position: absolute;
    right: -16px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    color: var(--vz-dark)
}

.move-animation {
    -webkit-animation: mover 1s infinite alternate;
    animation: mover 1s infinite alternate
}

@-webkit-keyframes mover {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        -webkit-transform: translateY(-16px);
        transform: translateY(-16px)
    }
}

@keyframes mover {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        -webkit-transform: translateY(-16px);
        transform: translateY(-16px)
    }
}

.coming-soon-text {
    font-weight: 600;
    text-transform: uppercase;
    color: #fff;
    text-shadow: 3px 4px #45cb85
}

.countdown-input-group {
    max-width: 400px
}

.search-more-results {
    position: relative;
    overflow: hidden
}

.search-more-results .nav-icon {
    font-size: 14px;
    color: #fff;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    text-align: center;
    left: 0;
    right: 0
}

.search-more-results .nav-icon i {
    font-size: 20px
}

@media (max-width:767.98px) {
    .search-more-results .nav-icon {
        font-size: 14px
    }

    .search-more-results .nav-icon i {
        font-size: 18px
    }
}

.video-list .list-element {
    display: none
}

.video-list .list-element:nth-child(1) {
    display: block
}

.video-list .list-element:nth-child(2) {
    display: block
}

.video-list .list-element:nth-child(3) {
    display: block
}

.search-voice {
    height: 120px;
    width: 120px;
    line-height: 120px;
    margin: 0 auto;
    text-align: center;
    border-radius: 50%;
    z-index: 1;
    position: relative
}

.search-voice i {
    line-height: 56px;
    font-size: 30px
}

.search-voice .voice-wave {
    position: absolute;
    width: 120px;
    height: 120px;
    z-index: -1;
    left: 0;
    right: 0;
    margin: 0 auto;
    opacity: 0;
    border-radius: 100px;
    -webkit-animation: voice-wave 1.8s infinite;
    animation: voice-wave 1.8s infinite;
    background-color: var(--vz-light)
}

.search-voice .voice-wave:nth-child(2) {
    -webkit-animation-delay: .3s;
    animation-delay: .3s
}

.search-voice .voice-wave:nth-child(3) {
    -webkit-animation-delay: .6s;
    animation-delay: .6s
}

@-webkit-keyframes voice-wave {
    0% {
        opacity: 1;
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    100% {
        opacity: 0;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes voice-wave {
    0% {
        opacity: 1;
        -webkit-transform: scale(0);
        transform: scale(0)
    }

    100% {
        opacity: 0;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.images-menu .swiper-slide {
    width: auto;
    display: inline-block
}

#selection-element {
    display: none
}

.filter-choices-input .choices__inner {
    padding: 0;
    border: none;
    background-color: var(--vz-card-bg)
}

.filter-choices-input .choices__input {
    background-color: var(--vz-card-bg)
}

@media (min-width:992px) {
    .sticky-side-div {
        position: sticky;
        top: calc(70px + 1.5rem)
    }
}

.product-img-slider .product-nav-slider .nav-slide-item {
    border: 1px solid var(--vz-border-color);
    border-radius: .25rem;
    padding: .5rem;
    cursor: pointer
}

.product-img-slider .product-nav-slider .swiper-slide-thumb-active .nav-slide-item {
    background-color: var(--vz-light)
}

.filter-list a.active .listname {
    color: #45cb85
}

.invoice-table tbody:last-child {
    border: none
}

.currency-select .choices__inner {
    padding: 0;
    padding-right: 15px;
    min-height: 0
}

.currency-select .choices__list--single {
    padding: 0 16px 0 4px
}

.currency-select .choices[data-type*=select-one] {
    bottom: 0
}

.currency-select .choices[data-type*=select-one] :after {
    top: 4px
}

.currency-select .choices[data-type*=select-one] .choices__inner {
    padding-bottom: 0
}

.chat-wrapper {
    position: relative;
    overflow-x: hidden
}

.chat-leftsidebar {
    height: calc(100vh - 137px);
    position: relative;
    background-color: var(--vz-card-bg)
}

@media (min-width:992px) {
    .chat-leftsidebar {
        min-width: 300px;
        max-width: 300px;
        height: calc(100vh - 70px - 60px - 8px)
    }
}

.chat-list {
    margin: 0
}

.chat-list>li.active a {
    background-color: var(--vz-primary);
    color: var(--vz-white)
}

.chat-list>li.active a .badge {
    background-color: rgba(var(--vz-success-rgb), .15) !important;
    color: #45cb85 !important
}

.chat-list>li a {
    display: block;
    padding: 7px 24px;
    color: var(--vz-body-color);
    -webkit-transition: all .4s;
    transition: all .4s;
    font-family: Inter, sans-serif;
    font-weight: 500;
    font-size: .8125rem
}

.chat-list>li .chat-user-message {
    font-size: 14px
}

.chat-list>li .unread-msg-user {
    font-weight: 600
}

.chat-list>li .unread-message {
    position: absolute;
    display: inline-block;
    right: 24px;
    left: auto;
    top: 33px
}

.chat-list>li .unread-message .badge {
    line-height: 16px;
    font-weight: 600;
    font-size: 10px
}

.chat-user-img {
    position: relative
}

.chat-user-img .user-status {
    width: 10px;
    height: 10px;
    background-color: #adb5bd;
    border-radius: 50%;
    border: 2px solid var(--vz-card-bg);
    position: absolute;
    right: 0;
    left: auto;
    bottom: 0
}

.chat-user-img.online .user-status {
    background-color: #45cb85
}

.chat-user-img.away .user-status {
    background-color: #ffbe0b
}

.chat-room-list {
    max-height: calc(100vh - 268px)
}

@media (max-width:991.98px) {
    .chat-room-list {
        height: calc(100vh - 268px)
    }
}

.user-chat {
    /* background: url(../images/chat-bg-pattern.png); */
    -webkit-transition: all .4s;
    transition: all .4s;
    position: relative;
    background-color: var(--vz-body-bg)
}

@media (max-width:991.98px) {
    .user-chat {
        position: absolute;
        left: 0;
        top: 3px;
        width: 100%;
        height: calc(100% - 3px);
        visibility: hidden;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        z-index: 99;
        padding-top: 70px
    }

    .user-chat.user-chat-show {
        visibility: visible;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

.user-chat .chat-content {
    position: relative
}

.user-chat.user-chat-show .chat-welcome-section {
    display: none
}

@media (min-width:992px) {
    .user-chat.user-chat-show .chat-content {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
}

.user-chat-topbar {
    border-bottom: 1px solid transparent;
    background-color: var(--vz-card-bg)
}

@media (max-width:991.98px) {
    .user-chat-topbar {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        z-index: 1
    }
}

.user-chat-nav .nav-btn {
    height: 40px;
    width: 40px;
    line-height: 40px;
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0;
    font-size: 20px;
    color: #343a40
}

@media (max-width:575.98px) {
    .user-chat-nav {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: end;
        -ms-flex-pack: end;
        justify-content: flex-end
    }
}

.chat-conversation {
    height: calc(100vh - 299px)
}

@media (max-width:991.98px) {
    .chat-conversation {
        height: calc(100vh - 275px)
    }
}

.chat-conversation .simplebar-content-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.chat-conversation .simplebar-content-wrapper .simplebar-content {
    margin-top: auto
}

.chat-conversation .chat-conversation-list {
    padding-top: 10px;
    margin-bottom: 0
}

.chat-conversation .chat-conversation-list>li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.chat-conversation li:last-of-type .conversation-list {
    margin-bottom: 0
}

.chat-conversation .chat-list.left .check-message-icon {
    display: none
}

.chat-conversation .chat-list .message-box-drop {
    visibility: hidden
}

.chat-conversation .chat-list:hover .message-box-drop {
    visibility: visible
}

.chat-conversation .chat-avatar {
    margin: 0 16px 0 0
}

.chat-conversation .chat-avatar img {
    width: 28px;
    height: 28px;
    border-radius: 50%
}

.chat-conversation .chat-day-title {
    position: relative;
    text-align: center;
    margin-bottom: 24px;
    margin-top: 12px;
    width: 100%
}

.chat-conversation .chat-day-title .title {
    background-color: #fff;
    position: relative;
    font-size: 13px;
    z-index: 1;
    padding: 6px 12px;
    border-radius: 5px
}

.chat-conversation .chat-day-title:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 1px;
    left: 0;
    right: 0;
    background-color: rgba(75, 56, 179, .2);
    top: 10px
}

.chat-conversation .chat-day-title .badge {
    font-size: 12px
}

.chat-conversation .conversation-list {
    margin-bottom: 24px;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    position: relative;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
    max-width: 80%
}

@media (max-width:575.98px) {
    .chat-conversation .conversation-list {
        max-width: 90%
    }
}

.chat-conversation .conversation-list .ctext-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 10px
}

.chat-conversation .conversation-list .ctext-content {
    word-wrap: break-word;
    word-break: break-word
}

.chat-conversation .conversation-list .ctext-wrap-content {
    padding: 12px 20px;
    background-color: var(--vz-light);
    position: relative;
    border-radius: 3px;
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12)
}

@media (max-width:575.98px) {
    .chat-conversation .conversation-list .ctext-wrap-content .attached-file .attached-file-avatar {
        display: none
    }

    .chat-conversation .conversation-list .ctext-wrap-content .attached-file .dropdown .dropdown-toggle {
        display: block
    }
}

.chat-conversation .conversation-list .conversation-name {
    font-weight: 500;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 8px
}

.chat-conversation .conversation-list .dropdown .dropdown-toggle {
    font-size: 18px;
    padding: 4px;
    color: #878a99
}

.chat-conversation .conversation-list .dropdown .dropdown-toggle::after {
    display: none
}

@media (max-width:575.98px) {
    .chat-conversation .conversation-list .dropdown .dropdown-toggle {
        display: none
    }
}

.chat-conversation .conversation-list .chat-time {
    font-size: 12px;
    margin-top: 4px;
    text-align: right
}

.chat-conversation .conversation-list .message-img {
    border-radius: .2rem;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    gap: 8px
}

.chat-conversation .conversation-list .message-img .message-img-list {
    position: relative
}

.chat-conversation .conversation-list .message-img img {
    max-width: 150px
}

.chat-conversation .conversation-list .message-img .message-img-link {
    position: absolute;
    right: 10px;
    left: auto;
    bottom: 10px
}

.chat-conversation .conversation-list .message-img .message-img-link li>a {
    font-size: 18px;
    color: #fff;
    display: inline-block;
    line-height: 20px;
    width: 26px;
    height: 24px;
    border-radius: 3px;
    background-color: rgba(33, 37, 41, .7);
    text-align: center
}

.chat-conversation .right {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.chat-conversation .right .chat-avatar {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
    margin-right: 0;
    margin-left: 16px
}

.chat-conversation .right .chat-time {
    text-align: left;
    color: #878a99
}

.chat-conversation .right .conversation-list {
    text-align: right
}

.chat-conversation .right .conversation-list .ctext-wrap {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
    background-color: var(--vz-primary);
    color: var(--vz-white);
    text-align: right;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow)
}

.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block {
    background-color: rgba(255, 255, 255, .5);
    border-color: rgba(var(--vz-success-rgb), 1);
    color: #212529
}

.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block .conversation-name {
    color: rgba(var(--vz-success-rgb), 1)
}

.chat-conversation .right .conversation-list .conversation-name {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.chat-conversation .right .conversation-list .conversation-name .check-message-icon {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.chat-conversation .right .conversation-list .conversation-name .time {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
}

.chat-conversation .right .conversation-list .conversation-name .name {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
}

.chat-conversation .right .conversation-list .dropdown {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.chat-conversation .right .dot {
    background-color: #212529
}

.chat-input-section {
    border-top: 1px solid transparent;
    background-color: var(--vz-card-bg);
    position: relative;
    z-index: 1
}

.chat-input-section .chat-input-feedback {
    display: none;
    position: absolute;
    top: -20px;
    left: 4px;
    font-size: 12px;
    color: #f06548
}

.chat-input-section .show {
    display: block
}

.chat-input-links {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.chat-input-links .links-list-item>.btn {
    -webkit-box-shadow: none;
    box-shadow: none;
    padding: 0;
    font-size: 20px;
    width: 37.5px;
    height: 37.5px
}

.chat-input-links .links-list-item>.btn.btn-link {
    color: #878a99
}

.copyclipboard-alert {
    position: absolute;
    bottom: 0;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.replyCard {
    position: absolute;
    left: 0;
    right: 0;
    border-top: 1px solid var(--vz-border-color);
    overflow: hidden;
    opacity: 0;
    bottom: 0;
    border-radius: 0;
    -webkit-transition: all .4s;
    transition: all .4s
}

@media (max-width:991.98px) {
    .replyCard {
        bottom: -12px
    }
}

.replyCard.show {
    -webkit-transform: translateY(-88px);
    transform: translateY(-88px);
    opacity: 1
}

@media (max-width:991.98px) {
    .replyCard.show {
        -webkit-transform: translateY(-83px);
        transform: translateY(-83px)
    }
}

.replymessage-block {
    padding: 12px 20px;
    margin-bottom: 8px;
    text-align: left;
    border-radius: 4px;
    background-color: rgba(var(--vz-success-rgb), .1);
    border-left: 2px solid rgba(var(--vz-success-rgb), 1)
}

.replymessage-block .conversation-name {
    color: rgba(var(--vz-success-rgb), 1);
    font-size: 14px
}

.chat-sm .ctext-wrap-content {
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.chat-sm .message-img img {
    max-width: 90px !important
}

.chat-sm .message-img-link {
    bottom: 0 !important;
    right: 5px !important
}

@media (min-width:1025px) {
    [data-layout=horizontal] .chat-wrapper {
        margin-left: 0 !important;
        margin-right: 0 !important
    }
}

@media (max-width:991.98px) {
    [data-layout=horizontal] .chat-wrapper {
        margin-top: 0 !important
    }
}

[data-layout=horizontal] .chat-leftsidebar {
    height: calc(100vh - 70px - 60px - 54px)
}

@media (max-width:991.98px) {
    [data-layout=horizontal] .chat-leftsidebar {
        height: calc(100vh - 70px - 60px - 8px)
    }
}

[data-layout=horizontal] .chat-room-list {
    height: calc(100vh - 316px)
}

@media (max-width:991.98px) {
    [data-layout=horizontal] .chat-room-list {
        height: calc(100vh - 265px)
    }
}

[data-layout=horizontal] .chat-conversation {
    height: calc(100vh - 343px)
}

@media (max-width:991.98px) {
    [data-layout=horizontal] .chat-conversation {
        height: calc(100vh - 275px)
    }
}

.email-wrapper {
    position: relative;
    overflow-x: hidden
}

.email-menu-sidebar {
    height: calc(100vh - 137px);
    position: relative;
    background-color: var(--vz-card-bg);
    -webkit-transition: all .2s;
    transition: all .2s
}

@media (min-width:992px) {
    .email-menu-sidebar {
        min-width: 250px;
        max-width: 250px;
        height: calc(100vh - 70px - 60px - 8px)
    }
}

@media (max-width:991.98px) {
    .email-menu-sidebar {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        width: 200px;
        max-width: 100%;
        z-index: 1003;
        -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
        box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
        visibility: hidden;
        height: 100vh
    }

    .email-menu-sidebar.menubar-show {
        visibility: visible;
        -webkit-transform: none;
        transform: none
    }
}

.email-menu-sidebar .email-menu-sidebar-scroll {
    height: calc(100vh - 295px)
}

@media (max-width:991.98px) {
    .email-menu-sidebar .email-menu-sidebar-scroll {
        height: calc(100vh - 150px)
    }
}

.mail-list a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #878a99;
    padding: 5px 0;
    font-weight: 500
}

.mail-list a:hover {
    color: #4b38b3
}

.mail-list a i {
    font-size: 14px
}

.mail-list a.active {
    color: #45cb85;
    font-weight: 600
}

.mail-list a.active i {
    color: #45cb85
}

.email-topbar-link .btn-ghost-secondary {
    color: #878a99
}

.email-topbar-link .btn-ghost-secondary:hover {
    color: #3577f1
}

.email-content {
    width: 100%;
    background-color: var(--vz-card-bg);
    -webkit-transition: all .2s;
    transition: all .2s
}

.unreadConversations-alert {
    position: fixed;
    bottom: 60px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    z-index: 1
}

.email-detail-content {
    position: absolute;
    top: 4px;
    bottom: 4px;
    left: 68%;
    width: 32%;
    background-color: var(--vz-card-bg);
    -webkit-transform: translateX(200%);
    transform: translateX(200%);
    -webkit-transition: all .2s;
    transition: all .2s;
    visibility: hidden;
    border-left: 2px solid var(--vz-body-bg)
}

.email-detail-show .email-detail-content {
    -webkit-transform: none;
    transform: none;
    visibility: visible
}

.email-detail-show .email-content {
    margin-right: 32%
}

@media (max-width:1349.98px) {
    .email-detail-show .email-content {
        margin-right: 0
    }
}

.email-detail-content-scroll {
    height: calc(100vh - 390px)
}

@media (max-width:1349.98px) {
    .email-detail-content-scroll {
        height: calc(100vh - 252px)
    }
}

.message-list-content {
    height: calc(100vh - 242px)
}

@media (max-width:575.98px) {
    .message-list-content {
        height: calc(100vh - 230px)
    }
}

.message-list {
    display: block;
    padding-left: 0;
    margin: 0
}

.message-list li {
    position: relative;
    display: block;
    height: 50px;
    line-height: 50px;
    cursor: default;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    clear: both
}

.message-list li a {
    color: var(--vz-gray-700)
}

.message-list li:hover {
    background: var(--vz-light);
    -webkit-transition-duration: 50ms;
    transition-duration: 50ms
}

.message-list li .col-mail {
    float: left;
    position: relative
}

.message-list li .col-mail-1 {
    width: 280px
}

.message-list li .col-mail-1 .checkbox-wrapper-mail,
.message-list li .col-mail-1 .dot,
.message-list li .col-mail-1 .star-toggle {
    display: block;
    float: left
}

.message-list li .col-mail-1 .dot {
    border: 4px solid transparent;
    border-radius: 100px;
    margin: 22px 26px 0;
    height: 0;
    width: 0;
    line-height: 0;
    font-size: 0
}

.message-list li .col-mail-1 .checkbox-wrapper-mail {
    margin: 15px 0 0 20px;
    line-height: normal
}

.message-list li .col-mail-1 .star-toggle {
    margin-top: 18px;
    margin-left: 5px
}

.message-list li .col-mail-1 .title {
    position: absolute;
    top: 0;
    left: 95px;
    right: 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin-bottom: 0
}

@media (max-width:575.98px) {
    .message-list li .col-mail-1 .title {
        left: 95px
    }
}

.message-list li .col-mail-2 {
    position: absolute;
    top: 0;
    left: 280px;
    right: 0;
    bottom: 0
}

.message-list li .col-mail-2 .date,
.message-list li .col-mail-2 .subject {
    position: absolute;
    top: 0
}

.message-list li .col-mail-2 .subject {
    left: 0;
    right: 110px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap
}

.message-list li .col-mail-2 .subject .teaser {
    color: var(--vz-gray-600)
}

.message-list li .col-mail-2 .date {
    right: 0;
    width: 100px;
    padding-left: 20px
}

.message-list li.active,
.message-list li.active:hover {
    -webkit-box-shadow: inset 3px 0 0 #4b38b3;
    box-shadow: inset 3px 0 0 #4b38b3;
    background-color: var(--vz-light)
}

.message-list li.unread {
    color: var(--vz-gray-800)
}

.message-list li.unread a {
    color: var(--vz-gray-800);
    font-weight: 600
}

#email-topbar-actions,
#unreadConversations {
    display: none
}

#mailLoader {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0
}

.email-compose-input {
    padding-right: 80px
}

@media (max-width:1349.98px) {
    .email-detail-content {
        position: fixed;
        top: 0;
        bottom: 0;
        left: auto;
        right: 0;
        width: 400px;
        max-width: 100%;
        z-index: 1003;
        -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
        box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }

    .email-detail-show .email-detail-content {
        -webkit-transform: none;
        transform: none
    }
}

@media (max-width:575.98px) {
    .message-list li .col-mail-1 {
        width: 180px
    }
}

.email-editor .ck-editor__editable_inline {
    min-height: 200px !important
}

.email-chat-detail {
    width: 350px;
    position: fixed;
    max-width: 100%;
    bottom: 60px;
    right: 60px;
    z-index: 9;
    display: none
}

.email-chat-detail .card {
    -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
    box-shadow: 0 5px 10px rgba(30, 32, 37, .12)
}

@media (max-width:515.98px) {
    .email-chat-detail {
        left: 16px;
        right: 16px
    }
}

@media (min-width:1025px) {
    [data-layout=horizontal] .email-wrapper {
        margin-left: 0 !important;
        margin-right: 0 !important
    }
}

@media (max-width:991.98px) {
    [data-layout=horizontal] .email-wrapper {
        margin-top: 0 !important
    }
}

[data-layout=horizontal] .email-menu-sidebar {
    height: calc(100vh - 70px - 60px - 54px)
}

@media (max-width:991.98px) {
    [data-layout=horizontal] .email-menu-sidebar {
        height: 100vh
    }
}

[data-layout=horizontal] .email-menu-sidebar .email-menu-sidebar-scroll {
    height: calc(100vh - 330px)
}

@media (max-width:991.98px) {
    [data-layout=horizontal] .email-menu-sidebar .email-menu-sidebar-scroll {
        height: calc(100vh - 150px)
    }
}

[data-layout=horizontal] .message-list-content {
    height: calc(100vh - 289px)
}

[data-layout=horizontal] .email-detail-content-scroll {
    height: calc(100vh - 435px)
}

@media (max-width:1349.98px) {
    [data-layout=horizontal] .email-detail-content-scroll {
        height: calc(100vh - 252px)
    }
}

.tasks-board {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    overflow-x: auto;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
}

.tasks-board .tasks-list {
    min-width: 300px;
    margin-right: 24px
}

.tasks-board::-webkit-scrollbar {
    -webkit-appearance: none
}

.tasks-board::-webkit-scrollbar:vertical {
    width: 10px
}

.tasks-board::-webkit-scrollbar:horizontal {
    height: 8px
}

.tasks-board::-webkit-scrollbar-thumb {
    background-color: rgba(var(--vz-dark-rgb), .075);
    border-radius: 8px
}

.tasks-board::-webkit-scrollbar-track {
    border-radius: 8px
}

.tasks-box .progress {
    border-radius: 0 0 .25rem .25rem
}

.tasks-box .tasks-img {
    height: 135px;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: center;
    object-position: center;
    margin: 12px 0
}

.tasks-box:last-child {
    margin-bottom: 0
}

.tasks-wrapper {
    max-height: calc(100vh - 418px)
}

.tasks {
    min-height: 180px;
    position: relative
}

.tasks.noTask::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: 180px;
    width: 270px;
    margin: 0 auto;
    /* background-image: url(../images/file.png); */
    background-size: cover;
    background-position: center
}

#tasksList tr .tasks-list-menu {
    opacity: 0
}

#tasksList tr:hover .tasks-list-menu {
    opacity: 1
}

.layout-wrapper.landing {
    background-color: var(--vz-card-bg);
    font-size: 15px
}

.section {
    padding: 90px 0;
    position: relative
}

@media (max-width:767.98px) {
    .section {
        padding: 50px 0
    }
}

.icon-effect {
    position: relative
}

.icon-effect::before {
    content: "";
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: rgba(69, 203, 133, .2);
    border-radius: 50%;
    bottom: 0;
    left: 0
}

.navbar-landing {
    padding: 10px 0;
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

@media (max-width:991.98px) {
    .navbar-landing {
        background-color: var(--vz-card-bg);
        -webkit-box-shadow: 0 1px 16px -2px rgba(56, 65, 74, .15);
        box-shadow: 0 1px 16px -2px rgba(56, 65, 74, .15);
        padding: 10px 8px
    }
}

.navbar-landing .navbar-nav .nav-item .nav-link {
    font-size: 16px;
    font-weight: 500;
    -webkit-transition: all .4s;
    transition: all .4s;
    font-family: Inter, sans-serif;
    color: var(--vz-dark);
    padding: 14px
}

@media (max-width:991.98px) {
    .navbar-landing .navbar-nav .nav-item .nav-link {
        padding: 8px 0
    }
}

.navbar-landing .navbar-nav .nav-item .nav-link.active,
.navbar-landing .navbar-nav .nav-item .nav-link:focus,
.navbar-landing .navbar-nav .nav-item .nav-link:hover {
    color: #45cb85 !important
}

.navbar-landing.is-sticky {
    background-color: var(--vz-card-bg);
    -webkit-box-shadow: 0 1px 16px -2px rgba(56, 65, 74, .15);
    box-shadow: 0 1px 16px -2px rgba(56, 65, 74, .15)
}

.navbar-light .navbar-brand .card-logo-dark {
    display: none
}

.navbar-light .navbar-brand .card-logo-light {
    display: block
}

.navbar-light .navbar-nav .nav-item .nav-link {
    color: rgba(243, 246, 249, .75)
}

.navbar-light.is-sticky .navbar-nav .nav-item .nav-link {
    color: var(--vz-dark)
}

.navbar-light.is-sticky .navbar-brand .card-logo-dark {
    display: block
}

.navbar-light.is-sticky .navbar-brand .card-logo-light {
    display: none
}

.hero-section {
    background-color: rgba(var(--vz-light-rgb), .5)
}

.hero-section .hero-shape-svg svg path {
    fill: var(--vz-card-bg)
}

.bg-overlay-pattern {
    /* background-image: url(../images/landing/bg-pattern.png); */
    background-color: transparent;
    background-position: center;
    background-size: cover;
    opacity: .2
}

.demo-carousel {
    position: relative;
    z-index: 1
}

.demo-carousel .carousel-item .demo-item {
    background-color: var(--vz-card-bg);
    padding: 8px;
    border-radius: 7px
}

.demo-carousel .demo-img-patten-top {
    position: absolute;
    right: -50px;
    top: -16px;
    max-width: 230px
}

.demo-carousel .demo-img-patten-bottom {
    position: absolute;
    left: -70px;
    bottom: -50px;
    max-width: 230px
}

.client-images img {
    max-height: 45px;
    width: auto;
    margin: 12px auto;
    -webkit-transition: all .4s;
    transition: all .4s
}

.plan-box {
    max-width: 356px;
    margin-left: auto;
    margin-right: auto
}

.process-card {
    position: relative
}

.process-card .process-arrow-img {
    position: absolute;
    left: 75%;
    top: 7%;
    width: 50%;
    opacity: .1
}

.custom-footer {
    color: #9ba7b3
}

.footer-list li a {
    color: #9ba7b3;
    padding: 7px 0;
    display: block;
    -webkit-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out
}

.footer-list li a:hover {
    color: rgba(255, 255, 255, .9)
}

.footer-social-link .avatar-title {
    color: #778089;
    background-color: rgba(255, 255, 255, .05);
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}

.footer-social-link .avatar-title:hover {
    color: #fff;
    background-color: #4b38b3
}

[data-layout-mode=dark] .layout-wrapper.landing .demo-img-patten-bottom,
[data-layout-mode=dark] .layout-wrapper.landing .demo-img-patten-top {
    opacity: .2
}

[data-layout-mode=dark] .layout-wrapper.landing footer.bg-dark {
    background-color: var(--vz-card-bg) !important
}

.bookmark-icon .btn {
    color: #878a99;
    background-color: #f3f6f9;
    -webkit-box-shadow: var(--vz-box-shadow);
    box-shadow: var(--vz-box-shadow);
    font-size: .8125rem
}

.bookmark-icon .btn.active,
.bookmark-icon .btn:hover {
    color: #f06548
}

.explore-box {
    border-radius: 9px;
    overflow: hidden
}

.explore-box .explore-img {
    height: 280px;
    -o-object-fit: cover;
    object-fit: cover
}

.explore-box .explore-place-bid-img {
    position: relative;
    overflow: hidden;
    z-index: 0
}

.explore-box .explore-place-bid-img .bg-overlay {
    position: absolute;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(33, 37, 41, .4);
    opacity: 0;
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

.explore-box .explore-place-bid-img .place-bid-btn {
    top: 50%;
    position: absolute;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.explore-box .explore-place-bid-img .place-bid-btn .btn {
    opacity: 0;
    bottom: -25px;
    -webkit-transition: .5s ease;
    transition: .5s ease
}

.explore-box:hover .explore-place-bid-img .place-bid-btn .btn {
    opacity: 1;
    bottom: 0
}

.explore-box:hover .explore-place-bid-img .bg-overlay {
    opacity: 1
}

.explore-box .discount-time {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    background-color: rgba(255, 255, 255, .4);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    padding: 8px
}

.nft-hero {
    /* background-image: url(../images/nft/bg-home.jpg); */
    background-size: cover;
    background-position: bottom;
    padding: 222px 0 150px 0
}

.nft-hero .bg-overlay {
    background-color: #05175f;
    opacity: .85
}

#explorecard-list .list-element {
    display: none
}

#explorecard-list .list-element:nth-child(-n+10) {
    display: block
}

/*# sourceMappingURL=app.min.css.map */