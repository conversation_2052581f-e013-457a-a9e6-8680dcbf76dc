ID,Tên Test Case,<PERSON><PERSON> tả,Endpoint,Method,Input,Expected Output,<PERSON><PERSON><PERSON>u kiện tiên quyết
TK-TC-01,C<PERSON><PERSON> nhật tồn kho thành công,<PERSON><PERSON><PERSON> tra API cập nhật thông tin tồn kho,/tonkho/update,PUT,TonKhoDTO với thông tin hợp lệ và ID tồn tại,Status: 200; Msg: "Thành công."; Data: Thông tin tồn kho sau khi cập nhật,Tồn kho với ID đã tồn tại; Thuốc với thuocId tồn tại
TK-TC-02,Cập nhật tồn kho không tồn tại,Kiểm tra API cập nhật thông tin tồn kho không tồn tại,/tonkho/update,PUT,TonKhoDTO với ID không tồn tại,Status: 404; Msg: "<PERSON>ồ<PERSON> kho không tồn tại.",<PERSON><PERSON><PERSON><PERSON> có tồn kho với ID cần cập nhật
TK-TC-03,Cập nhật tồn kho với thuốc không tồn tại,Kiểm tra API cập nhật tồn kho với thuốc không tồn tại,/tonkho/update,PUT,TonKhoDTO với thuocId không tồn tại,Status: 500; Lỗi khi không tìm thấy thuốc,Thuốc với ID không tồn tại
TK-TC-04,Cập nhật tồn kho với số lượng âm,Kiểm tra API cập nhật tồn kho với số lượng âm,/tonkho/update,PUT,TonKhoDTO với soLuong < 0,Status: 400; Lỗi validation,Số lượng phải lớn hơn hoặc bằng 0
TK-TC-05,Cập nhật tồn kho với hạn sử dụng đã qua,Kiểm tra API cập nhật tồn kho với hạn sử dụng đã qua,/tonkho/update,PUT,TonKhoDTO với hanSuDung < ngày hiện tại,Status: 400; Lỗi validation,Hạn sử dụng phải lớn hơn hoặc bằng ngày hiện tại
TK-TC-06,Cập nhật tồn kho với số lô rỗng,Kiểm tra API cập nhật tồn kho với số lô rỗng,/tonkho/update,PUT,TonKhoDTO với soLo="",Status: 400; Lỗi validation,Số lô không được rỗng
TK-TC-07,Tìm kiếm tồn kho thành công,Kiểm tra API tìm kiếm tồn kho với phân trang,/tonkho/search,POST,SearchTonKhoDTO với currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách tồn kho,Có dữ liệu tồn kho trong database
TK-TC-08,Tìm kiếm tồn kho theo tên thuốc,Kiểm tra API tìm kiếm tồn kho theo tên thuốc,/tonkho/search,POST,SearchTonKhoDTO với tenThuoc="Paracetamol"; currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách tồn kho của thuốc có tên chứa "Paracetamol",Có tồn kho của thuốc có tên chứa "Paracetamol"
TK-TC-09,Tìm kiếm tồn kho theo số lô,Kiểm tra API tìm kiếm tồn kho theo số lô,/tonkho/search,POST,SearchTonKhoDTO với soLo="L001"; currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách tồn kho có số lô chứa "L001",Có tồn kho có số lô chứa "L001"
TK-TC-10,Tìm kiếm tồn kho theo tên nhà sản xuất,Kiểm tra API tìm kiếm tồn kho theo tên nhà sản xuất,/tonkho/search,POST,SearchTonKhoDTO với tenNhaSanXuat="ABC"; currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách tồn kho của thuốc có nhà sản xuất có tên chứa "ABC",Có tồn kho của thuốc có nhà sản xuất có tên chứa "ABC"
TK-TC-11,Tìm kiếm tồn kho với nhiều điều kiện,Kiểm tra API tìm kiếm tồn kho với nhiều điều kiện,/tonkho/search,POST,SearchTonKhoDTO với tenThuoc="Para"; soLo="L001"; tenNhaSanXuat="ABC"; currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách tồn kho thỏa mãn tất cả điều kiện,Có tồn kho thỏa mãn tất cả điều kiện tìm kiếm
TK-TC-12,Tìm kiếm tồn kho không có kết quả,Kiểm tra API tìm kiếm tồn kho không có kết quả,/tonkho/search,POST,SearchTonKhoDTO với tenThuoc="XYZ123"; currentPage=0; size=20,Status: 200; Msg: "Thanh công"; Data: PageDTO chứa danh sách rỗng,Không có tồn kho thỏa mãn điều kiện tìm kiếm
TK-TC-13,Tìm kiếm tồn kho với phân trang không hợp lệ,Kiểm tra API tìm kiếm tồn kho với phân trang không hợp lệ,/tonkho/search,POST,SearchTonKhoDTO với currentPage=-1; size=20,Status: 400; Lỗi validation,Tham số phân trang không hợp lệ
TK-TC-14,Tìm kiếm tồn kho với kích thước trang không hợp lệ,Kiểm tra API tìm kiếm tồn kho với kích thước trang không hợp lệ,/tonkho/search,POST,SearchTonKhoDTO với currentPage=0; size=0,Status: 400; Lỗi validation,Tham số kích thước trang không hợp lệ
TK-TC-15,Tìm kiếm tồn kho với sắp xếp theo trường không tồn tại,Kiểm tra API tìm kiếm tồn kho với sắp xếp theo trường không tồn tại,/tonkho/search,POST,SearchTonKhoDTO với sortedField="fieldNotExist",Status: 500; Lỗi khi sắp xếp theo trường không tồn tại,Trường sắp xếp không tồn tại
