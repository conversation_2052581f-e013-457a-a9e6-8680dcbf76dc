{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "HieuThuoc - NhaCungCap API Tests", "description": "Collection for testing NhaCungCapController APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All NhaCungCap", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/nhacungcap/list", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "list"]}, "description": "<PERSON><PERSON><PERSON> danh sách tất cả nhà cung cấp"}, "response": []}, {"name": "Search NhaCungCap By Name", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/nhacungcap/search_by_ten_nha_cung_cap?tenNhaCungCap=ABC", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "search_by_ten_nha_cung_cap"], "query": [{"key": "tenNhaCungCap", "value": "ABC"}]}, "description": "T<PERSON>m kiếm nhà cung cấp theo tên"}, "response": []}, {"name": "Create NhaCungCap", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"maNCC\": \"NCC001\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 1\",\n  \"diaChi\": \"Địa chỉ 1\",\n  \"soDienThoai\": \"0123456789\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/nhacungcap/create", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "create"]}, "description": "<PERSON><PERSON>o mới nhà cung cấp"}, "response": []}, {"name": "Create NhaCungCap - Existing Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"maNCC\": \"NCC001\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 2\",\n  \"diaChi\": \"Địa chỉ 2\",\n  \"soDien<PERSON><PERSON>ai\": \"0987654321\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/nhacungcap/create", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "create"]}, "description": "Tạo mới nhà cung cấp với mã đã tồn tại"}, "response": []}, {"name": "Create NhaCungCap - Invalid Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"maNCC\": \"NCC003\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 3\",\n  \"diaChi\": \"Địa chỉ 3\",\n  \"soDienT<PERSON>ai\": \"0123456789\",\n  \"email\": \"invalid-email\"\n}"}, "url": {"raw": "{{baseUrl}}/nhacungcap/create", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "create"]}, "description": "Tạo mới nhà cung cấp với dữ liệu không hợp lệ"}, "response": []}, {"name": "Update NhaCungCap", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"maNCC\": \"NCC001\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 1 Updated\",\n  \"diaChi\": \"Địa chỉ 1 Updated\",\n  \"soDienThoai\": \"0123456789\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/nhacungcap/update", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật thông tin nhà cung cấp"}, "response": []}, {"name": "Update NhaCungCap - Not Found", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 999,\n  \"maNCC\": \"NCC999\",\n  \"tenNhaCungCap\": \"<PERSON>h<PERSON> cung cấp không tồn tại\",\n  \"diaChi\": \"Địa chỉ\",\n  \"soDienT<PERSON>ai\": \"0123456789\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/nhacungcap/update", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật thông tin nhà cung cấp không tồn tại"}, "response": []}, {"name": "Update NhaCungCap - Existing Code", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": 1,\n  \"maNCC\": \"NCC002\",\n  \"tenNhaCungCap\": \"Nhà cung cấp 1 Updated\",\n  \"diaChi\": \"Địa chỉ 1 Updated\",\n  \"soDienThoai\": \"0123456789\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/nhacungcap/update", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "update"]}, "description": "<PERSON><PERSON><PERSON> nhật thông tin nhà cung cấp với mã đã tồn tại"}, "response": []}, {"name": "Delete NhaCungCap", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/nhacungcap/delete?id=1", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "delete"], "query": [{"key": "id", "value": "1"}]}, "description": "Xóa nhà cung cấp theo ID"}, "response": []}, {"name": "Delete NhaCungCap - Not Found", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/nhacungcap/delete?id=999", "host": ["{{baseUrl}}"], "path": ["nhacungcap", "delete"], "query": [{"key": "id", "value": "999"}]}, "description": "<PERSON><PERSON><PERSON> nhà cung cấp không tồn tại"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}]}