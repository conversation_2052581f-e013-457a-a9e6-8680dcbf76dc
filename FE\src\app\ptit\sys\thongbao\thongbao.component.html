<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <div *ngIf="displayDialog" id="layer-popup" [ngClass]="{ active: displayDialog }">
        <app-thongbao-createment (cancel)="handleCancel($event)" [loaithuoc]="thongbaoNew" (save)="handeSave($event)">
        </app-thongbao-createment>
      </div>
      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Thông Báo</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <!-- content   -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div class="card-header border-0 align-items-center justify-content-end">
              <div class="align-items-center p-3 justify-content-between d-flex">
                <div class="flex-shrink-0">
                  <div class="text-muted">
                    <span class="fw-semibold">Total: </span>
                    <span class="fw-semibold">{{ thongBao.length }}</span>
                  </div>
                </div>
                <button type="button" class="btn btn-success shadow-none" (click)="preAdd()">
                  <i class="ri-add-circle-line align-middle me-1"></i>
                  Thêm thông báo
                </button>
              </div>
              <div class="col-auto mb-3"></div>

              <div class="col-auto">
                <p-table [value]="thongBao" [rowHover]="true" dataKey="id" [showCurrentPageReport]="true" [rows]="10"
                  [alwaysShowPaginator]="true" [paginator]="true" currentPageReportTemplate="">
                  <ng-template pTemplate="header">
                    <tr>
                      <th>Thông Báo</th>
                      <th>Nội Dung</th>
                      <th>Tiêu Đề</th>
                      <th>Ngày Tạo</th>
                      <!-- <th>Trạng Thái</th> -->
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-customer let-rowIndex="rowIndex">
                    <tr [style]="{ cursor: 'pointer' }">
                      <td>
                        <span class="badge btn-success" *ngIf="customer.loaiThongBao === 'CA_NHAN'">
                          Cá Nhân
                        </span>
                        <span class="badge btn-warning" *ngIf="customer.loaiThongBao === 'CANH_BAO'">
                          Cảnh Báo
                        </span>
                        <span class="badge btn-primary" *ngIf="customer.loaiThongBao === 'GIAO_DICH'">
                          Giao Dịch
                        </span>
                        <span class="badge btn-info" *ngIf="customer.loaiThongBao === 'HE_THONG'">
                          Hệ Thống
                        </span>
                        <span class="badge btn-danger" *ngIf="customer.loaiThongBao === 'KHUYEN_MAI'">
                          Khuyến Mãi
                        </span>
                        <span class="badge btn-secondary" *ngIf="customer.loaiThongBao === 'SU_KIEN'">
                          Sự Kiện
                        </span>
                        <span class="badge btn-dark" *ngIf="customer.loaiThongBao === 'TAI_KHOAN'">
                          Tài Khoản
                        </span>
                      </td>
                      <td>{{ customer.noiDung }}</td>
                      <td>{{ customer.tieuDe }}</td>
                      <td>{{ customer.createdAt | date : "dd/MM/yyyy" }}</td>
                      <!-- <td>
                        <span
                          class="badge btn-success"
                          *ngIf="customer.trangThai == true"
                        >
                          Hoạt động
                        </span>
                        <span
                          class="badge btn-success"
                          *ngIf="customer.trangThai == false"
                        >
                          Không hoạt động
                        </span>
                      </td> -->
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>