<PERSON><PERSON>,<PERSON><PERSON><PERSON> đ<PERSON> ki<PERSON> thử,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> thự<PERSON> hi<PERSON>,<PERSON><PERSON><PERSON> quả mong muốn,<PERSON><PERSON><PERSON> quả hiện tại,<PERSON> tiết lỗi
UI-PN-001,<PERSON><PERSON><PERSON> tra hiển thị danh sách phiếu nhập,"1. <PERSON><PERSON><PERSON> cập vào màn hình <PERSON>u <PERSON>hập
2. <PERSON><PERSON> sát danh sách phiếu nhập","1. <PERSON><PERSON> sách phiếu nhập hiển thị đầy đủ với các cột: <PERSON>, <PERSON><PERSON><PERSON>ng cấ<PERSON>, <PERSON><PERSON><PERSON><PERSON>h<PERSON>, <PERSON><PERSON><PERSON>h<PERSON><PERSON>, <PERSON><PERSON><PERSON> tiền
2. <PERSON><PERSON> (biểu tượng thùng rác) cho mỗi phiếu nhập
3. <PERSON><PERSON><PERSON> thị tổng số phiếu nhập (Total)
4. <PERSON><PERSON> nú<PERSON> ""Thêm phiếu nhập""","",""
UI-PN-002,<PERSON><PERSON><PERSON> tra chứ<PERSON> nă<PERSON> tì<PERSON> ki<PERSON>hi<PERSON>,"1. <PERSON><PERSON><PERSON> cậ<PERSON> và<PERSON> màn hình <PERSON>
2. <PERSON><PERSON><PERSON><PERSON> tên người nhập vào ô tìm kiếm
3. Nhấn nút ""Tìm kiếm""","1. Hệ thống hiển thị các phiếu nhập phù hợp với từ khóa tìm kiếm
2. Nếu không có kết quả, hiển thị danh sách trống","",""
UI-PN-003,Kiểm tra chức năng tìm kiếm với từ khóa không tồn tại,"1. Truy cập vào màn hình Phiếu Nhập
2. Nhập từ khóa không tồn tại (ví dụ: ""xyz123"")
3. Nhấn nút ""Tìm kiếm""","1. Hệ thống hiển thị danh sách trống
2. Hiển thị thông báo ""Total: 0""","",""
UI-PN-004,Kiểm tra nút Thêm phiếu nhập,"1. Truy cập vào màn hình Phiếu Nhập
2. Nhấn nút ""Thêm phiếu nhập""","1. Hệ thống chuyển đến màn hình Thêm phiếu nhập
2. Các trường nhập liệu được hiển thị đúng: Người nhập, Nhà cung cấp, và danh sách thuốc","",""
UI-PN-005,Kiểm tra nút xóa phiếu nhập,"1. Truy cập vào màn hình Phiếu Nhập
2. Nhấn biểu tượng thùng rác của một phiếu nhập bất kỳ","1. Hệ thống hiển thị hộp thoại xác nhận xóa
2. Khi xác nhận, phiếu nhập được xóa khỏi danh sách
3. Tổng số phiếu nhập giảm đi 1","",""
UI-TPN-001,Kiểm tra hiển thị màn hình Thêm phiếu nhập,"1. Truy cập vào màn hình Phiếu Nhập
2. Nhấn nút ""Thêm phiếu nhập""","1. Màn hình Thêm phiếu nhập hiển thị đầy đủ các trường:
   - Người nhập (bắt buộc)
   - Nhà cung cấp (bắt buộc)
   - Danh sách thuốc với các cột: Thuốc, Số lượng, Đơn giá, Tổng, Hạn sử dụng
2. Có nút ""Thêm"" để thêm thuốc
3. Có nút ""Thêm"" ở cuối form để lưu phiếu nhập","",""
UI-TPN-002,Kiểm tra trường bắt buộc khi thêm phiếu nhập,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Để trống trường Người nhập hoặc Nhà cung cấp
3. Nhấn nút ""Thêm"" để lưu phiếu nhập","1. Hệ thống hiển thị thông báo lỗi cho các trường bắt buộc
2. Không cho phép lưu phiếu nhập","",""
UI-TPN-003,Kiểm tra thêm thuốc vào phiếu nhập,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Nhấn nút ""Thêm"" bên cạnh danh sách thuốc","1. Hệ thống hiển thị popup chọn thuốc
2. Danh sách thuốc hiển thị đầy đủ với các cột: STT, Tên, Mã thuốc, Số lượng tồn
3. Có ô checkbox để chọn thuốc","",""
UI-TPN-004,Kiểm tra chọn thuốc từ popup,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Nhấn nút ""Thêm"" bên cạnh danh sách thuốc
3. Chọn một hoặc nhiều thuốc bằng cách tick vào checkbox
4. Nhấn nút ""Lưu""","1. Các thuốc đã chọn được thêm vào danh sách thuốc của phiếu nhập
2. Thông tin thuốc hiển thị đúng: tên thuốc, số lượng mặc định là 0, đơn giá lấy từ hệ thống, tổng tiền = 0, có trường nhập hạn sử dụng","",""
UI-TPN-005,Kiểm tra nhập số lượng và đơn giá,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Thêm thuốc vào danh sách
3. Nhập số lượng > 0
4. Nhập đơn giá > 0","1. Hệ thống tự động tính toán tổng tiền = số lượng * đơn giá
2. Tổng tiền hiển thị đúng","",""
UI-TPN-006,Kiểm tra nhập số lượng không hợp lệ,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Thêm thuốc vào danh sách
3. Nhập số lượng là ký tự chữ hoặc số âm","1. Hệ thống hiển thị thông báo lỗi
2. Không cho phép nhập giá trị không hợp lệ","",""
UI-TPN-007,Kiểm tra nhập đơn giá không hợp lệ,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Thêm thuốc vào danh sách
3. Nhập đơn giá là ký tự chữ hoặc số âm","1. Hệ thống hiển thị thông báo lỗi
2. Không cho phép nhập giá trị không hợp lệ","",""
UI-TPN-008,Kiểm tra nhập hạn sử dụng,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Thêm thuốc vào danh sách
3. Nhập hạn sử dụng bằng cách chọn từ date picker","1. Hệ thống cho phép chọn ngày từ date picker
2. Ngày được hiển thị đúng định dạng mm/dd/yyyy","",""
UI-TPN-009,Kiểm tra xóa thuốc khỏi phiếu nhập,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Thêm thuốc vào danh sách
3. Nhấn biểu tượng thùng rác bên cạnh thuốc","1. Thuốc được xóa khỏi danh sách
2. Danh sách thuốc được cập nhật","",""
UI-TPN-010,Kiểm tra lưu phiếu nhập hợp lệ,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Nhập đầy đủ thông tin: Người nhập, Nhà cung cấp
3. Thêm ít nhất một thuốc với số lượng, đơn giá và hạn sử dụng hợp lệ
4. Nhấn nút ""Thêm"" để lưu phiếu nhập","1. Hệ thống lưu phiếu nhập thành công
2. Hiển thị thông báo thành công
3. Chuyển về màn hình danh sách phiếu nhập
4. Phiếu nhập mới xuất hiện trong danh sách","",""
UI-T-001,Kiểm tra hiển thị popup Thuốc,"1. Truy cập vào màn hình Thêm phiếu nhập
2. Nhấn nút ""Thêm"" bên cạnh danh sách thuốc","1. Popup Thuốc hiển thị đầy đủ với tiêu đề ""Thuốc""
2. Có ô tìm kiếm thuốc
3. Danh sách thuốc hiển thị đầy đủ với các cột: STT, Tên, Mã thuốc, Số lượng tồn
4. Có nút ""Thêm thuốc"" ở góc trên bên phải
5. Có nút ""Hủy"" và ""Lưu"" ở cuối popup","",""
UI-T-002,Kiểm tra chức năng tìm kiếm thuốc,"1. Mở popup Thuốc
2. Nhập tên thuốc vào ô tìm kiếm
3. Nhấn nút ""Tìm kiếm""","1. Hệ thống hiển thị các thuốc phù hợp với từ khóa tìm kiếm
2. Nếu không có kết quả, hiển thị danh sách trống","",""
UI-T-003,Kiểm tra chức năng tìm kiếm với từ khóa không tồn tại,"1. Mở popup Thuốc
2. Nhập từ khóa không tồn tại (ví dụ: ""xyz123"")
3. Nhấn nút ""Tìm kiếm""","1. Hệ thống hiển thị danh sách trống","",""
UI-T-004,Kiểm tra nút Thêm thuốc,"1. Mở popup Thuốc
2. Nhấn nút ""Thêm thuốc""","1. Hệ thống chuyển đến màn hình Thêm thuốc mới
2. Các trường nhập liệu được hiển thị đúng","",""
UI-T-005,Kiểm tra chọn nhiều thuốc,"1. Mở popup Thuốc
2. Chọn nhiều thuốc bằng cách tick vào checkbox
3. Nhấn nút ""Lưu""","1. Tất cả các thuốc đã chọn được thêm vào danh sách thuốc của phiếu nhập","",""
UI-T-006,Kiểm tra nút Hủy,"1. Mở popup Thuốc
2. Chọn một số thuốc
3. Nhấn nút ""Hủy""","1. Popup đóng lại
2. Không có thuốc nào được thêm vào phiếu nhập","",""
UI-TK-001,Kiểm tra hiển thị màn hình Tồn Kho,"1. Truy cập vào màn hình Tồn Kho
2. Quan sát danh sách thuốc tồn kho","1. Danh sách thuốc tồn kho hiển thị đầy đủ với các cột: ID, Số lô, Tên thuốc, Đơn vị, Số lượng tồn
2. Hiển thị tổng số thuốc (Total)
3. Có ô tìm kiếm thuốc","",""
UI-TK-002,Kiểm tra chức năng tìm kiếm thuốc tồn kho,"1. Truy cập vào màn hình Tồn Kho
2. Nhập tên thuốc vào ô tìm kiếm
3. Nhấn nút ""Tìm kiếm""","1. Hệ thống hiển thị các thuốc phù hợp với từ khóa tìm kiếm
2. Nếu không có kết quả, hiển thị danh sách trống","",""
UI-TK-003,Kiểm tra chức năng tìm kiếm với từ khóa không tồn tại,"1. Truy cập vào màn hình Tồn Kho
2. Nhập từ khóa không tồn tại (ví dụ: ""xyz123"")
3. Nhấn nút ""Tìm kiếm""","1. Hệ thống hiển thị danh sách trống
2. Hiển thị thông báo tổng số thuốc là 0","",""
UI-KH-001,Kiểm tra hiển thị màn hình thông tin khách hàng/bệnh nhân,"1. Truy cập vào màn hình thông tin khách hàng/bệnh nhân
2. Quan sát các trường thông tin","1. Màn hình hiển thị đầy đủ các trường thông tin:
   - Thông tin cá nhân (Họ tên, Số điện thoại, Email, Địa chỉ, Ngày sinh, Giới tính)
   - Thông tin bệnh án
   - Các trường khác liên quan đến khám bệnh
2. Có nút ""Lưu"" ở cuối form","",""
UI-KH-002,Kiểm tra nhập thông tin khách hàng/bệnh nhân,"1. Truy cập vào màn hình thông tin khách hàng/bệnh nhân
2. Nhập đầy đủ thông tin vào các trường
3. Nhấn nút ""Lưu""","1. Hệ thống lưu thông tin thành công
2. Hiển thị thông báo thành công","",""
UI-KH-003,Kiểm tra trường bắt buộc khi nhập thông tin khách hàng,"1. Truy cập vào màn hình thông tin khách hàng/bệnh nhân
2. Để trống các trường bắt buộc
3. Nhấn nút ""Lưu""","1. Hệ thống hiển thị thông báo lỗi cho các trường bắt buộc
2. Không cho phép lưu thông tin","",""
UI-KH-004,Kiểm tra nhập email không hợp lệ,"1. Truy cập vào màn hình thông tin khách hàng/bệnh nhân
2. Nhập email không hợp lệ (ví dụ: ""abc"")
3. Nhấn nút ""Lưu""","1. Hệ thống hiển thị thông báo lỗi cho trường email
2. Không cho phép lưu thông tin","",""
UI-KH-005,Kiểm tra nhập số điện thoại không hợp lệ,"1. Truy cập vào màn hình thông tin khách hàng/bệnh nhân
2. Nhập số điện thoại không hợp lệ (ví dụ: chứa ký tự chữ)
3. Nhấn nút ""Lưu""","1. Hệ thống hiển thị thông báo lỗi cho trường số điện thoại
2. Không cho phép lưu thông tin","",""
UI-RESP-001,Kiểm tra tính responsive của giao diện,"1. Truy cập vào các màn hình khác nhau của ứng dụng
2. Thay đổi kích thước màn hình (thu nhỏ, phóng to)","1. Giao diện hiển thị phù hợp với kích thước màn hình
2. Các phần tử không bị che khuất hoặc tràn ra ngoài màn hình","",""
UI-RESP-002,Kiểm tra hiển thị trên các trình duyệt khác nhau,"1. Truy cập vào ứng dụng bằng các trình duyệt khác nhau (Chrome, Firefox, Edge)
2. Kiểm tra hiển thị của các màn hình","1. Giao diện hiển thị đồng nhất trên các trình duyệt
2. Các chức năng hoạt động bình thường trên tất cả các trình duyệt","",""
UI-PERF-001,Kiểm tra thời gian tải trang,"1. Truy cập vào các màn hình khác nhau của ứng dụng
2. Đo thời gian tải trang","1. Thời gian tải trang không quá 3 giây
2. Không có hiện tượng treo, đứng khi tải trang","",""
UI-PERF-002,Kiểm tra hiệu suất khi tìm kiếm với dữ liệu lớn,"1. Truy cập vào màn hình Phiếu Nhập hoặc Tồn Kho
2. Thực hiện tìm kiếm với dữ liệu lớn (nhiều bản ghi)","1. Thời gian phản hồi không quá 3 giây
2. Kết quả tìm kiếm hiển thị chính xác","",""
UI-SEC-001,Kiểm tra xác thực người dùng,"1. Truy cập vào ứng dụng
2. Kiểm tra yêu cầu đăng nhập","1. Hệ thống yêu cầu đăng nhập trước khi truy cập vào các chức năng
2. Không thể truy cập trực tiếp vào các trang nội bộ khi chưa đăng nhập","",""
UI-SEC-002,Kiểm tra phân quyền người dùng,"1. Đăng nhập với các tài khoản có quyền khác nhau
2. Kiểm tra khả năng truy cập vào các chức năng","1. Người dùng chỉ có thể truy cập vào các chức năng được phân quyền
2. Các chức năng không được phân quyền sẽ bị ẩn hoặc hiển thị thông báo lỗi khi truy cập","",""
