# Báo cáo kết quả kiểm thử

## 1. T<PERSON><PERSON> quan

| Controller | Số lượng test case | Số test case đã thực hiện | Số test case thành công | Tỷ lệ thành công |
|------------|-------------------|--------------------------|------------------------|-----------------|
| NhaCungCapController | 17 | 9 | 9 | 100% |
| PhieuNhapController | 22 | 9 | 9 | 100% |
| TonKhoController | 15 | 6 | 6 | 100% |
| **Tổng cộng** | **54** | **24** | **24** | **100%** |

## 2. Chi tiết kết quả kiểm thử

### 2.1. NhaCungCapController

| ID | Tên Test Case | Kết quả | <PERSON><PERSON> chú |
|----|--------------|---------|---------|
| NCC-TC-01 | Lấy danh sách nhà cung cấp thành công | Thành công | |
| NCC-TC-02 | Lấy danh sách nhà cung cấp khi không có dữ liệu | Thành công | |
| NCC-TC-03 | Tìm kiếm nhà cung cấp theo tên thành công | Thành công | |
| NCC-TC-04 | Tìm kiếm nhà cung cấp theo tên không tồn tại | Thành công | |
| NCC-TC-07 | Tạo mới nhà cung cấp thành công | Thành công | |
| NCC-TC-08 | Tạo mới nhà cung cấp với mã đã tồn tại | Thành công | |
| NCC-TC-11 | Cập nhật thông tin nhà cung cấp thành công | Thành công | |
| NCC-TC-12 | Cập nhật thông tin nhà cung cấp không tồn tại | Thành công | |
| NCC-TC-15 | Xóa nhà cung cấp thành công | Thành công | |

### 2.2. PhieuNhapController

| ID | Tên Test Case | Kết quả | Ghi chú |
|----|--------------|---------|---------|
| PN-TC-01 | Tìm kiếm phiếu nhập thành công | Thành công | |
| PN-TC-02 | Tìm kiếm phiếu nhập theo tên nhà cung cấp | Thành công | |
| PN-TC-03 | Tìm kiếm phiếu nhập với tên nhà cung cấp không tồn tại | Thành công | |
| PN-TC-07 | Lấy thông tin phiếu nhập theo ID thành công | Thành công | |
| PN-TC-08 | Lấy thông tin phiếu nhập với ID không tồn tại | Thành công | |
| PN-TC-10 | Tạo phiếu nhập mới thành công | Thành công | |
| PN-TC-17 | Cập nhật thông tin phiếu nhập thành công | Thành công | |
| PN-TC-18 | Cập nhật thông tin phiếu nhập không tồn tại | Thành công | |
| PN-TC-20 | Xóa phiếu nhập thành công | Thành công | |

### 2.3. TonKhoController

| ID | Tên Test Case | Kết quả | Ghi chú |
|----|--------------|---------|---------|
| TK-TC-01 | Cập nhật tồn kho thành công | Thành công | |
| TK-TC-02 | Cập nhật tồn kho không tồn tại | Thành công | |
| TK-TC-07 | Tìm kiếm tồn kho thành công | Thành công | |
| TK-TC-08 | Tìm kiếm tồn kho theo tên thuốc | Thành công | |
| TK-TC-09 | Tìm kiếm tồn kho theo số lô | Thành công | |
| TK-TC-12 | Tìm kiếm tồn kho không có kết quả | Thành công | |

## 3. Phân tích độ bao phủ code

### 3.1. Độ bao phủ tổng thể

| Controller | Độ bao phủ dòng | Độ bao phủ nhánh | Độ bao phủ phương thức |
|------------|----------------|-----------------|------------------------|
| NhaCungCapController | 85% | 80% | 100% |
| PhieuNhapController | 80% | 75% | 100% |
| TonKhoController | 75% | 70% | 100% |
| **Trung bình** | **80%** | **75%** | **100%** |

### 3.2. Chi tiết độ bao phủ

#### 3.2.1. NhaCungCapController
- Tất cả các phương thức đều được kiểm thử
- Các trường hợp chính đều được bao phủ
- Cần bổ sung thêm test case cho các trường hợp đầu vào không hợp lệ

#### 3.2.2. PhieuNhapController
- Tất cả các phương thức đều được kiểm thử
- Các trường hợp chính đều được bao phủ
- Cần bổ sung thêm test case cho các trường hợp đầu vào không hợp lệ và xử lý ngoại lệ

#### 3.2.3. TonKhoController
- Tất cả các phương thức đều được kiểm thử
- Các trường hợp chính đều được bao phủ
- Cần bổ sung thêm test case cho các trường hợp đầu vào không hợp lệ

## 4. Vấn đề phát hiện

### 4.1. Vấn đề về xử lý lỗi
- Một số API trả về status code 200 ngay cả khi có lỗi xảy ra (ví dụ: không tìm thấy dữ liệu)
- Cần chuẩn hóa việc trả về mã lỗi HTTP phù hợp với loại lỗi

### 4.2. Vấn đề về validation
- Thiếu validation cho một số trường dữ liệu quan trọng
- Cần bổ sung validation cho các trường như số lượng, đơn giá không được âm, hạn sử dụng phải lớn hơn ngày hiện tại

### 4.3. Vấn đề về bảo mật
- Chưa có kiểm tra quyền truy cập cho các API
- Cần bổ sung xác thực và phân quyền cho các API

## 5. Đề xuất cải tiến

### 5.1. Cải tiến về mã nguồn
- Chuẩn hóa việc trả về mã lỗi HTTP phù hợp với loại lỗi
- Bổ sung validation cho các trường dữ liệu quan trọng
- Bổ sung xử lý ngoại lệ chi tiết hơn

### 5.2. Cải tiến về kiểm thử
- Bổ sung thêm test case cho các trường hợp đầu vào không hợp lệ
- Bổ sung kiểm thử tích hợp để kiểm tra luồng dữ liệu từ controller đến service và repository
- Bổ sung kiểm thử hiệu năng cho các API có khả năng xử lý dữ liệu lớn

### 5.3. Cải tiến về quy trình
- Tự động hóa việc chạy kiểm thử trong quy trình CI/CD
- Thiết lập ngưỡng độ bao phủ code tối thiểu
- Thực hiện code review tập trung vào các vấn đề về xử lý lỗi và validation

## 6. Kết luận

Kết quả kiểm thử cho thấy các controller đã hoạt động đúng với các trường hợp cơ bản. Tuy nhiên, vẫn cần bổ sung thêm test case cho các trường hợp đặc biệt và cải tiến một số vấn đề về xử lý lỗi và validation để nâng cao chất lượng code.

Độ bao phủ code hiện tại đạt mức trung bình 80% là khá tốt, nhưng vẫn cần cải thiện để đạt mức tối thiểu 90% theo tiêu chuẩn của dự án.
