Test Case ID,Method,Purpose,Test Setup,Input,Expected Result,Actual Result,<PERSON>rror Details,Evaluation
NCC-UT-01,getAll(),<PERSON><PERSON><PERSON> tra phương thức getAll() trả về danh sách nhà cung cấp,"Mock NhaCungCapService.getAll() trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách nhà cung cấp",<PERSON>h<PERSON><PERSON> có input,"Controller gọi nhaCungCapService.getAll() và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách nhà cung cấp",,,
NCC-UT-02,getAll(),Kiểm tra phương thức getAll() khi service trả về danh sách rỗng,"Mock NhaCungCapService.getAll() tr<PERSON> về ResponseDTO với status=200, msg=""Thà<PERSON> công"", data=danh sách rỗng",<PERSON><PERSON><PERSON><PERSON> có input,"Controller gọi nhaCungCapService.getAll() và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách rỗng",,,
NCC-UT-03,getAll(),Kiểm tra phương thức getAll() khi service ném ra exception,"Mock NhaCungCapService.getAll() ném ra RuntimeException với message ""Database error""",Không có input,Controller bắt exception và trả về ResponseDTO với status=500 hoặc ném lại exception để được xử lý bởi global exception handler,,,
NCC-UT-04,getAll(),Kiểm tra phương thức getAll() với danh sách lớn,"Mock NhaCungCapService.getAll() trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách 1000 nhà cung cấp",Không có input,"Controller gọi nhaCungCapService.getAll() và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách 1000 nhà cung cấp",,,
NCC-UT-05,getAll(),Kiểm tra hiệu suất của phương thức getAll(),"Mock NhaCungCapService.getAll() trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách 10000 nhà cung cấp sau 2 giây",Không có input,"Controller gọi nhaCungCapService.getAll() và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách 10000 nhà cung cấp trong thời gian chấp nhận được",,,
NCC-UT-06,searchByTenNhaCungCap(),Kiểm tra phương thức searchByTenNhaCungCap() trả về danh sách nhà cung cấp,"Mock NhaCungCapService.searchByTenNhaCungCap(""ABC"") trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách nhà cung cấp có tên chứa ""ABC""",tenNhaCungCap=""ABC""","Controller gọi nhaCungCapService.searchByTenNhaCungCap(""ABC"") và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách nhà cung cấp có tên chứa ""ABC""",,,
NCC-UT-07,searchByTenNhaCungCap(),Kiểm tra phương thức searchByTenNhaCungCap() khi service trả về danh sách rỗng,"Mock NhaCungCapService.searchByTenNhaCungCap(""XYZ"") trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách rỗng",tenNhaCungCap=""XYZ""","Controller gọi nhaCungCapService.searchByTenNhaCungCap(""XYZ"") và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách rỗng",,,
NCC-UT-08,searchByTenNhaCungCap(),Kiểm tra phương thức searchByTenNhaCungCap() với tên rỗng,"Mock NhaCungCapService.searchByTenNhaCungCap("""") trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách tất cả nhà cung cấp",tenNhaCungCap=""""","Controller gọi nhaCungCapService.searchByTenNhaCungCap("""") và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách tất cả nhà cung cấp",,,
NCC-UT-09,searchByTenNhaCungCap(),Kiểm tra phương thức searchByTenNhaCungCap() khi service ném ra exception,"Mock NhaCungCapService.searchByTenNhaCungCap(""ABC"") ném ra RuntimeException với message ""Database error""",tenNhaCungCap=""ABC"",Controller bắt exception và trả về ResponseDTO với status=500 hoặc ném lại exception để được xử lý bởi global exception handler,,,
NCC-UT-10,searchByTenNhaCungCap(),Kiểm tra phương thức searchByTenNhaCungCap() với tên chứa ký tự đặc biệt,"Mock NhaCungCapService.searchByTenNhaCungCap(""ABC@#$"") trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách nhà cung cấp có tên chứa ""ABC@#$""",tenNhaCungCap=""ABC@#$""","Controller gọi nhaCungCapService.searchByTenNhaCungCap(""ABC@#$"") và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách nhà cung cấp có tên chứa ""ABC@#$""",,,
NCC-UT-11,searchByTenNhaCungCap(),Kiểm tra phương thức searchByTenNhaCungCap() với tên dài,"Mock NhaCungCapService.searchByTenNhaCungCap() với tên dài 255 ký tự trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách nhà cung cấp",tenNhaCungCap=<tên dài 255 ký tự>,"Controller gọi nhaCungCapService.searchByTenNhaCungCap() và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách nhà cung cấp",,,
NCC-UT-12,searchByTenNhaCungCap(),Kiểm tra hiệu suất của phương thức searchByTenNhaCungCap(),"Mock NhaCungCapService.searchByTenNhaCungCap(""ABC"") trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách 1000 nhà cung cấp sau 2 giây",tenNhaCungCap=""ABC""","Controller gọi nhaCungCapService.searchByTenNhaCungCap(""ABC"") và trả về ResponseDTO với status=200, msg=""Thành công"", data=danh sách 1000 nhà cung cấp trong thời gian chấp nhận được",,,
NCC-UT-13,create(),Kiểm tra phương thức create() tạo mới nhà cung cấp thành công,"Mock NhaCungCapService.create(nhaCungCapDTO) trả về ResponseDTO với status=201, msg=""Thành công"", data=nhà cung cấp vừa tạo","NhaCungCapDTO với maNCC=""NCC001"", tenNhaCungCap=""Công ty ABC"", diaChi=""123 Đường XYZ"", soDienThoai=""0123456789"", email=""<EMAIL>""","Controller gọi nhaCungCapService.create(nhaCungCapDTO) và trả về ResponseDTO với status=201, msg=""Thành công"", data=nhà cung cấp vừa tạo",,,
NCC-UT-14,create(),Kiểm tra phương thức create() khi service báo lỗi mã đã tồn tại,"Mock NhaCungCapService.create(nhaCungCapDTO) trả về ResponseDTO với status=409, msg=""Nhà cung cấp đã tồn tại"", data=null","NhaCungCapDTO với maNCC=""NCC001"" (đã tồn tại), tenNhaCungCap=""Công ty DEF""","Controller gọi nhaCungCapService.create(nhaCungCapDTO) và trả về ResponseDTO với status=409, msg=""Nhà cung cấp đã tồn tại"", data=null",,,
NCC-UT-15,create(),Kiểm tra phương thức create() khi service ném ra exception,"Mock NhaCungCapService.create(nhaCungCapDTO) ném ra RuntimeException với message ""Database error""","NhaCungCapDTO với maNCC=""NCC001"", tenNhaCungCap=""Công ty ABC""",Controller bắt exception và trả về ResponseDTO với status=500 hoặc ném lại exception để được xử lý bởi global exception handler,,,
NCC-UT-16,create(),Kiểm tra phương thức create() với dữ liệu đầy đủ,"Mock NhaCungCapService.create(nhaCungCapDTO) trả về ResponseDTO với status=201, msg=""Thành công"", data=nhà cung cấp vừa tạo","NhaCungCapDTO với đầy đủ thông tin: maNCC, tenNhaCungCap, diaChi, soDienThoai, email","Controller gọi nhaCungCapService.create(nhaCungCapDTO) và trả về ResponseDTO với status=201, msg=""Thành công"", data=nhà cung cấp vừa tạo",,,
NCC-UT-17,create(),Kiểm tra phương thức create() với dữ liệu thiếu,"Mock NhaCungCapService.create(nhaCungCapDTO) trả về ResponseDTO với status=400, msg=""Dữ liệu không hợp lệ"", data=null","NhaCungCapDTO với thiếu thông tin: maNCC=null, tenNhaCungCap=null","Controller gọi nhaCungCapService.create(nhaCungCapDTO) và trả về ResponseDTO với status=400, msg=""Dữ liệu không hợp lệ"", data=null",,,
NCC-UT-18,create(),Kiểm tra phương thức create() với email không hợp lệ,"Mock NhaCungCapService.create(nhaCungCapDTO) trả về ResponseDTO với status=400, msg=""Email không hợp lệ"", data=null","NhaCungCapDTO với email không hợp lệ: email=""invalid-email""","Controller gọi nhaCungCapService.create(nhaCungCapDTO) và trả về ResponseDTO với status=400, msg=""Email không hợp lệ"", data=null",,,
NCC-UT-19,create(),Kiểm tra phương thức create() với số điện thoại không hợp lệ,"Mock NhaCungCapService.create(nhaCungCapDTO) trả về ResponseDTO với status=400, msg=""Số điện thoại không hợp lệ"", data=null","NhaCungCapDTO với số điện thoại không hợp lệ: soDienThoai=""abc123""","Controller gọi nhaCungCapService.create(nhaCungCapDTO) và trả về ResponseDTO với status=400, msg=""Số điện thoại không hợp lệ"", data=null",,,
NCC-UT-20,update(),Kiểm tra phương thức update() cập nhật nhà cung cấp thành công,"Mock NhaCungCapService.update(nhaCungCapDTO) trả về ResponseDTO với status=200, msg=""Thành công"", data=nhà cung cấp sau khi cập nhật","NhaCungCapDTO với id=1, maNCC=""NCC001"", tenNhaCungCap=""Công ty ABC Updated""","Controller gọi nhaCungCapService.update(nhaCungCapDTO) và trả về ResponseDTO với status=200, msg=""Thành công"", data=nhà cung cấp sau khi cập nhật",,,
NCC-UT-21,update(),Kiểm tra phương thức update() khi service báo lỗi không tìm thấy nhà cung cấp,"Mock NhaCungCapService.update(nhaCungCapDTO) trả về ResponseDTO với status=404, msg=""Không tìm thấy nhà cung cấp"", data=null","NhaCungCapDTO với id=999 (không tồn tại), maNCC=""NCC999""","Controller gọi nhaCungCapService.update(nhaCungCapDTO) và trả về ResponseDTO với status=404, msg=""Không tìm thấy nhà cung cấp"", data=null",,,
NCC-UT-22,update(),Kiểm tra phương thức update() khi service báo lỗi mã đã tồn tại,"Mock NhaCungCapService.update(nhaCungCapDTO) trả về ResponseDTO với status=409, msg=""Mã Nhà cung cấp đã tồn tại"", data=null","NhaCungCapDTO với id=1, maNCC=""NCC002"" (đã tồn tại ở record khác)","Controller gọi nhaCungCapService.update(nhaCungCapDTO) và trả về ResponseDTO với status=409, msg=""Mã Nhà cung cấp đã tồn tại"", data=null",,,
NCC-UT-23,update(),Kiểm tra phương thức update() khi service ném ra exception,"Mock NhaCungCapService.update(nhaCungCapDTO) ném ra RuntimeException với message ""Database error""","NhaCungCapDTO với id=1, maNCC=""NCC001""",Controller bắt exception và trả về ResponseDTO với status=500 hoặc ném lại exception để được xử lý bởi global exception handler,,,
NCC-UT-24,update(),Kiểm tra phương thức update() với dữ liệu đầy đủ,"Mock NhaCungCapService.update(nhaCungCapDTO) trả về ResponseDTO với status=200, msg=""Thành công"", data=nhà cung cấp sau khi cập nhật","NhaCungCapDTO với đầy đủ thông tin: id, maNCC, tenNhaCungCap, diaChi, soDienThoai, email","Controller gọi nhaCungCapService.update(nhaCungCapDTO) và trả về ResponseDTO với status=200, msg=""Thành công"", data=nhà cung cấp sau khi cập nhật",,,
NCC-UT-25,update(),Kiểm tra phương thức update() với dữ liệu thiếu,"Mock NhaCungCapService.update(nhaCungCapDTO) trả về ResponseDTO với status=400, msg=""Dữ liệu không hợp lệ"", data=null","NhaCungCapDTO với thiếu thông tin: id=1, maNCC=null, tenNhaCungCap=null","Controller gọi nhaCungCapService.update(nhaCungCapDTO) và trả về ResponseDTO với status=400, msg=""Dữ liệu không hợp lệ"", data=null",,,
NCC-UT-26,update(),Kiểm tra phương thức update() với email không hợp lệ,"Mock NhaCungCapService.update(nhaCungCapDTO) trả về ResponseDTO với status=400, msg=""Email không hợp lệ"", data=null","NhaCungCapDTO với id=1, email không hợp lệ: email=""invalid-email""","Controller gọi nhaCungCapService.update(nhaCungCapDTO) và trả về ResponseDTO với status=400, msg=""Email không hợp lệ"", data=null",,,
NCC-UT-27,update(),Kiểm tra phương thức update() với số điện thoại không hợp lệ,"Mock NhaCungCapService.update(nhaCungCapDTO) trả về ResponseDTO với status=400, msg=""Số điện thoại không hợp lệ"", data=null","NhaCungCapDTO với id=1, số điện thoại không hợp lệ: soDienThoai=""abc123""","Controller gọi nhaCungCapService.update(nhaCungCapDTO) và trả về ResponseDTO với status=400, msg=""Số điện thoại không hợp lệ"", data=null",,,
NCC-UT-28,delete(),Kiểm tra phương thức delete() xóa nhà cung cấp thành công,"Mock NhaCungCapService.delete(1) trả về ResponseDTO với status=200, msg=""Thành công"", data=null",id=1,"Controller gọi nhaCungCapService.delete(1) và trả về ResponseDTO với status=200, msg=""Thành công"", data=null",,,
NCC-UT-29,delete(),Kiểm tra phương thức delete() khi service ném ra exception,"Mock NhaCungCapService.delete(1) ném ra RuntimeException với message ""Database error""",id=1,Controller bắt exception và trả về ResponseDTO với status=500 hoặc ném lại exception để được xử lý bởi global exception handler,,,
NCC-UT-30,delete(),Kiểm tra phương thức delete() khi service ném ra exception do ràng buộc khóa ngoại,"Mock NhaCungCapService.delete(1) ném ra DataIntegrityViolationException",id=1,Controller bắt exception và trả về ResponseDTO với status=409 hoặc ném lại exception để được xử lý bởi global exception handler,,,
NCC-UT-31,delete(),Kiểm tra phương thức delete() với ID không hợp lệ,"Mock NhaCungCapService.delete() với ID không hợp lệ ném ra IllegalArgumentException",id=0,Controller bắt exception và trả về ResponseDTO với status=400 hoặc ném lại exception để được xử lý bởi global exception handler,,,
NCC-UT-32,delete(),Kiểm tra phương thức delete() với ID âm,"Mock NhaCungCapService.delete() với ID âm ném ra IllegalArgumentException",id=-1,Controller bắt exception và trả về ResponseDTO với status=400 hoặc ném lại exception để được xử lý bởi global exception handler,,,
