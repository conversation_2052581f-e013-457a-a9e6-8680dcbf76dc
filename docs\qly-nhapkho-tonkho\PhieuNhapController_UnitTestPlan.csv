Test Case ID,Method,Purpose,Test Setup,Input,Expected Result,Actual Result,Error Details,Evaluation
PN-UT-01,search(),Ki<PERSON>m tra phương thức search() trả về danh sách phiếu nhập,"Mock PhieuNhapService.search(searchDTO) trả về ResponseDTO với status=200, msg=""Thành công"", data=PageDTO chứa danh sách phiếu nhập","SearchDTO với tenNhaCungCap=""ABC"", currentPage=0, size=10","Controller gọi phieuNhapService.search(searchDTO) và trả về ResponseEntity với body là ResponseDTO có status=200, msg=""Thành công"", data=PageDTO chứa danh sách phiếu nhập",,, 
PN-UT-02,search(),Kiểm tra phương thức search() khi service trả về danh sách rỗng,"Mock <PERSON>eu<PERSON>hapService.search(searchDTO) tr<PERSON> về ResponseDTO với status=200, msg=""Thành công"", data=PageDTO chứa danh sách rỗng","SearchDTO với tenNhaCungCap=""XYZ"" (không tồn tại), currentPage=0, size=10","Controller gọi phieuNhapService.search(searchDTO) và trả về ResponseEntity với body là ResponseDTO có status=200, msg=""Thành công"", data=PageDTO chứa danh sách rỗng",,, 
PN-UT-03,search(),Kiểm tra phương thức search() khi service ném ra exception,"Mock PhieuNhapService.search(searchDTO) ném ra RuntimeException với message ""Database error""","SearchDTO với tenNhaCungCap=""ABC"", currentPage=0, size=10",Controller bắt exception và trả về ResponseEntity với status code 500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
PN-UT-04,getById(),Kiểm tra phương thức getById() trả về phiếu nhập,"Mock PhieuNhapService.getById(1) trả về ResponseDTO với status=200, msg=""Thành công"", data=phiếu nhập có id=1",id=1,"Controller gọi phieuNhapService.getById(1) và trả về ResponseEntity với body là ResponseDTO có status=200, msg=""Thành công"", data=phiếu nhập có id=1",,, 
PN-UT-05,getById(),Kiểm tra phương thức getById() khi service báo lỗi không tìm thấy phiếu nhập,"Mock PhieuNhapService.getById(999) trả về ResponseDTO với status=404, msg=""Không tìm thấy phiếu nhập"", data=null",id=999,"Controller gọi phieuNhapService.getById(999) và trả về ResponseEntity với body là ResponseDTO có status=404, msg=""Không tìm thấy phiếu nhập"", data=null",,, 
PN-UT-06,getById(),Kiểm tra phương thức getById() khi service ném ra exception,"Mock PhieuNhapService.getById(1) ném ra RuntimeException với message ""Database error""",id=1,Controller bắt exception và trả về ResponseEntity với status code 500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
PN-UT-07,create(),Kiểm tra phương thức create() tạo mới phiếu nhập thành công,"Mock PhieuNhapService.create(phieuNhapDTO) trả về ResponseDTO với status=201, msg=""Thành công"", data=phiếu nhập vừa tạo","PhieuNhapDTO với nhaCungCapId=1, nguoiDungId=1, tongTien=1000000, chiTietPhieuNhaps=[{thuocId=1, soLuong=10, donGia=100000, hanSuDung=2025-12-31}]","Controller gọi phieuNhapService.create(phieuNhapDTO) và trả về ResponseEntity với body là ResponseDTO có status=201, msg=""Thành công"", data=phiếu nhập vừa tạo",,, 
PN-UT-08,create(),Kiểm tra phương thức create() khi service ném ra exception do nhà cung cấp không tồn tại,"Mock PhieuNhapService.create(phieuNhapDTO) ném ra RuntimeException với message ""Nhà cung cấp không tồn tại""","PhieuNhapDTO với nhaCungCapId=999 (không tồn tại), nguoiDungId=1",Controller bắt exception và trả về ResponseEntity với status code 500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
PN-UT-09,create(),Kiểm tra phương thức create() khi service ném ra exception do người dùng không tồn tại,"Mock PhieuNhapService.create(phieuNhapDTO) ném ra RuntimeException với message ""Người dùng không tồn tại""","PhieuNhapDTO với nhaCungCapId=1, nguoiDungId=999 (không tồn tại)",Controller bắt exception và trả về ResponseEntity với status code 500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
PN-UT-10,create(),Kiểm tra phương thức create() khi service ném ra exception do thuốc không tồn tại,"Mock PhieuNhapService.create(phieuNhapDTO) ném ra RuntimeException với message ""Thuốc không tồn tại""","PhieuNhapDTO với nhaCungCapId=1, nguoiDungId=1, chiTietPhieuNhaps=[{thuocId=999 (không tồn tại), soLuong=10, donGia=100000}]",Controller bắt exception và trả về ResponseEntity với status code 500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
PN-UT-11,update(),Kiểm tra phương thức update() cập nhật phiếu nhập thành công,"Mock PhieuNhapService.update(phieuNhapDTO) trả về ResponseDTO với status=200, msg=""Thành công"", data=phiếu nhập sau khi cập nhật","PhieuNhapDTO với id=1, nhaCungCapId=1, nguoiDungId=1, tongTien=2000000, chiTietPhieuNhaps=[{thuocId=1, soLuong=20, donGia=100000, hanSuDung=2025-12-31}]","Controller gọi phieuNhapService.update(phieuNhapDTO) và trả về ResponseEntity với body là ResponseDTO có status=200, msg=""Thành công"", data=phiếu nhập sau khi cập nhật",,, 
PN-UT-12,update(),Kiểm tra phương thức update() khi service báo lỗi không tìm thấy phiếu nhập,"Mock PhieuNhapService.update(phieuNhapDTO) trả về ResponseDTO với status=200, msg=""Không tìm thấy phiếu nhập"", data=null","PhieuNhapDTO với id=999 (không tồn tại), nhaCungCapId=1, nguoiDungId=1","Controller gọi phieuNhapService.update(phieuNhapDTO) và trả về ResponseEntity với body là ResponseDTO có status=200, msg=""Không tìm thấy phiếu nhập"", data=null",,, 
PN-UT-13,update(),Kiểm tra phương thức update() khi service ném ra exception,"Mock PhieuNhapService.update(phieuNhapDTO) ném ra RuntimeException với message ""Database error""","PhieuNhapDTO với id=1, nhaCungCapId=1, nguoiDungId=1",Controller bắt exception và trả về ResponseEntity với status code 500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
PN-UT-14,delete(),Kiểm tra phương thức delete() xóa phiếu nhập thành công,"Mock PhieuNhapService.delete(1) trả về ResponseDTO với status=200, msg=""Thành công"", data=null",id=1,"Controller gọi phieuNhapService.delete(1) và trả về ResponseEntity với body là ResponseDTO có status=200, msg=""Thành công"", data=null",,, 
PN-UT-15,delete(),Kiểm tra phương thức delete() khi service ném ra exception,"Mock PhieuNhapService.delete(1) ném ra RuntimeException với message ""Database error""",id=1,Controller bắt exception và trả về ResponseEntity với status code 500 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
PN-UT-16,delete(),Kiểm tra phương thức delete() khi service ném ra exception do ràng buộc khóa ngoại,"Mock PhieuNhapService.delete(1) ném ra DataIntegrityViolationException",id=1,Controller bắt exception và trả về ResponseEntity với status code 409 hoặc ném lại exception để được xử lý bởi global exception handler,,, 
PN-UT-17,Kiểm tra tích hợp,Kiểm tra tích hợp giữa PhieuNhapController và PhieuNhapService,"Sử dụng PhieuNhapService thật (không mock), nhưng mock các repository","SearchDTO với tenNhaCungCap=""ABC"", currentPage=0, size=10","Controller gọi phieuNhapService.search(searchDTO) và trả về ResponseEntity với body là ResponseDTO có status=200, msg=""Thành công"", data=PageDTO chứa danh sách phiếu nhập",,, 
PN-UT-18,Kiểm tra tích hợp,Kiểm tra tích hợp giữa PhieuNhapController và createTonKhoFromPhieuNhap,"Sử dụng PhieuNhapService thật (không mock), nhưng mock các repository","PhieuNhapDTO với nhaCungCapId=1, nguoiDungId=1, chiTietPhieuNhaps=[{thuocId=1, soLuong=10, donGia=100000, hanSuDung=2025-12-31}]","Controller gọi phieuNhapService.create(phieuNhapDTO), service gọi createTonKhoFromPhieuNhap và tạo TonKho mới, cập nhật soLuongTon của Thuoc",,, 
