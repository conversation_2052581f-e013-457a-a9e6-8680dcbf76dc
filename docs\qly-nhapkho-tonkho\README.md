# Kế hoạch kiểm thử Unit Test cho các Controller

## Giớ<PERSON> thiệu

Tài liệu này mô tả kế hoạch kiểm thử đơn vị (Unit Test) cho ba controller chính trong hệ thống quản lý nhập kho và tồn kho:
1. NhaCungCapController
2. PhieuNhapController
3. TonKhoController

## C<PERSON>u trúc tài liệu

Mỗi controller có hai loại tài liệu kiểm thử:
1. **API Test Plan**: Tập trung vào kiểm thử API từ góc độ người dùng (black-box testing)
2. **Unit Test Plan**: Tập trung vào kiểm thử các phương thức trong controller (white-box testing)

## Cách thực hiện Unit Test

### Công cụ và thư viện

Để thực hiện Unit Test cho các controller, chúng ta sẽ sử dụng các công cụ và thư viện sau:
- JUnit 5: Framework kiểm thử
- Mockito: <PERSON><PERSON><PERSON> viện mock đối tượng
- Spring Test: Hỗ trợ kiểm thử ứng dụng Spring Boot

### Cấu trúc Unit Test

Mỗi Unit Test sẽ có cấu trúc sau:
1. **Thiết lập (Setup)**: Khởi tạo các đối tượng cần thiết, mock các dependency
2. **Thực thi (Execute)**: Gọi phương thức cần kiểm thử
3. **Kiểm tra (Verify)**: Kiểm tra kết quả trả về và các tương tác với các dependency
4. **Dọn dẹp (Cleanup)**: Giải phóng tài nguyên (nếu cần)

### Ví dụ Unit Test

Dưới đây là ví dụ về cách viết Unit Test cho phương thức `getAll()` trong `NhaCungCapController`:

```java
@ExtendWith(MockitoExtension.class)
public class NhaCungCapControllerTest {

    @Mock
    private NhaCungCapService nhaCungCapService;

    @InjectMocks
    private NhaCungCapController nhaCungCapController;

    @Test
    public void testGetAll_Success() {
        // Setup
        List<NhaCungCap> nhaCungCaps = new ArrayList<>();
        nhaCungCaps.add(new NhaCungCap(1, "NCC001", "Công ty ABC", "123 Đường XYZ", "0123456789", "<EMAIL>"));
        
        ResponseDTO<List<NhaCungCap>> expectedResponse = ResponseDTO.<List<NhaCungCap>>builder()
            .status(200)
            .msg("Thành công")
            .data(nhaCungCaps)
            .build();
        
        when(nhaCungCapService.getAll()).thenReturn(expectedResponse);
        
        // Execute
        ResponseDTO<List<NhaCungCap>> actualResponse = nhaCungCapController.getAll();
        
        // Verify
        assertEquals(expectedResponse.getStatus(), actualResponse.getStatus());
        assertEquals(expectedResponse.getMsg(), actualResponse.getMsg());
        assertEquals(expectedResponse.getData(), actualResponse.getData());
        
        verify(nhaCungCapService, times(1)).getAll();
    }
}
```

## Kế hoạch kiểm thử chi tiết

### NhaCungCapController

File `NhaCungCapController_UnitTestPlan.csv` chứa kế hoạch kiểm thử chi tiết cho `NhaCungCapController`. Kế hoạch này bao gồm các test case cho các phương thức:
- getAll()
- searchByTenNhaCungCap()
- create()
- update()
- delete()

### PhieuNhapController

File `PhieuNhapController_UnitTestPlan.csv` chứa kế hoạch kiểm thử chi tiết cho `PhieuNhapController`. Kế hoạch này bao gồm các test case cho các phương thức:
- search()
- getById()
- create()
- update()
- delete()

### TonKhoController

File `TonKhoController_UnitTestPlan.csv` chứa kế hoạch kiểm thử chi tiết cho `TonKhoController`. Kế hoạch này bao gồm các test case cho các phương thức:
- updateTonKho()
- search()

## Hướng dẫn thực hiện

1. Tạo các class test tương ứng với mỗi controller
2. Implement các test case theo kế hoạch
3. Chạy các test và ghi lại kết quả vào cột "Actual Result"
4. Nếu có lỗi, ghi chi tiết lỗi vào cột "Error Details"
5. Đánh giá kết quả kiểm thử vào cột "Evaluation"

## Lưu ý

- Các test case nên được thực hiện độc lập với nhau
- Sử dụng mock để cô lập controller khỏi các dependency
- Kiểm tra cả trường hợp thành công và thất bại
- Kiểm tra cả trường hợp ngoại lệ (exception)
- Đảm bảo độ bao phủ mã nguồn (code coverage) cao

## Kết luận

Kế hoạch kiểm thử này giúp đảm bảo chất lượng của các controller trong hệ thống quản lý nhập kho và tồn kho. Việc thực hiện đầy đủ các test case sẽ giúp phát hiện và sửa lỗi sớm, đảm bảo hệ thống hoạt động đúng như mong đợi.
