﻿<Mã dự án>: Kế hoạch kiểm thử		
|||
| :- | - |





**<TÊN DỰ ÁN>** 

***Kế hoạch kiểm thử***

|**Mã dự án**|**<Mã dự án>**|
| -: | :- |
|**Phiên bản**|**vx/x/x**|
|**Ngày**|**dd/mm/yyyy**|







**<Nơi, ngày làm tài liệu>**

**NỘI DUNG SỬA ĐỔI**

\*M- Mới S – Sửa X - Xóa

|**Ngày**|**Mục sửa đổi**|**M\*<br>S, X**|**Nội dung sửa đổi**|**Người sửa đổi**|**Lần sửa đổi**|
| :-: | :-: | :-: | :-: | :-: | :-: |
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||
|||||||

**TRANG KÝ**


**NGƯỜI LẬP:**		<Tên>		           	<Ngày>			

`		`<Vị trí>

**NGƯỜI KIỂM TRA:**	<Tên>		           	<Ngày>			

`		`<Vị trí>

**NGƯỜI PHÊ DUYỆT:**	<Tên>		           	<Ngày>			

`		`<Vị trí>

**MỤC LỤC**

[**1.**	**GIỚI THIỆU	3****](#_toc309821875)**

[1.1	Mục đích	3](#_toc309821876)

[1.2	Phạm vi	3](#_toc309821877)

[1.3	Tài liệu tham khảo	3](#_toc309821878)

[1.4	Từ và thuật ngữ	3](#_toc309821879)

[**2.**	**NỘI DUNG	3****](#_toc309821880)

[2.1.	Yêu cầu kiểm thử	3](#_toc309821881)

[2.2.	Tiêu chí chấp nhận	3](#_toc309821882)

[2.3.	Chiến lược kiểm thử	3](#_toc309821883)

[2.4.	Nguồn lực và tài nguyên hệ thống	3](#_toc309821884)

[2.5.	Kế hoạch và tiến độ thực hiện	3](#_toc309821885)

[2.6.	Các tài liệu, báo cáo kiểm thử cần có	3](#_toc309821886)

[**3.**	**CÁC LƯU Ý KHÁC	3****](#_toc309821887)
1. # <a name="_toc309821875"></a>**GIỚI THIỆU**
   1. ## <a name="_toc309821876"></a>***Mục đích***
Tài liệu kế hoạch kiểm thử cho dự án <*Tên dự án*> dùng để:

- Xác định những thông tin dự án và các phần dự án cần được kiểm thử
- Liệt kê những yêu cầu kiểm thử (Test Requirements)
- Nêu ra những phương pháp, chiến lược kiểm thử nên sử dụng
- Xác định nguồn lực, ước tính thời gian thực hiện
- Liệt kê những kết quả, tài liệu có được sau khi thực hiện kiểm thử
  1. ## <a name="_toc309821877"></a>***Phạm vi***
*[Mô tả các lọai kiểm thử sẽ được chỉ ra trong kế hoạch này. Mô tả ngắn gọn dự án, đặc tính, chức năng sẽ được kiểm thử /chức năng ko kiểm thử (nếu có)]. Trách nhiệm người thực hiện kiểm thử*

*Ví dụ:(Ví dụ này cần được xóa khi tạo cho dự án thực tế)\
Tài liệu này mô tả phạm vi các hoạt động kiểm thử của hệ thống Messenger. Tóm tắt các yêu cầu chức năng cần kiểm thử, cách thức kiểm thử, môi trường thực hiện, các nguồn lực cần thiết, trách nhiệm người thực hiện kiểm thử.*

1. *Các trường hợp sử dụng cần phải kiểm tra:*

*• Đăng ký thành viên \
• Đăng nhập \
• Ngắt kết nối \
• Xem tin nhắn off-line \
• Sửa đổi hồ sơ: Chang mật khẩu và các thông tin khác \
• Gửi và nhận tin nhắn \
• Gửi và Nhận tập tin \
• Bạn bè / Nhóm duy trì: Thêm một người bạn mới / nhóm, xóa một người bạn / nhóm, bạn bè tìm kiếm \
b. Để đảm bảo tất cả các yêu cầu của chương trình được kiểm tra kỹ lưỡng, các chiến lược thử nghiệm sau đây sẽ được sử dụng:\
• Kiểm tra hệ thống (system testing) \
• Kiểm tra giao diện người sử dụng (User interface testing)\
• Thử nghiệm tính năng (Performance testing)\
…\
c. Trách nhiệm thực hiện: \
• Developer sẽ chịu trách nhiệm kiểm tra đơn vị (Unit Testing)\
• Tester sẽ được chịu trách nhiệm kiểm tra Hệ thống (System testing), thử nghiệm giao diện người dùng (User Interface Testing), Performance testing…*
1. ## <a name="_toc309821878"></a>***Tài liệu tham khảo***
*[Danh sách các tài liệu đã có của dự án ]*

|**STT**|**Tên tài liệu**	|**Giải thích**|
| - | - | - |
|1|||
|2 |||
1. ## <a name="_toc309821879"></a>***Từ và thuật ngữ***
*[Giải thích các thuật ngữ được sử dụng trong tài liệu này ]*

|**STT**|**Viết tắt và thuật ngữ**|**Giải thích**|
| - | - | - |
|1|||
|2|||
|3|||
|4|||
|5|||

1. # <a name="_toc297195109"></a><a name="_toc309821880"></a>**NỘI DUNG**
   1. ## <a name="_toc297195110"></a><a name="_toc309821881"></a>***Yêu cầu kiểm thử***
Danh sách dưới đây sẽ chỉ ra những trường hợp (yêu cầu chức năng, yêu cầu hệ thống, yêu cầu phi chức năng, ..) đã được xác định là mục tiêu để thử nghiệm.

*[Mô tả những phân hệ kiểm thử]*

*Ví dụ:*

*Kiểm tra chức năng hệ thống (system testing):*

- *Kiểm tra đăng nhập, ngắt kết nối, đăng ký thành viên, chỉnh sửa hồ sơ, thêm bạn bè/nhóm mới, xóa bạn bè/nhóm. Tìm kiếm bạn bè. Tin nhắn gửi,nhận. Gửi/mở tập tin đính kèm, tin nhắn offline…*
- *Kiểm tra các chức năng của menu chính…*

*Kiểm tra giao diện người sử dụng*

- *Kiểm tra việc dễ dàng chuyển hướng thông qua mẫu thiết kế màn hình*
- *Kiểm tra màn hình mẫu phù hợp tiêu chuẩn của windows 95/98, Windows NT4.0, Win 2000…*

*Kiểm tra hiệu năng (Performance testing)*

- *Xác nhận thời gian đáp ứng để đăng nhập. Hệ thống sẽ cung cấp cho việc đăng nhập ko quá 30 giây*
- *Xác nhận thời gian đáp ứng gửi, nhân tin nhắn. Chỉ cho phép chậm không quá 30 giây*
- *Xác định thời gian đáp ứng cho việc tìm kiếm bạn bè. Hệ thống sẽ cung cấp cho việc tìm kiếm không quá 100 giây cho việc tìm kiếm với toàn bộ các tiêu chí*
1. ## <a name="_toc309821882"></a>**Tiêu chí chấp nhận**
- Toàn bộ các test case phải được thực hiện
- Xác định tỉ lệ % các test case đạt yêu cầu và % các test case chưa đạt với những lỗi nhỏ
1. ## <a name="_toc297195111"></a><a name="_toc309821883"></a>***Chiến lược kiểm thử***
Chiến lược kiểm thử trình bày các phương pháp được đề nghị để kiểm thử ứng dụng phần mềm. Phần yêu cầu kiểm thử chỉ ra những gì sẽ được kiểm tra, còn phần chiến lược sẽ mô tả cách thức kiểm tra như thế nào

Mục đích của chiến lược kiểm thử là đưa ra kỹ thuật nào sẽ được sử dụng và các tiêu chí cho biết khi nào việc kiểm thử hoàn tất.
1. ### ***Các loại kiểm thử***
Bảng các lọai kiểm thử có thể tiến hành và phục vụ cho các mục đích khác nhau thông qua các giai đọan của dự án, bao gồm units, intergrated units, application and system. (do khách hàng yêu cầu, lựa chọn)

*Lựa chọn các kiểu kiểm thử áp dụng cho dự án này, và xóa các kiểu kiểm thử không sử dụng.*
### <a name="_toc324843641"></a><a name="_toc324851948"></a><a name="_toc324915531"></a><a name="_toc433104444"></a><a name="_toc135541610"></a><a name="_toc135563780"></a>*Kiểm thử tính toàn vẹn về dữ liệu và CSDL - Data and Database Integrity Testing*

|**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
### <a name="_toc135541611"></a><a name="_toc135563781"></a>*Kiểm thử về chức năng – System Testing*

|**Mục đích:**|Đảm bảo các chức năng của hệ thống (bao gồm chức năng navigator, nhập liệu, xử lý dữ liệu và khôi phục dữ liệu) hoạt động chính xác.|
| :- | :- |
|**Kỹ thuật:**|<p>Thực hiện mỗi use case, use-case flow, hoặc chức năng bằng cách sử dụng dữ liệu hợp lệ và không hợp lệ để xác nhận:</p><p>-  Các kết quả đã mong đợi xảy ra khi nhập vào dữ liệu hợp lệ.</p><p>-  Các thông báo hoặc cảnh báo lỗi xuất hiện khi nhập vào dữ liệu không hợp lệ</p><p>- Các quy tắc nghiệp vụ được áp dụng chính xác</p>|
|**Tiêu chí hoàn thành:**|<p>Tất cả các thử nghiệp đã lập kế hoạch đều được thực hiện</p><p>Tất cả các lỗi đã xác định đều được đề cập đến</p>|
|**Lưu ý:**|Xác định và mô tả các yếu tố hoặc các vấn đề (bên trong hoặc bên ngoài) ảnh hưởng đến việc cài đặt và thực hiện việc kiểm tra chức năng|
### <a name="_toc433104446"></a><a name="_toc135541612"></a><a name="_toc135563782"></a>*Kiểm thử về chu trình nghiệp vụ - Business Cycle Testing*

|<a name="_toc327254065"></a><a name="_toc327255030"></a><a name="_toc327255099"></a><a name="_toc327255338"></a><a name="_toc433104447"></a><a name="_toc135541613"></a><a name="_toc135563783"></a>**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
### *Kiểm thử về giao diện người dùng - User Interface Testing*

|<a name="_toc327254066"></a><a name="_toc327255031"></a><a name="_toc327255100"></a><a name="_toc327255339"></a><a name="_toc433104448"></a><a name="_toc135541614"></a><a name="_toc135563784"></a>**Mục đích:**|<p>Xác nhận:</p><p>-  Việc đi lướt qua hệ thống cần test phản ánh chính xác các chức năng và các</p><p>yêu cầu nghiệp vụ; bao gồm giao diện giữa các trang màn hình, giữa các</p><p>trường, và việc sử dụng các phương thức truy nhập (các phím tab, xê dịch</p><p>chuột, các phím chức năng)</p><p>-  Các đối tượng và các thuộc tính của cửa sổ như: các menu, kích thước, vị trí, trạng thái và sự phù hợp với các chuẩn.</p>|
| :- | :- |
|**Kỹ thuật:**|<p>Với mỗi cửa sổ màn hình, tạo ra hoặc chỉnh sửa các thử nghiệm để xác nhận rằng:</p><p>- Các cửa sổ đã được liên kết theo đúng trật tự</p><p>- Các trạng thái của đối tượng trong mỗi cửa sổ màn hình và các đối tượng là</p><p>chính xác.</p>|
|**Tiêu chí hoàn thành:**|Mỗi cửa sổ màn hình đã xác nhận thành công; và giữ nguyên tính nhất quán với phiên bản và trong giới hạnh tiêu chuẩn cho phép.|
|**Lưu ý:**|<p>Không phải tất cả các thuộc tính dành cho người dùng và các đối tượng của</p><p>các hãng thứ 3 đều có thể truy nhập được.</p>|
### *Kiểm thử về hiệu suất - Performance Testing*

|<a name="_toc417790796"></a><a name="_toc433104449"></a><a name="_toc135541615"></a><a name="_toc135563785"></a>**Mục đích:**|<p>Kiểm tra hiệu suất thực hiện của các giao dịch và các chức năng nghiệp vụ xác định dưới các điều kiện sau đây:</p><p>-  Trong trường hợp chịu tải bình thường</p><p>-  Trong trường hợp chịu tải kém nhất</p>|
| :- | :- |
|**Kỹ thuật:**|<p>Sử dụng các thủ tục kiểm tra được xây dựng để kiểm tra chức năng hoặc</p><p>kiểm tra chu trình nghiệp vụ</p><p>-  Chỉnh sửa các file dữ liệu để tăng số lượng giao dịch hoặc chỉnh sửa các</p><p>scripts để tăng tần suất xảy ra mỗi giao dịch</p><p>-  Các script nên chạy trên một máy (trường hợp tốt nhất để làm chuẩn là một</p><p>người sử dụng, một giao dịch) và nên lặp lại với nhiều khách hàng (ảo hoặc thực, xem thêm phần Lưu ý dưới đây)</p>|
|**Tiêu chí hoàn thành:**|<p>-  1 giao dịch đơn hoặc 1 người dùng: thực hiện thành công khi các test scripts </p><p>không gây ra lỗi và kết thúc trong khoảng thời gian mong muốn hoặc trong</p><p>khoảng thời gian được phân bố cho giao dịch đó</p><p>- Đa giao dịch hoặc đa người dùng: thực hiện thành công khi test script không có lỗi và kết thúc trong khoảng thời gian chấp nhận được</p>|
|**Lưu ý:**|<p>Việc kiểm tra hiệu suất bao gồm cả tải trọng nền của server.  </p><p>Một số phương pháp có thể dùng để kiểm tra tải trọng, bao gồm:</p><p>- Chuyển trực tiếp các giao dịch sang server, thông thường dưới dạng câu </p><p>lệnh SQL.</p><p>- Tạo người dùng ảo để mô phỏng trường hợp nhiều người dùng, thường vài </p><p>trăm. Các công cụ mô phỏng truy nhập từ xa (Remote Terminal Emulation) để </p><p>thực hiện việc tạo tải này. Kỹ thuật này cũng có thể dụng để gây ra quá tải cho</p><p>mạng.</p><p>- Sử dụng nhiều client vật lý, tất cả đều chạy các đoạn test script để làm tăng </p><p>tải của hệ thống.  </p><p>Việc kiểm tra hiệu suất nên thực hiện trên một máy chuyên dụng hoặc tại một </p><p>thời điểm nhất định. Điều này cho phép đo lường chính xác và điều khiển đầy </p><p>đủ. Cơ sở dữ liệu dùng cho việc test hiệu suất nên có kích thước thật hoặc có </p><p>tỉ lệ tương ứng.</p>|
### *Kiểm thử về khả năng chịu tải - Load Testing*

|**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
### <a name="_toc327254067"></a><a name="_toc327255032"></a><a name="_toc327255101"></a><a name="_toc327255340"></a><a name="_toc433104450"></a><a name="_toc135541616"></a><a name="_toc135563786"></a>*Kiểm thử về khả năng chịu áp lực - Stress Testing*

|**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
### <a name="_toc327254068"></a><a name="_toc327255033"></a><a name="_toc327255102"></a><a name="_toc327255341"></a><a name="_toc433104451"></a><a name="_toc135541617"></a><a name="_toc135563787"></a><a name="_toc314978540"></a>*Kiểm thử về khối lượng - Volume Testing*

|**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
### <a name="_toc433104452"></a><a name="_toc135541618"></a><a name="_toc135563788"></a><a name="_toc314978541"></a><a name="_toc327254070"></a><a name="_toc327255035"></a><a name="_toc327255104"></a><a name="_toc327255343"></a>*Kiểm thử về tính bảo mật và khả năng kiểm soát truy cập - Security and Access Control Testing*

|**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
### <a name="_toc417790800"></a><a name="_toc433104453"></a><a name="_toc135541619"></a><a name="_toc135563789"></a>*Kiểm thử về khả năng chịu lỗi và phục hồi - Failover and Recovery Testing*

|**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
### <a name="_toc327254071"></a><a name="_toc327255036"></a><a name="_toc327255105"></a><a name="_toc327255344"></a><a name="_toc433104454"></a><a name="_toc135541620"></a><a name="_toc135563790"></a>*Kiểm thử về cấu hình - Configuration Testing*

|<a name="_toc327254072"></a><a name="_toc327255037"></a><a name="_toc327255106"></a><a name="_toc327255345"></a><a name="_toc433104455"></a><a name="_toc135541621"></a><a name="_toc135563791"></a>**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
### *Kiểm thử việc cài đặt - Installation Testing*

|**Mục đích:**||
| :- | :- |
|**Kỹ thuật:**||
|**Tiêu chí hoàn thành:**||
|**Lưu ý:**||
1. ### ***Giai đoạn kiểm thử***

<table><tr><th colspan="1" rowspan="2"><b>Loại kiểm thử</b></th><th colspan="4" valign="top"><b>Giai đoạn kiểm thử</b></th></tr>
<tr><td colspan="1" valign="top"><b>Unit</b></td><td colspan="1" valign="top"><b>Integration</b></td><td colspan="1" valign="top"><b>System</b></td><td colspan="1" valign="top"><b>Acceptance</b></td></tr>
<tr><td colspan="1" valign="top">Kiểm thử chức năng</td><td colspan="1" valign="top">x</td><td colspan="1" valign="top">x</td><td colspan="1" valign="top">x</td><td colspan="1" valign="top">x</td></tr>
<tr><td colspan="1" valign="top">Kiểm thử giao diện</td><td colspan="1" valign="top"></td><td colspan="1" valign="top">x</td><td colspan="1" valign="top">x</td><td colspan="1" valign="top">x</td></tr>
<tr><td colspan="1" valign="top">Kiểm thử hiệu năng</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td></tr>
<tr><td colspan="1" valign="top">………</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td></tr>
<tr><td colspan="1" valign="top">………</td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td><td colspan="1" valign="top"></td></tr>
</table>
1. ### ***Công cụ kiểm thử***
Các công cụ sau sẽ được dùng cho dự án

||**Công cụ**|**Nhà sản xuất/ Tự phát triển**|**Phiên bản**|
| :- | :- | :- | :- |
|||||
|||||
|||||
|||||
|||||
|||||
1. ## <a name="_toc297195112"></a><a name="_toc309821884"></a>***Nguồn lực và tài nguyên hệ thống***
Bảng này chỉ ra nhân sự cụ thể thực hiện dự án
1. ### ***Nhân sự***

|**Người thực hiện**|**Vị trí**|**Mô tả công việc**|
| :-: | :-: | :-: |
|Nguyễn văn A|Test lead|<p>- Lập test plan</p><p>&emsp;- Review test case, test data</p><p>&emsp;- Làm báo cáo kết quả dự án</p>|
|Trân Văn B|Tester|<p>- Viết test case</p><p>&emsp;- Chuẩn bị dữ liệu test...</p>|
||||
||||
1. ### ***Hệ thống***

|**Yêu cầu về phần cứng**||
| :-: | :- |
|**Tài nguyên**|**Tên / Kiểu (loại)**|
|Máy chủ CSDL (Database Server)||
|—Tên máy chủ||
|—Tên CSDL||
|`   `Các thông tin cấu hình khác||
|Máy kiểm thử||
|—Những cấu hình cụ thể cần thiết||

|<a name="_toc297195113"></a>**Yêu cầu về phần mềm**||
| :-: | :- |
|**Tài nguyên**|**Tên / Kiểu (loại)**|
|Window NT 4.0 Server (Service Pack 4 or above)|Web server and application server|
|||
|||
1. ## <a name="_toc309821885"></a>***Kế hoạch và tiến độ thực hiện***
   1. ### ***Kế hoạch thực hiện***

|**Nhiệm vụ** |**Ngày bắt đầu**|**Ngày kết thúc**|
| :-: | :-: | :-: |
|Lâp kế họach|||
|Viết test case|||
|Thực hiện kiểm thử Unit|||
|Thực hiện kiểm thử System|||
|Đánh giá kết quả kiểm thử|||
1. ### <a name="_toc297195114"></a>***Tiến độ thực hiện***
*[Lịch trình kiểm thử là một phần không thể tách rời của kế hoạch dự án.Bảng dưới đây là ước tính thời gian viết các trường hợp kiểm thử và thực hiện các kiểm thử. Trên cơ sở này PM sẽ có lên kế hoạch chi tiết trong kế hoạch dự án (Detail plan.mpp)*

|**Công việc**|**Người thực hiện**|**Ngày bắt đầu**|**Ngày kết thúc**|
| :-: | :-: | :-: | :-: |
|Viết tài liệu kế hoạch kiểm thử||||
|Viết test case||||
|Thực hiện Unit test||||
|Thực hiện Integration test||||
|Thực hiện System test||||
|Đánh giá kết quả thực hiện||||
1. ## <a name="_toc309821886"></a>***Các tài liệu, báo cáo kiểm thử cần có***
*[Phần này chỉ ra các tài liệu, báo cáo cần có trong quá trình kiểm thử: tên từng tài liệu là gì, do ai làm, làm khi nào. Dưới đây là một ví dụ điển hình cho phần này (tuỳ từng dự án cụ thể test leader có thể chỉ ra thêm các tài liệu khác)]*

|**STT**|**Tài liệu** |**Ghi chú**|
| :-: | :-: | :-: |
|1|Kế hoạch kiểm thử|Tài liệu này cần được lập bởi test leader trước khi bắt tay vào kiểm thử.|
|2|Test Case|Mô tả các trường hợp |
|3|Defect logs|Cập nhật kết quả báo cáo cho từng nội dung cụ thể|
|4|Test report|Khi kiểm thử hết một vòng hoặc trước khi kết thúc dự án, test leader cần cho ra báo cáo tổng kết quá trình đánh giá trình bày tổng kết về kết quả kiểm thử và đánh giá hệ thống. Báo cáo này sẽ được gửi cho QA Lead, Project Lead.|
###
###
1. # <a name="_toc309821887"></a>**CÁC LƯU Ý KHÁC**

`	`Tài liệu nội bộ	6/15/
