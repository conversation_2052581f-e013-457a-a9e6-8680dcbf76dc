Test Case ID,UI Screen,Purpose,Steps,Expected Results,Actual Results,<PERSON><PERSON><PERSON>ails,Evaluation
UI-TK-001,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> tra tiêu đề trang,"1. Mở màn hình Tồn <PERSON>ho
2. <PERSON><PERSON><PERSON> tra tiêu đề trang","Tiêu đề ""TỒN KHO"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-002,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> tra nhãn cột ID,"1. Mở màn hình Tồn Kho
2. <PERSON><PERSON><PERSON> tra nhãn cột ID","<PERSON>hãn ""ID"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-003,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> tra nhãn cột <PERSON>ã số,"1. Mở màn hình <PERSON>ồ<PERSON>
2. <PERSON><PERSON><PERSON> tra nhãn cột <PERSON>ã số","<PERSON>h<PERSON><PERSON> ""<PERSON><PERSON> số"" hiển thị đúng ch<PERSON> tả, font chữ và kích thư<PERSON>c phù hợp",,,
UI-TK-004,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> tra nhãn cột Tên thuốc,"1. Mở màn hình Tồn Kho
2. Kiểm tra nhãn cột Tên thuốc","Nhãn ""Tên thuốc"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-005,Tồn Kho,Kiểm tra nhãn cột Đơn vị,"1. Mở màn hình Tồn Kho
2. Kiểm tra nhãn cột Đơn vị","Nhãn ""Đơn vị"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-006,Tồn Kho,Kiểm tra nhãn cột Số lượng tồn,"1. Mở màn hình Tồn Kho
2. Kiểm tra nhãn cột Số lượng tồn","Nhãn ""Số lượng tồn"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TK-007,Tồn Kho,Kiểm tra căn chỉnh các cột,"1. Mở màn hình Tồn Kho
2. Kiểm tra căn chỉnh của các cột","Các cột được căn chỉnh hợp lý (ID và Mã số căn trái, Tên thuốc căn trái, Đơn vị căn giữa, Số lượng tồn căn phải)",,,
UI-TK-019,Tồn Kho,Kiểm tra hiển thị mã thuốc,"1. Mở màn hình Tồn Kho
2. Kiểm tra hiển thị mã thuốc trong bảng","Mã thuốc hiển thị đúng định dạng, không bị cắt ngắn",,,
UI-TK-020,Tồn Kho,Kiểm tra hiển thị tên thuốc,"1. Mở màn hình Tồn Kho
2. Kiểm tra hiển thị tên thuốc trong bảng","Tên thuốc hiển thị đúng, không bị cắt ngắn hoặc tràn ra ngoài cột",,,
UI-TK-021,Tồn Kho,Kiểm tra hiển thị đơn vị,"1. Mở màn hình Tồn Kho
2. Kiểm tra hiển thị đơn vị trong bảng","Đơn vị hiển thị đúng, không bị cắt ngắn",,,
UI-TK-022,Tồn Kho,Kiểm tra hiển thị số lượng tồn,"1. Mở màn hình Tồn Kho
2. Kiểm tra hiển thị số lượng tồn trong bảng","Số lượng tồn hiển thị đúng định dạng số, căn phải và không bị cắt ngắn",,,
UI-TK-023,Tồn Kho,Kiểm tra hiển thị trên màn hình desktop,"1. Mở màn hình Tồn Kho trên desktop (>1200px)
2. Kiểm tra hiển thị tổng thể","Giao diện hiển thị đầy đủ, các cột căn chỉnh đúng, không có thanh cuộn ngang",,,
UI-TK-024,Tồn Kho,Kiểm tra hiển thị trên màn hình tablet,"1. Mở màn hình Tồn Kho trên tablet (768px-1024px)
2. Kiểm tra hiển thị tổng thể","Giao diện hiển thị phù hợp, có thể có thanh cuộn ngang nhưng vẫn dễ đọc",,,
UI-TK-025,Tồn Kho,Kiểm tra hiển thị trên màn hình mobile,"1. Mở màn hình Tồn Kho trên mobile (<768px)
2. Kiểm tra hiển thị tổng thể","Giao diện hiển thị phù hợp với thiết bị di động, có thể chuyển sang dạng danh sách thay vì bảng",,,
UI-TK-035,Tồn Kho,Kiểm tra hiển thị khi không có dữ liệu,"1. Xóa tất cả dữ liệu hoặc tìm kiếm với từ khóa không có kết quả
2. Kiểm tra hiển thị","Hệ thống hiển thị thông báo ""Không có dữ liệu"" hoặc bảng trống với thông báo phù hợp",,,
UI-TK-008,Tồn Kho,Kiểm tra ô tìm kiếm,"1. Mở màn hình Tồn Kho
2. Kiểm tra ô tìm kiếm","Ô tìm kiếm hiển thị với placeholder phù hợp, kích thước đủ lớn để nhập dữ liệu",,,
UI-TK-009,Tồn Kho,Kiểm tra nút Tìm kiếm,"1. Mở màn hình Tồn Kho
2. Kiểm tra nút Tìm kiếm","Nút Tìm kiếm hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-TK-010,Tồn Kho,Kiểm tra tìm kiếm với từ khóa hợp lệ,"1. Nhập từ khóa hợp lệ (ví dụ: ""Amoxicillin"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa ""Amoxicillin""",,,
UI-TK-011,Tồn Kho,Kiểm tra tìm kiếm với từ khóa không hợp lệ,"1. Nhập từ khóa không hợp lệ (ví dụ: ""xyz123"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị thông báo không tìm thấy kết quả hoặc danh sách trống",,,
UI-TK-012,Tồn Kho,Kiểm tra tìm kiếm với ký tự đặc biệt,"1. Nhập ký tự đặc biệt (ví dụ: ""@#$%"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống xử lý đúng, không bị lỗi và hiển thị kết quả phù hợp hoặc thông báo không tìm thấy",,,
UI-TK-013,Tồn Kho,Kiểm tra tìm kiếm với chuỗi dài,"1. Nhập chuỗi dài (>50 ký tự) vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống xử lý đúng, không bị lỗi và hiển thị kết quả phù hợp hoặc thông báo không tìm thấy",,,
UI-TK-030,Tồn Kho,Kiểm tra tìm kiếm với từ khóa tiếng Việt có dấu,"1. Nhập từ khóa tiếng Việt có dấu (ví dụ: ""Thuốc đau đầu"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa tiếng Việt có dấu",,,
UI-TK-031,Tồn Kho,Kiểm tra tìm kiếm với từ khóa tiếng Việt không dấu,"1. Nhập từ khóa tiếng Việt không dấu (ví dụ: ""Thuoc dau dau"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả phù hợp, có thể tìm được các từ có dấu tương ứng",,,
UI-TK-032,Tồn Kho,Kiểm tra tìm kiếm với từ khóa viết hoa,"1. Nhập từ khóa viết hoa (ví dụ: ""AMOXICILLIN"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả không phân biệt chữ hoa/thường",,,
UI-TK-033,Tồn Kho,Kiểm tra tìm kiếm với từ khóa một phần,"1. Nhập một phần của từ khóa (ví dụ: ""amox"" thay vì ""amoxicillin"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa phần từ khóa đã nhập",,,
UI-TK-034,Tồn Kho,Kiểm tra tìm kiếm với mã thuốc,"1. Nhập mã thuốc (ví dụ: ""T001"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả có mã thuốc tương ứng",,,
UI-TK-014,Tồn Kho,Kiểm tra nút phân trang Trước,"1. Nếu có nhiều hơn 10 mục, chuyển đến trang 2
2. Nhấn nút Trước","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-TK-015,Tồn Kho,Kiểm tra nút phân trang Sau,"1. Nếu đang ở trang 1 và có nhiều hơn 10 mục
2. Nhấn nút Sau","Hệ thống chuyển đến trang 2 và hiển thị dữ liệu chính xác",,,
UI-TK-016,Tồn Kho,Kiểm tra nút phân trang Đầu tiên,"1. Nếu đang ở trang khác trang 1
2. Nhấn nút Đầu tiên (<<)","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-TK-017,Tồn Kho,Kiểm tra nút phân trang Cuối cùng,"1. Nếu đang ở trang không phải trang cuối
2. Nhấn nút Cuối cùng (>>)","Hệ thống chuyển đến trang cuối và hiển thị dữ liệu chính xác",,,
UI-TK-018,Tồn Kho,Kiểm tra nhấp vào số trang,"1. Nếu có nhiều trang
2. Nhấn vào số trang cụ thể (ví dụ: 3)","Hệ thống chuyển đến trang đã chọn và hiển thị dữ liệu chính xác",,,
UI-TK-026,Tồn Kho,Kiểm tra sắp xếp theo ID,"1. Mở màn hình Tồn Kho
2. Nhấp vào tiêu đề cột ID","Dữ liệu được sắp xếp theo ID tăng dần hoặc giảm dần",,,
UI-TK-027,Tồn Kho,Kiểm tra sắp xếp theo Mã số,"1. Mở màn hình Tồn Kho
2. Nhấp vào tiêu đề cột Mã số","Dữ liệu được sắp xếp theo Mã số tăng dần hoặc giảm dần",,,
UI-TK-028,Tồn Kho,Kiểm tra sắp xếp theo Tên thuốc,"1. Mở màn hình Tồn Kho
2. Nhấp vào tiêu đề cột Tên thuốc","Dữ liệu được sắp xếp theo Tên thuốc tăng dần hoặc giảm dần",,,
UI-TK-029,Tồn Kho,Kiểm tra sắp xếp theo Số lượng tồn,"1. Mở màn hình Tồn Kho
2. Nhấp vào tiêu đề cột Số lượng tồn","Dữ liệu được sắp xếp theo Số lượng tồn tăng dần hoặc giảm dần",,,
UI-PN-001,Phiếu Nhập,Kiểm tra tiêu đề trang,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra tiêu đề trang","Tiêu đề ""PHIẾU NHẬP"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-002,Phiếu Nhập,Kiểm tra nhãn cột ID,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột ID","Nhãn ""ID"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-003,Phiếu Nhập,Kiểm tra nhãn cột Mã công ty,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột Mã công ty","Nhãn ""Mã công ty"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-004,Phiếu Nhập,Kiểm tra nhãn cột Người nhập,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột Người nhập","Nhãn ""Người nhập"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-005,Phiếu Nhập,Kiểm tra nhãn cột Ngày nhập,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột Ngày nhập","Nhãn ""Ngày nhập"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-006,Phiếu Nhập,Kiểm tra nhãn cột Tổng tiền,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nhãn cột Tổng tiền","Nhãn ""Tổng tiền"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-PN-007,Phiếu Nhập,Kiểm tra căn chỉnh các cột,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra căn chỉnh của các cột","Các cột được căn chỉnh hợp lý (ID và Mã công ty căn trái, Người nhập căn trái, Ngày nhập căn giữa, Tổng tiền căn phải)",,,
UI-PN-023,Phiếu Nhập,Kiểm tra hiển thị mã công ty,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra hiển thị mã công ty trong bảng","Mã công ty hiển thị đúng định dạng, không bị cắt ngắn",,,
UI-PN-024,Phiếu Nhập,Kiểm tra hiển thị người nhập,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra hiển thị người nhập trong bảng","Tên người nhập hiển thị đúng, không bị cắt ngắn hoặc tràn ra ngoài cột",,,
UI-PN-025,Phiếu Nhập,Kiểm tra hiển thị ngày nhập,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra hiển thị ngày nhập trong bảng","Ngày nhập hiển thị đúng định dạng (DD-MM-YYYY), nhất quán giữa các dòng",,,
UI-PN-026,Phiếu Nhập,Kiểm tra hiển thị tổng tiền,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra hiển thị tổng tiền trong bảng","Tổng tiền hiển thị đúng định dạng số, có dấu phân cách hàng nghìn, căn phải và không bị cắt ngắn",,,
UI-PN-035,Phiếu Nhập,Kiểm tra hiển thị khi không có dữ liệu,"1. Xóa tất cả dữ liệu hoặc tìm kiếm với từ khóa không có kết quả
2. Kiểm tra hiển thị","Hệ thống hiển thị thông báo ""Không có dữ liệu"" hoặc bảng trống với thông báo phù hợp",,,
UI-PN-008,Phiếu Nhập,Kiểm tra ô tìm kiếm,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra ô tìm kiếm","Ô tìm kiếm hiển thị với placeholder phù hợp, kích thước đủ lớn để nhập dữ liệu",,,
UI-PN-009,Phiếu Nhập,Kiểm tra nút Tìm kiếm,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nút Tìm kiếm","Nút Tìm kiếm hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-PN-010,Phiếu Nhập,Kiểm tra tìm kiếm với từ khóa hợp lệ,"1. Nhập từ khóa hợp lệ (ví dụ: tên công ty) vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa đã nhập",,,
UI-PN-011,Phiếu Nhập,Kiểm tra tìm kiếm với từ khóa không hợp lệ,"1. Nhập từ khóa không hợp lệ (ví dụ: ""xyz123"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị thông báo không tìm thấy kết quả hoặc danh sách trống",,,
UI-PN-012,Phiếu Nhập,Kiểm tra tìm kiếm với ký tự đặc biệt,"1. Nhập ký tự đặc biệt (ví dụ: ""@#$%"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống xử lý đúng, không bị lỗi và hiển thị kết quả phù hợp hoặc thông báo không tìm thấy",,,
UI-PN-032,Phiếu Nhập,Kiểm tra tìm kiếm theo ngày,"1. Nhập ngày (ví dụ: ""20-05-2023"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả có ngày nhập tương ứng",,,
UI-PN-033,Phiếu Nhập,Kiểm tra tìm kiếm theo khoảng tiền,"1. Nhập khoảng tiền (ví dụ: ""1000000-2000000"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả có tổng tiền trong khoảng đã nhập",,,
UI-PN-018,Phiếu Nhập,Kiểm tra nút phân trang Trước,"1. Nếu có nhiều hơn 10 mục, chuyển đến trang 2
2. Nhấn nút Trước","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-PN-019,Phiếu Nhập,Kiểm tra nút phân trang Sau,"1. Nếu đang ở trang 1 và có nhiều hơn 10 mục
2. Nhấn nút Sau","Hệ thống chuyển đến trang 2 và hiển thị dữ liệu chính xác",,,
UI-PN-020,Phiếu Nhập,Kiểm tra nút phân trang Đầu tiên,"1. Nếu đang ở trang khác trang 1
2. Nhấn nút Đầu tiên (<<)","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-PN-021,Phiếu Nhập,Kiểm tra nút phân trang Cuối cùng,"1. Nếu đang ở trang không phải trang cuối
2. Nhấn nút Cuối cùng (>>)","Hệ thống chuyển đến trang cuối và hiển thị dữ liệu chính xác",,,
UI-PN-022,Phiếu Nhập,Kiểm tra nhấp vào số trang,"1. Nếu có nhiều trang
2. Nhấn vào số trang cụ thể (ví dụ: 3)","Hệ thống chuyển đến trang đã chọn và hiển thị dữ liệu chính xác",,,
UI-PN-027,Phiếu Nhập,Kiểm tra sắp xếp theo ID,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột ID","Dữ liệu được sắp xếp theo ID tăng dần hoặc giảm dần",,,
UI-PN-028,Phiếu Nhập,Kiểm tra sắp xếp theo Mã công ty,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột Mã công ty","Dữ liệu được sắp xếp theo Mã công ty tăng dần hoặc giảm dần",,,
UI-PN-029,Phiếu Nhập,Kiểm tra sắp xếp theo Người nhập,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột Người nhập","Dữ liệu được sắp xếp theo Người nhập tăng dần hoặc giảm dần",,,
UI-PN-030,Phiếu Nhập,Kiểm tra sắp xếp theo Ngày nhập,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột Ngày nhập","Dữ liệu được sắp xếp theo Ngày nhập tăng dần hoặc giảm dần",,,
UI-PN-031,Phiếu Nhập,Kiểm tra sắp xếp theo Tổng tiền,"1. Mở màn hình Phiếu Nhập
2. Nhấp vào tiêu đề cột Tổng tiền","Dữ liệu được sắp xếp theo Tổng tiền tăng dần hoặc giảm dần",,,
UI-PN-013,Phiếu Nhập,Kiểm tra nút Thêm phiếu nhập - hiển thị,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra nút Thêm phiếu nhập","Nút Thêm phiếu nhập hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-PN-014,Phiếu Nhập,Kiểm tra nút Thêm phiếu nhập - chức năng,"1. Nhấn nút Thêm phiếu nhập","Hệ thống chuyển đến màn hình Thêm phiếu nhập",,,
UI-PN-015,Phiếu Nhập,Kiểm tra nút xóa - hiển thị,"1. Mở màn hình Phiếu Nhập
2. Kiểm tra biểu tượng xóa bên cạnh một phiếu nhập","Biểu tượng xóa hiển thị rõ ràng và dễ nhận biết",,,
UI-PN-016,Phiếu Nhập,Kiểm tra nút xóa - chức năng,"1. Nhấn biểu tượng xóa bên cạnh một phiếu nhập","1. Hệ thống hiển thị hộp thoại xác nhận xóa
2. Sau khi xác nhận, phiếu nhập được xóa khỏi danh sách",,,
UI-PN-017,Phiếu Nhập,Kiểm tra hộp thoại xác nhận xóa,"1. Nhấn biểu tượng xóa bên cạnh một phiếu nhập
2. Kiểm tra hộp thoại xác nhận","1. Hộp thoại hiển thị với nội dung rõ ràng
2. Có nút Xác nhận và nút Hủy
3. Nút Hủy hoạt động đúng khi nhấn",,,
UI-PN-034,Phiếu Nhập,Kiểm tra hiển thị chi tiết phiếu nhập,"1. Nhấp vào một dòng phiếu nhập trong bảng","Hệ thống chuyển đến màn hình Chi tiết phiếu nhập tương ứng",,,
UI-TPN-001,Thêm Phiếu Nhập,Kiểm tra tiêu đề trang,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra tiêu đề trang","Tiêu đề ""THÊM PHIẾU NHẬP"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TPN-002,Thêm Phiếu Nhập,Kiểm tra nhãn Người nhập,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nhãn Người nhập","Nhãn ""Người nhập"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TPN-003,Thêm Phiếu Nhập,Kiểm tra nhãn Nhà cung cấp,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nhãn Nhà cung cấp","Nhãn ""Nhà cung cấp"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TPN-004,Thêm Phiếu Nhập,Kiểm tra nhãn Thuốc,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nhãn Thuốc","Nhãn ""Thuốc"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-TPN-005,Thêm Phiếu Nhập,Kiểm tra trường nhập Người nhập,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra trường nhập Người nhập","Trường nhập Người nhập hiển thị đúng, có kích thước phù hợp và có thể nhập dữ liệu",,,
UI-TPN-006,Thêm Phiếu Nhập,Kiểm tra dropdown nhà cung cấp - hiển thị,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra dropdown nhà cung cấp","Dropdown nhà cung cấp hiển thị đúng, có kích thước phù hợp và có biểu tượng dropdown",,,
UI-TPN-007,Thêm Phiếu Nhập,Kiểm tra dropdown nhà cung cấp - chức năng,"1. Nhấn vào dropdown nhà cung cấp","Dropdown hiển thị danh sách nhà cung cấp đầy đủ",,,
UI-TPN-008,Thêm Phiếu Nhập,Kiểm tra chọn nhà cung cấp,"1. Nhấn vào dropdown nhà cung cấp
2. Chọn một nhà cung cấp từ danh sách","Sau khi chọn, giá trị được hiển thị trong ô dropdown",,,
UI-TPN-014,Thêm Phiếu Nhập,Kiểm tra trường Người nhập bắt buộc,"1. Để trống trường Người nhập
2. Điền đầy đủ các trường khác
3. Nhấn nút Thêm","Hệ thống hiển thị thông báo lỗi cho trường Người nhập",,,
UI-TPN-015,Thêm Phiếu Nhập,Kiểm tra trường Nhà cung cấp bắt buộc,"1. Để trống trường Nhà cung cấp
2. Điền đầy đủ các trường khác
3. Nhấn nút Thêm","Hệ thống hiển thị thông báo lỗi cho trường Nhà cung cấp",,,
UI-TPN-009,Thêm Phiếu Nhập,Kiểm tra nút Thêm - hiển thị,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nút Thêm","Nút Thêm hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-TPN-010,Thêm Phiếu Nhập,Kiểm tra nút Thêm - dữ liệu hợp lệ,"1. Điền đầy đủ thông tin hợp lệ vào form
2. Nhấn nút Thêm","1. Hệ thống kiểm tra dữ liệu hợp lệ
2. Phiếu nhập được tạo thành công
3. Hiển thị thông báo thành công",,,
UI-TPN-011,Thêm Phiếu Nhập,Kiểm tra nút Thêm - dữ liệu không hợp lệ,"1. Điền thông tin không hợp lệ vào form (ví dụ: số lượng âm)
2. Nhấn nút Thêm","1. Hệ thống kiểm tra dữ liệu không hợp lệ
2. Hiển thị thông báo lỗi phù hợp
3. Không tạo phiếu nhập mới",,,
UI-TPN-012,Thêm Phiếu Nhập,Kiểm tra nút Hủy - hiển thị,"1. Mở màn hình Thêm Phiếu Nhập
2. Kiểm tra nút Hủy","Nút Hủy hiển thị với nhãn đúng, kích thước phù hợp",,,
UI-TPN-013,Thêm Phiếu Nhập,Kiểm tra nút Hủy - chức năng,"1. Điền một số thông tin vào form
2. Nhấn nút Hủy","1. Hệ thống hiển thị hộp thoại xác nhận hủy (nếu có)
2. Sau khi xác nhận, hệ thống quay lại màn hình Phiếu Nhập
3. Dữ liệu đã nhập không được lưu",,,
UI-TPN-016,Thêm Phiếu Nhập,Kiểm tra thêm thuốc vào phiếu nhập,"1. Nhấn nút thêm thuốc
2. Chọn một loại thuốc từ modal
3. Nhập số lượng và đơn giá","1. Thuốc được thêm vào danh sách
2. Thành tiền được tính đúng (Số lượng x Đơn giá)
3. Tổng tiền được cập nhật",,,
UI-TPN-017,Thêm Phiếu Nhập,Kiểm tra xóa thuốc khỏi phiếu nhập,"1. Thêm ít nhất một thuốc vào phiếu nhập
2. Nhấn nút xóa bên cạnh thuốc đó","1. Thuốc được xóa khỏi danh sách
2. Tổng tiền được cập nhật lại",,,
UI-TPN-018,Thêm Phiếu Nhập,Kiểm tra tính toán thành tiền,"1. Thêm thuốc vào phiếu nhập
2. Nhập số lượng và đơn giá
3. Kiểm tra giá trị thành tiền","Thành tiền được tính đúng (Số lượng x Đơn giá)",,,
UI-TPN-019,Thêm Phiếu Nhập,Kiểm tra tính toán tổng tiền,"1. Thêm nhiều thuốc vào phiếu nhập
2. Kiểm tra giá trị tổng tiền","Tổng tiền bằng tổng các thành tiền của từng thuốc",,,
UI-TPN-020,Thêm Phiếu Nhập,Kiểm tra nhập số lượng âm,"1. Thêm thuốc vào phiếu nhập
2. Nhập số lượng âm
3. Nhấn nút Thêm","Hệ thống hiển thị thông báo lỗi cho trường số lượng",,,
UI-TPN-021,Thêm Phiếu Nhập,Kiểm tra nhập số lượng lớn,"1. Thêm thuốc vào phiếu nhập
2. Nhập số lượng rất lớn (ví dụ: 999999999)
3. Nhấn nút Thêm","Hệ thống xử lý đúng hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-TPN-022,Thêm Phiếu Nhập,Kiểm tra nhập đơn giá lớn,"1. Thêm thuốc vào phiếu nhập
2. Nhập đơn giá rất lớn (ví dụ: 999999999)
3. Nhấn nút Thêm","Hệ thống xử lý đúng hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-TPN-023,Thêm Phiếu Nhập,Kiểm tra nhập số lượng thập phân,"1. Thêm thuốc vào phiếu nhập
2. Nhập số lượng thập phân (ví dụ: 1.5)
3. Nhấn nút Thêm","Hệ thống xử lý đúng hoặc hiển thị thông báo lỗi phù hợp nếu không chấp nhận số thập phân",,,
UI-TPN-024,Thêm Phiếu Nhập,Kiểm tra thêm nhiều thuốc cùng lúc,"1. Thêm nhiều thuốc khác nhau vào phiếu nhập
2. Nhấn nút Thêm","Tất cả thuốc được thêm vào phiếu nhập và tổng tiền được tính đúng",,,
UI-TPN-025,Thêm Phiếu Nhập,Kiểm tra thêm thuốc trùng lặp,"1. Thêm một thuốc vào phiếu nhập
2. Thêm lại cùng thuốc đó
3. Nhấn nút Thêm","Hệ thống xử lý đúng (cộng dồn số lượng hoặc hiển thị thông báo trùng lặp)",,,
UI-CTPN-001,Chi Tiết Phiếu Nhập,Kiểm tra tiêu đề trang,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra tiêu đề trang","Tiêu đề ""CHI TIẾT PHIẾU NHẬP"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-002,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Mã phiếu,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Mã phiếu","Nhãn ""Mã phiếu"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-003,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Số lượng,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Số lượng","Nhãn ""Số lượng"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-004,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Đơn giá,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Đơn giá","Nhãn ""Đơn giá"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-005,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Thành tiền,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Thành tiền","Nhãn ""Thành tiền"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-006,Chi Tiết Phiếu Nhập,Kiểm tra nhãn Ghi chú,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nhãn Ghi chú","Nhãn ""Ghi chú"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-CTPN-007,Chi Tiết Phiếu Nhập,Kiểm tra trường nhập Mã phiếu,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường nhập Mã phiếu","Trường nhập Mã phiếu hiển thị đúng, có kích thước phù hợp và có thể nhập dữ liệu",,,
UI-CTPN-008,Chi Tiết Phiếu Nhập,Kiểm tra trường nhập Số lượng,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường nhập Số lượng","Trường nhập Số lượng hiển thị đúng, có kích thước phù hợp và chỉ chấp nhận giá trị số",,,
UI-CTPN-009,Chi Tiết Phiếu Nhập,Kiểm tra trường nhập Đơn giá,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường nhập Đơn giá","Trường nhập Đơn giá hiển thị đúng, có kích thước phù hợp và chỉ chấp nhận giá trị số",,,
UI-CTPN-010,Chi Tiết Phiếu Nhập,Kiểm tra trường hiển thị Thành tiền,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường hiển thị Thành tiền","Trường hiển thị Thành tiền có định dạng số đúng, có dấu phân cách hàng nghìn",,,
UI-CTPN-011,Chi Tiết Phiếu Nhập,Kiểm tra trường nhập Ghi chú,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra trường nhập Ghi chú","Trường nhập Ghi chú hiển thị đúng, có kích thước phù hợp và có thể nhập nhiều dòng",,,
UI-CTPN-012,Chi Tiết Phiếu Nhập,Kiểm tra giới hạn trường Số lượng,"1. Nhập giá trị âm vào trường Số lượng
2. Nhấn nút Lưu","Hệ thống hiển thị thông báo lỗi cho trường Số lượng",,,
UI-CTPN-013,Chi Tiết Phiếu Nhập,Kiểm tra giới hạn trường Đơn giá,"1. Nhập giá trị âm vào trường Đơn giá
2. Nhấn nút Lưu","Hệ thống hiển thị thông báo lỗi cho trường Đơn giá",,,
UI-CTPN-028,Chi Tiết Phiếu Nhập,Kiểm tra nhập số lượng lớn,"1. Nhập số lượng rất lớn (ví dụ: 999999999) vào trường Số lượng
2. Nhấn nút Lưu","Hệ thống xử lý đúng hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-CTPN-029,Chi Tiết Phiếu Nhập,Kiểm tra nhập đơn giá lớn,"1. Nhập đơn giá rất lớn (ví dụ: 999999999) vào trường Đơn giá
2. Nhấn nút Lưu","Hệ thống xử lý đúng hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-CTPN-030,Chi Tiết Phiếu Nhập,Kiểm tra nhập số lượng thập phân,"1. Nhập số lượng thập phân (ví dụ: 1.5) vào trường Số lượng
2. Nhấn nút Lưu","Hệ thống xử lý đúng hoặc hiển thị thông báo lỗi phù hợp nếu không chấp nhận số thập phân",,,
UI-CTPN-031,Chi Tiết Phiếu Nhập,Kiểm tra nhập đơn giá thập phân,"1. Nhập đơn giá thập phân (ví dụ: 10000.5) vào trường Đơn giá
2. Nhấn nút Lưu","Hệ thống xử lý đúng và hiển thị thành tiền với định dạng số phù hợp",,,
UI-CTPN-034,Chi Tiết Phiếu Nhập,Kiểm tra nhập ghi chú dài,"1. Nhập ghi chú rất dài vào trường Ghi chú
2. Nhấn nút Lưu","Hệ thống xử lý đúng, có thể cắt bớt hoặc hiển thị thông báo giới hạn phù hợp",,,
UI-CTPN-035,Chi Tiết Phiếu Nhập,Kiểm tra nhập ký tự đặc biệt vào ghi chú,"1. Nhập ký tự đặc biệt (ví dụ: @#$%&) vào trường Ghi chú
2. Nhấn nút Lưu","Hệ thống xử lý đúng, lưu và hiển thị ký tự đặc biệt trong ghi chú",,,
UI-CTPN-014,Chi Tiết Phiếu Nhập,Kiểm tra tính toán thành tiền,"1. Nhập số lượng 5 và đơn giá 10000
2. Kiểm tra giá trị thành tiền","Thành tiền hiển thị giá trị 50000 (5 x 10000)",,,
UI-CTPN-015,Chi Tiết Phiếu Nhập,Kiểm tra cập nhật thành tiền,"1. Nhập số lượng 5 và đơn giá 10000
2. Thay đổi số lượng thành 10
3. Kiểm tra giá trị thành tiền","Thành tiền được cập nhật thành 100000 (10 x 10000)",,,
UI-CTPN-016,Chi Tiết Phiếu Nhập,Kiểm tra nút chọn thuốc - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nút chọn thuốc","Nút chọn thuốc hiển thị rõ ràng và dễ nhận biết",,,
UI-CTPN-017,Chi Tiết Phiếu Nhập,Kiểm tra nút chọn thuốc - chức năng,"1. Nhấn nút chọn thuốc","Modal thuốc hiển thị với danh sách thuốc",,,
UI-CTPN-018,Chi Tiết Phiếu Nhập,Kiểm tra chọn thuốc từ modal,"1. Nhấn nút chọn thuốc
2. Chọn một loại thuốc từ modal
3. Nhấn nút Lưu trong modal","1. Modal đóng lại
2. Thông tin thuốc được điền vào form (tên thuốc, mã thuốc)",,,
UI-CTPN-019,Chi Tiết Phiếu Nhập,Kiểm tra nút Lưu - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nút Lưu","Nút Lưu hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-CTPN-020,Chi Tiết Phiếu Nhập,Kiểm tra nút Lưu - dữ liệu hợp lệ,"1. Điền đầy đủ thông tin hợp lệ vào form
2. Nhấn nút Lưu","1. Hệ thống kiểm tra dữ liệu hợp lệ
2. Chi tiết phiếu nhập được lưu thành công
3. Hiển thị thông báo thành công",,,
UI-CTPN-021,Chi Tiết Phiếu Nhập,Kiểm tra nút Lưu - dữ liệu không hợp lệ,"1. Điền thông tin không hợp lệ vào form (ví dụ: số lượng âm)
2. Nhấn nút Lưu","1. Hệ thống kiểm tra dữ liệu không hợp lệ
2. Hiển thị thông báo lỗi phù hợp
3. Không lưu chi tiết phiếu nhập",,,
UI-CTPN-022,Chi Tiết Phiếu Nhập,Kiểm tra nút Hủy - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nút Hủy","Nút Hủy hiển thị với nhãn đúng, kích thước phù hợp",,,
UI-CTPN-023,Chi Tiết Phiếu Nhập,Kiểm tra nút Hủy - chức năng,"1. Điền một số thông tin vào form
2. Nhấn nút Hủy","1. Hệ thống hiển thị hộp thoại xác nhận hủy
2. Sau khi xác nhận, hệ thống quay lại màn hình trước đó
3. Dữ liệu đã nhập không được lưu",,,
UI-CTPN-024,Chi Tiết Phiếu Nhập,Kiểm tra nút thêm dòng chi tiết - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập
2. Kiểm tra nút thêm dòng chi tiết","Nút thêm dòng chi tiết hiển thị rõ ràng và dễ nhận biết",,,
UI-CTPN-025,Chi Tiết Phiếu Nhập,Kiểm tra nút thêm dòng chi tiết - chức năng,"1. Nhập thông tin cho một dòng chi tiết
2. Nhấn nút thêm dòng chi tiết mới","1. Dòng chi tiết hiện tại được lưu
2. Một dòng chi tiết mới trống được thêm vào form",,,
UI-CTPN-026,Chi Tiết Phiếu Nhập,Kiểm tra nút xóa dòng chi tiết - hiển thị,"1. Mở màn hình Chi Tiết Phiếu Nhập với ít nhất một dòng chi tiết
2. Kiểm tra nút xóa bên cạnh dòng chi tiết","Nút xóa hiển thị rõ ràng và dễ nhận biết",,,
UI-CTPN-027,Chi Tiết Phiếu Nhập,Kiểm tra nút xóa dòng chi tiết - chức năng,"1. Nhấn nút xóa bên cạnh một dòng chi tiết","1. Hệ thống hiển thị hộp thoại xác nhận xóa
2. Sau khi xác nhận, dòng chi tiết được xóa khỏi form
3. Tổng tiền được cập nhật lại",,,
UI-CTPN-032,Chi Tiết Phiếu Nhập,Kiểm tra thêm nhiều dòng chi tiết,"1. Thêm nhiều dòng chi tiết khác nhau
2. Nhấn nút Lưu","Tất cả dòng chi tiết được lưu và tổng tiền được tính đúng",,,
UI-CTPN-033,Chi Tiết Phiếu Nhập,Kiểm tra thêm dòng chi tiết trùng lặp,"1. Thêm một dòng chi tiết
2. Thêm lại dòng chi tiết với cùng thuốc
3. Nhấn nút Lưu","Hệ thống xử lý đúng (cộng dồn số lượng hoặc hiển thị thông báo trùng lặp)",,,
UI-MT-001,Modal Thuốc,Kiểm tra tiêu đề modal,"1. Mở modal Thuốc
2. Kiểm tra tiêu đề modal","Tiêu đề ""Thuốc"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-002,Modal Thuốc,Kiểm tra nhãn cột STT,"1. Mở modal Thuốc
2. Kiểm tra nhãn cột STT","Nhãn ""STT"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-003,Modal Thuốc,Kiểm tra nhãn cột Tên,"1. Mở modal Thuốc
2. Kiểm tra nhãn cột Tên","Nhãn ""Tên"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-004,Modal Thuốc,Kiểm tra nhãn cột Mã thuốc,"1. Mở modal Thuốc
2. Kiểm tra nhãn cột Mã thuốc","Nhãn ""Mã thuốc"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-005,Modal Thuốc,Kiểm tra nhãn cột Số lượng tồn,"1. Mở modal Thuốc
2. Kiểm tra nhãn cột Số lượng tồn","Nhãn ""Số lượng tồn"" hiển thị đúng chính tả, font chữ và kích thước phù hợp",,,
UI-MT-006,Modal Thuốc,Kiểm tra căn chỉnh các cột,"1. Mở modal Thuốc
2. Kiểm tra căn chỉnh của các cột","Các cột được căn chỉnh hợp lý (STT căn giữa, Tên căn trái, Mã thuốc căn trái, Số lượng tồn căn phải)",,,
UI-MT-024,Modal Thuốc,Kiểm tra hiển thị tên thuốc,"1. Mở modal Thuốc
2. Kiểm tra hiển thị tên thuốc trong bảng","Tên thuốc hiển thị đúng, không bị cắt ngắn hoặc tràn ra ngoài cột",,,
UI-MT-025,Modal Thuốc,Kiểm tra hiển thị mã thuốc,"1. Mở modal Thuốc
2. Kiểm tra hiển thị mã thuốc trong bảng","Mã thuốc hiển thị đúng định dạng, không bị cắt ngắn",,,
UI-MT-026,Modal Thuốc,Kiểm tra hiển thị số lượng tồn,"1. Mở modal Thuốc
2. Kiểm tra hiển thị số lượng tồn trong bảng","Số lượng tồn hiển thị đúng định dạng số, căn phải và không bị cắt ngắn",,,
UI-MT-036,Modal Thuốc,Kiểm tra hiển thị khi không có dữ liệu,"1. Tìm kiếm với từ khóa không có kết quả
2. Kiểm tra hiển thị","Hệ thống hiển thị thông báo ""Không có dữ liệu"" hoặc bảng trống với thông báo phù hợp",,,
UI-MT-007,Modal Thuốc,Kiểm tra ô tìm kiếm,"1. Mở modal Thuốc
2. Kiểm tra ô tìm kiếm","Ô tìm kiếm hiển thị với placeholder phù hợp, kích thước đủ lớn để nhập dữ liệu",,,
UI-MT-008,Modal Thuốc,Kiểm tra nút Tìm kiếm,"1. Mở modal Thuốc
2. Kiểm tra nút Tìm kiếm","Nút Tìm kiếm hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-MT-009,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa hợp lệ,"1. Nhập từ khóa hợp lệ (ví dụ: ""Amoxicillin"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa ""Amoxicillin""",,,
UI-MT-010,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa không hợp lệ,"1. Nhập từ khóa không hợp lệ (ví dụ: ""xyz123"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị thông báo không tìm thấy kết quả hoặc danh sách trống",,,
UI-MT-027,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa tiếng Việt có dấu,"1. Nhập từ khóa tiếng Việt có dấu (ví dụ: ""Thuốc đau đầu"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa từ khóa tiếng Việt có dấu",,,
UI-MT-028,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa tiếng Việt không dấu,"1. Nhập từ khóa tiếng Việt không dấu (ví dụ: ""Thuoc dau dau"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả phù hợp, có thể tìm được các từ có dấu tương ứng",,,
UI-MT-029,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa viết hoa,"1. Nhập từ khóa viết hoa (ví dụ: ""AMOXICILLIN"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả không phân biệt chữ hoa/thường",,,
UI-MT-030,Modal Thuốc,Kiểm tra tìm kiếm với từ khóa một phần,"1. Nhập một phần của từ khóa (ví dụ: ""amox"" thay vì ""amoxicillin"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả chứa phần từ khóa đã nhập",,,
UI-MT-031,Modal Thuốc,Kiểm tra tìm kiếm với mã thuốc,"1. Nhập mã thuốc (ví dụ: ""T001"") vào ô tìm kiếm
2. Nhấn nút Tìm kiếm","Hệ thống hiển thị kết quả có mã thuốc tương ứng",,,
UI-MT-019,Modal Thuốc,Kiểm tra nút phân trang Trước,"1. Nếu có nhiều hơn 10 mục, chuyển đến trang 2
2. Nhấn nút Trước","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-MT-020,Modal Thuốc,Kiểm tra nút phân trang Sau,"1. Nếu đang ở trang 1 và có nhiều hơn 10 mục
2. Nhấn nút Sau","Hệ thống chuyển đến trang 2 và hiển thị dữ liệu chính xác",,,
UI-MT-021,Modal Thuốc,Kiểm tra nút phân trang Đầu tiên,"1. Nếu đang ở trang khác trang 1
2. Nhấn nút Đầu tiên (<<)","Hệ thống chuyển về trang 1 và hiển thị dữ liệu chính xác",,,
UI-MT-022,Modal Thuốc,Kiểm tra nút phân trang Cuối cùng,"1. Nếu đang ở trang không phải trang cuối
2. Nhấn nút Cuối cùng (>>)","Hệ thống chuyển đến trang cuối và hiển thị dữ liệu chính xác",,,
UI-MT-023,Modal Thuốc,Kiểm tra nhấp vào số trang,"1. Nếu có nhiều trang
2. Nhấn vào số trang cụ thể (ví dụ: 3)","Hệ thống chuyển đến trang đã chọn và hiển thị dữ liệu chính xác",,,
UI-MT-032,Modal Thuốc,Kiểm tra sắp xếp theo STT,"1. Mở modal Thuốc
2. Nhấp vào tiêu đề cột STT","Dữ liệu được sắp xếp theo STT tăng dần hoặc giảm dần",,,
UI-MT-033,Modal Thuốc,Kiểm tra sắp xếp theo Tên,"1. Mở modal Thuốc
2. Nhấp vào tiêu đề cột Tên","Dữ liệu được sắp xếp theo Tên tăng dần hoặc giảm dần",,,
UI-MT-034,Modal Thuốc,Kiểm tra sắp xếp theo Mã thuốc,"1. Mở modal Thuốc
2. Nhấp vào tiêu đề cột Mã thuốc","Dữ liệu được sắp xếp theo Mã thuốc tăng dần hoặc giảm dần",,,
UI-MT-035,Modal Thuốc,Kiểm tra sắp xếp theo Số lượng tồn,"1. Mở modal Thuốc
2. Nhấp vào tiêu đề cột Số lượng tồn","Dữ liệu được sắp xếp theo Số lượng tồn tăng dần hoặc giảm dần",,,
UI-MT-011,Modal Thuốc,Kiểm tra chọn thuốc - checkbox,"1. Mở modal Thuốc
2. Nhấn vào checkbox bên cạnh một loại thuốc","Checkbox được chọn và hàng tương ứng được đánh dấu",,,
UI-MT-012,Modal Thuốc,Kiểm tra chọn nhiều thuốc,"1. Mở modal Thuốc
2. Nhấn vào checkbox bên cạnh nhiều loại thuốc","Nhiều checkbox được chọn và các hàng tương ứng được đánh dấu",,,
UI-MT-037,Modal Thuốc,Kiểm tra chọn nhiều trang,"1. Chọn một thuốc ở trang 1
2. Chuyển sang trang 2
3. Chọn một thuốc ở trang 2
4. Nhấn nút Lưu","Hệ thống xử lý đúng, lưu tất cả các thuốc đã chọn từ các trang khác nhau",,,
UI-MT-038,Modal Thuốc,Kiểm tra bỏ chọn thuốc,"1. Chọn một thuốc
2. Nhấp lại vào checkbox để bỏ chọn
3. Nhấn nút Lưu","Thuốc đã bỏ chọn không được lưu",,,
UI-MT-039,Modal Thuốc,Kiểm tra chọn tất cả,"1. Nếu có nút Chọn tất cả, nhấp vào nút đó
2. Kiểm tra trạng thái checkbox","Tất cả checkbox được chọn",,,
UI-MT-040,Modal Thuốc,Kiểm tra bỏ chọn tất cả,"1. Chọn tất cả thuốc
2. Nếu có nút Bỏ chọn tất cả, nhấp vào nút đó
3. Kiểm tra trạng thái checkbox","Tất cả checkbox được bỏ chọn",,,
UI-MT-013,Modal Thuốc,Kiểm tra nút Lưu - hiển thị,"1. Mở modal Thuốc
2. Kiểm tra nút Lưu","Nút Lưu hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-MT-014,Modal Thuốc,Kiểm tra nút Lưu - chức năng,"1. Chọn một loại thuốc từ danh sách
2. Nhấn nút Lưu","1. Modal đóng lại
2. Thông tin thuốc được chọn được truyền về form chính",,,
UI-MT-015,Modal Thuốc,Kiểm tra nút Hủy - hiển thị,"1. Mở modal Thuốc
2. Kiểm tra nút Hủy","Nút Hủy hiển thị với nhãn đúng, kích thước phù hợp",,,
UI-MT-016,Modal Thuốc,Kiểm tra nút Hủy - chức năng,"1. Chọn một loại thuốc từ danh sách
2. Nhấn nút Hủy","1. Modal đóng lại
2. Không có thay đổi nào được áp dụng vào form chính",,,
UI-MT-017,Modal Thuốc,Kiểm tra nút Thêm thuốc - hiển thị,"1. Mở modal Thuốc
2. Kiểm tra nút Thêm thuốc","Nút Thêm thuốc hiển thị với nhãn đúng, kích thước phù hợp và màu sắc nổi bật",,,
UI-MT-018,Modal Thuốc,Kiểm tra nút Thêm thuốc - chức năng,"1. Nhấn nút Thêm thuốc","Hệ thống chuyển đến form thêm thuốc mới",,,
