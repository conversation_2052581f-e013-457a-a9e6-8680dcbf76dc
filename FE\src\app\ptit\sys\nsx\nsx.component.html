<div class="main-content">
  <div class="page-content">
    <div class="container-fluid">
      <div
        *ngIf="displayDialog"
        id="layer-popup"
        [ngClass]="{ active: displayDialog }"
      >
        <app-nsx-createment
          (cancel)="handleCancel($event)"
          [nsx]="nsxNew"
          (save)="handeSave($event)"
        >
        </app-nsx-createment>
      </div>

      <app-confirm-dialog-common> </app-confirm-dialog-common>

      <!-- start page title -->
      <div class="row">
        <div class="col-12">
          <div
            class="page-title-box d-sm-flex align-items-center justify-content-between"
          >
            <h4 class="mb-sm-0">Nhà Sản Xuất</h4>
          </div>
        </div>
      </div>
      <!-- end page title -->

      <!-- search -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div
              class="card-header border-0 align-items-center justify-content-end"
            >
              <div
                class="align-items-center p-3 justify-content-between d-flex"
              >
                <div class="d-flex flex-column col-xl-9">
                  <label for="titleDropdown">Tên Nhà Sản Xuất </label>
                  <input
                    type="text"
                    pInputText
                    [(ngModel)]="modelSearch.keyWord"
                  />
                </div>

                <div class="d-flex align-items-end col-xl-2">
                  <button
                    type="button"
                    class="btn btn-info shadow-none mt-3"
                    (click)="search()"
                  >
                    <i class="ri-search-line align-middle me-1"></i>
                    Tìm Kiếm
                  </button>
                </div>
              </div>

              <!-- <div class="col-auto mb-3"></div> -->
            </div>
          </div>
        </div>
      </div>

      <!-- content   -->
      <div class="row">
        <div class="col-xl-12">
          <div class="card">
            <div
              class="card-header border-0 align-items-center justify-content-end"
            >
              <div
                class="align-items-center p-3 justify-content-between d-flex"
              >
                <div class="flex-shrink-0">
                  <div class="text-muted">
                    <span class="fw-semibold">Total: </span>
                    <span class="fw-semibold">{{ nsxLst.length }}</span>
                  </div>
                </div>
                <button
                  type="button"
                  class="btn btn-success shadow-none"
                  (click)="preAdd()"
                >
                  <i class="ri-add-circle-line align-middle me-1"></i>
                  Thêm nhà sản xuất
                </button>
              </div>
              <div class="col-auto mb-3"></div>

              <div class="col-auto">
                <p-table
                  [value]="nsxLst"
                  [rowHover]="true"
                  dataKey="id"
                  [showCurrentPageReport]="true"
                  [rows]="10"
                  [alwaysShowPaginator]="true"
                  [paginator]="true"
                  currentPageReportTemplate=""
                >
                  <ng-template pTemplate="header">
                    <tr>
                      <th>ID</th>
                      <th>Mã nhà sản xuất</th>
                      <th>Tên nhà sản xuất</th>
                      <th>Địa chỉ</th>
                      <th>Số Điện Thoại</th>
                      <th>Email</th>
                      <th></th>
                    </tr>
                  </ng-template>
                  <ng-template pTemplate="body" let-nsx let-rowIndex="rowIndex">
                    <tr [style]="{ cursor: 'pointer' }">
                      <td (click)="preUpdate(nsx)">{{ rowIndex + 1 }}</td>
                      <td (click)="preUpdate(nsx)">{{ nsx.maNSX }}</td>
                      <td (click)="preUpdate(nsx)">{{ nsx.tenNhaSanXuat }}</td>
                      <td (click)="preUpdate(nsx)">{{ nsx.diaChi }}</td>
                      <td (click)="preUpdate(nsx)">{{ nsx.soDienThoai }}</td>
                      <td (click)="preUpdate(nsx)">{{ nsx.email }}</td>
                      <td>
                        <a
                          class="text-danger d-inline-block remove-item-btn"
                          data-bs-toggle="modal"
                          href="#delete"
                          (click)="preDelete(nsx)"
                        >
                          <i class="ri-delete-bin-line fs-2"></i>
                        </a>
                      </td>
                    </tr>
                  </ng-template>
                </p-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
